<?php

use Hyperf\Database\Schema\Schema;
use Hyperf\Database\Schema\Blueprint;
use Hyperf\Database\Migrations\Migration;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('copy_spot_expert_statistics', function (Blueprint $table) {
            $table->bigIncrements('id')->comment('主键ID');
            $table->bigInteger('expert_id')->unsigned()->comment('专家ID');
            $table->bigInteger('expert_user_id')->unsigned()->comment('专家用户ID（冗余字段）');

            // 收益金额
            $table->decimal('profit', 20, 8)->default(0)->comment('收益金额');
            $table->decimal('profit_7d', 20, 8)->default(0)->comment('7日收益金额');
            $table->decimal('profit_30d', 20, 8)->default(0)->comment('30日收益金额');
            $table->decimal('profit_90d', 20, 8)->default(0)->comment('90日收益金额');
            $table->decimal('profit_180d', 20, 8)->default(0)->comment('180日收益金额');

            // 盈利订单数（仓位单位）、7日盈利订单数（仓位单位）、30日盈利订单数（仓位单位）、90日盈利订单数（仓位单位）、180日盈利订单数（仓位单位）
            $table->integer('profit_order_count')->default(0)->comment('盈利订单数');
            $table->integer('profit_order_count_7d')->default(0)->comment('7日盈利订单数');
            $table->integer('profit_order_count_30d')->default(0)->comment('30日盈利订单数');
            $table->integer('profit_order_count_90d')->default(0)->comment('90日盈利订单数');
            $table->integer('profit_order_count_180d')->default(0)->comment('180日盈利订单数');

            // 亏损订单数（仓位单位）、7日亏损订单数（仓位单位）、30日亏损订单数（仓位单位）、90日亏损订单数（仓位单位）、180日亏损订单数（仓位单位）
            $table->integer('loss_order_count')->default(0)->comment('亏损订单数');
            $table->integer('loss_order_count_7d')->default(0)->comment('7日亏损订单数');
            $table->integer('loss_order_count_30d')->default(0)->comment('30日亏损订单数');
            $table->integer('loss_order_count_90d')->default(0)->comment('90日亏损订单数');
            $table->integer('loss_order_count_180d')->default(0)->comment('180日亏损订单数');

            // 胜率
            $table->decimal('win_rate', 8, 2)->default(0)->comment('胜率 %');
            $table->decimal('win_rate_7d', 8, 2)->default(0)->comment('7日胜率 %');
            $table->decimal('win_rate_30d', 8, 2)->default(0)->comment('30日胜率 %');
            $table->decimal('win_rate_90d', 8, 2)->default(0)->comment('90日胜率 %');
            $table->decimal('win_rate_180d', 8, 2)->default(0)->comment('180日胜率 %');

            // 收益率
            $table->decimal('profit_rate', 8, 2)->default(0)->comment('收益率 %');
            $table->decimal('profit_rate_7d', 8, 2)->default(0)->comment('7日收益率 %');
            $table->decimal('profit_rate_30d', 8, 2)->default(0)->comment('30日收益率 %');
            $table->decimal('profit_rate_90d', 8, 2)->default(0)->comment('90日收益率 %');
            $table->decimal('profit_rate_180d', 8, 2)->default(0)->comment('180日收益率 %');

            // 跟单者收益
            $table->decimal('follower_profit', 20, 8)->default(0)->comment('跟单者收益');
            $table->decimal('follower_profit_7d', 20, 8)->default(0)->comment('7日跟单者收益');
            $table->decimal('follower_profit_30d', 20, 8)->default(0)->comment('30日跟单者收益');
            $table->decimal('follower_profit_90d', 20, 8)->default(0)->comment('90日跟单者收益');
            $table->decimal('follower_profit_180d', 20, 8)->default(0)->comment('180日跟单者收益');

            // 资产管理规模
            $table->decimal('aum', 20, 8)->default(0)->comment('资产管理规模');

            // 交易频率
            $table->integer('trade_frequency')->default(0)->comment('交易频率');
            $table->integer('trade_frequency_7d')->default(0)->comment('7日交易频率');
            $table->integer('trade_frequency_30d')->default(0)->comment('30日交易频率');
            $table->integer('trade_frequency_90d')->default(0)->comment('90日交易频率');
            $table->integer('trade_frequency_180d')->default(0)->comment('180日交易频率');

            // 累计跟单人数
            $table->integer('total_follower_count')->default(0)->comment('累计跟单人数');

            $table->timestamps();

            // 索引
            $table->index('expert_id', 'idx_expert');
            $table->index('expert_user_id', 'idx_expert_user_id');

            $table->comment('现货交易专家统计表');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('copy_spot_expert_statistics');
    }
};
