# 跟单模块开发文档（修正版）

## 1. 需求概述

跟单模块是一个完整的交易专家带单和用户跟单系统，支持现货和合约两种交易类型。系统包括交易专家申请、等级管理、数据统计、跟单模式选择等核心功能。

### 1.1 核心功能模块

- **交易专家申请与管理**：支持合约和现货交易专家申请
- **个人设置管理**：专家个性化配置和隐私设置
- **等级系统**：基于业绩的等级升降
- **跟单模式**：智能比例跟单和多元探索跟单
- **尊享模式**：白名单邀请的私享带单环境
- **数据统计与展示**：全面的带单和分润数据分析
- **关注与反馈**：用户关注和问题反馈机制

## 2. 数据库设计

### 2.1 cpx_user 表扩展

现有 `cpx_user` 表中已存在 `display_name` 字段，新增关联字段：

```sql
ALTER TABLE `cpx_user`
ADD COLUMN `contract_expert_id` bigint NULL COMMENT '合约交易专家ID',
ADD COLUMN `spot_expert_id` bigint NULL COMMENT '现货交易专家ID',
```

### 2.2 合约交易专家表（申请+设置合并）

```sql
CREATE TABLE `copy_contract_expert` (
    `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `user_id` bigint unsigned NOT NULL COMMENT '用户ID',
    `introduction` text NOT NULL COMMENT '个人介绍',
    `transfer_from_account` tinyint NOT NULL COMMENT '划转资金来源账户，枚举：\App\Model\Enums\User\AccountType::class',
    `transfer_amount` decimal(20,8) NOT NULL COMMENT '划转金额（USDT）',
    `status` tinyint NOT NULL DEFAULT '1' COMMENT '申请状态：1-待审核，2-审核通过，3-审核拒绝',
    `review_remark` varchar(500) NULL COMMENT '审核备注',
    `reviewed_at` timestamp NULL COMMENT '审核时间',
    `reviewed_by` bigint unsigned NULL COMMENT '审核人ID',
    `is_active` tinyint NOT NULL DEFAULT '1' COMMENT '是否开启带单：1-是，0-否',
    `show_total_assets` tinyint NOT NULL DEFAULT '0' COMMENT '是否展示总资产：1-是，0-否',
    `show_expert_rating` tinyint NOT NULL DEFAULT '0' COMMENT '是否展示专家评分及排名：1-是，0-否',
    `position_protection` tinyint NOT NULL DEFAULT '1' COMMENT '未结仓位保护：1-开启，0-关闭',
    `min_follow_amount` decimal(20,8) NULL COMMENT '最小跟单金额（USDT）',
    `recommend_params` json NULL COMMENT '推荐参数配置',
    `currency_ids` json NULL COMMENT '跟单币种 id 配置（支持多币种）',
    `profit_sharing_rate` decimal(8,2) NOT NULL DEFAULT '0.0000' COMMENT '分润比例 %',
    `level_id` bigint unsigned NOT NULL DEFAULT '1' COMMENT '专家等级ID',
    `rating` decimal(8,2) NULL COMMENT '专家评分',
    `exclusive_mode` tinyint NOT NULL DEFAULT '0' COMMENT '尊享模式：1-开启，0-关闭',
    `created_at` timestamp NULL COMMENT '创建时间',
    `updated_at` timestamp NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_user_id` (`user_id`),
    INDEX `idx_status` (`status`),
    INDEX `idx_is_active` (`is_active`),
    INDEX `idx_level_id` (`level_id`),
    INDEX `idx_exclusive_mode` (`exclusive_mode`)
) COMMENT='合约交易专家表';
```

### 2.3 现货交易专家表（申请+设置合并）

```sql
CREATE TABLE `copy_spot_expert` (
    `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `user_id` bigint unsigned NOT NULL COMMENT '用户ID',
    `introduction` text NOT NULL COMMENT '个人介绍',
    `status` tinyint NOT NULL DEFAULT '1' COMMENT '申请状态：1-待审核，2-审核通过，3-审核拒绝',
    `review_remark` varchar(500) NULL COMMENT '审核备注',
    `reviewed_at` timestamp NULL COMMENT '审核时间',
    `reviewed_by` bigint unsigned NULL COMMENT '审核人ID',
    `is_active` tinyint NOT NULL DEFAULT '0' COMMENT '是否开启现货带单：1-是，0-否',
    `show_total_assets` tinyint NOT NULL DEFAULT '0' COMMENT '是否展示总资产：1-是，0-否',
    `show_fund_composition` tinyint NOT NULL DEFAULT '0' COMMENT '是否展示资金构成：1-是，0-否',
    `new_currency_auto_copy` tinyint NOT NULL DEFAULT '0' COMMENT '新上线的交易对自动开启带单：1-是，0-否',
    `position_protection` tinyint NOT NULL DEFAULT '1' COMMENT '未结仓位保护：1-开启，0-关闭',
    `min_follow_amount` decimal(20,8) NULL COMMENT '最小跟单金额（USDT）',
    `recommend_params` json NULL COMMENT '推荐参数配置',
    `currency_ids` json NULL COMMENT '跟单币种 id 配置（支持多币种）',
    `profit_sharing_rate` decimal(8,2) NOT NULL DEFAULT '0.0000' COMMENT '分润比例 %',
    `level_id` bigint unsigned NOT NULL DEFAULT '1' COMMENT '专家等级ID',
    `exclusive_mode` tinyint NOT NULL DEFAULT '0' COMMENT '尊享模式：1-开启，0-关闭',
    `created_at` timestamp NULL COMMENT '创建时间',
    `updated_at` timestamp NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_user_id` (`user_id`),
    INDEX `idx_status` (`status`),
    INDEX `idx_is_active` (`is_active`),
    INDEX `idx_level_id` (`level_id`),
    INDEX `idx_exclusive_mode` (`exclusive_mode`)
) COMMENT='现货交易专家表';
```

**推荐参数（recommend_params） JSON 格式：**

```json
{
  "fixed_amount": {
    "amount": "100.00",
    "stop_loss_rate": "10.00",
    "take_profit_rate": "20.00",
    "max_copy_amount": "1000.00"
  },
  "multiplier": {
    "multiplier": "0.1",
    "stop_loss_rate": "10.00",
    "take_profit_rate": "20.00",
    "max_copy_amount": "1000.00"
  }
}
```

### 2.4 交易专家等级表（新增等级图标）

```sql
CREATE TABLE `copy_expert_level` (
    `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `type` tinyint NOT NULL COMMENT '类型：1-合约，2-现货',
    `level` int NOT NULL COMMENT '等级',
    `name` json NOT NULL COMMENT '等级名称（支持多语言）',
    `icon` varchar(255) NULL COMMENT '等级图标',
    `condition_amount` decimal(20,8) NOT NULL COMMENT '条件一：带单金额（USDT）',
    `condition_follow_amount` decimal(20,8) NULL COMMENT '条件二：跟单者总跟单资金（USDT）',
    `condition_follow_count` int NULL COMMENT '条件二：跟单交易人数（人）',
    `max_follow_count` int NOT NULL COMMENT '最大可带单人数',
    `max_profit_rate` decimal(8,2) NOT NULL COMMENT '最大分润比例 %',
    `created_at` timestamp NULL COMMENT '创建时间',
    `updated_at` timestamp NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_type_level` (`type`, `level`)
) COMMENT='交易专家等级表';
```

### 2.5 关注表

```sql
CREATE TABLE `copy_follow` (
    `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `user_id` bigint unsigned NOT NULL COMMENT '关注者用户ID',
    `expert_id` bigint unsigned NOT NULL COMMENT '专家ID',
    `expert_type` varchar(255) NOT NULL COMMENT '专家模型类名（多态关联）',
    `expert_user_id` bigint unsigned NOT NULL COMMENT '专家用户ID（冗余字段）',
    `type` tinyint NOT NULL COMMENT '专家类型：1-合约，2-现货',
    `created_at` timestamp NULL COMMENT '创建时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_user_expert` (`user_id`, `expert_id`, `type`),
    INDEX `idx_expert` (`expert_id`, `expert_type`)
) COMMENT='关注表';
```

### 2.6 问题反馈表（合并问题反馈和身份撤销）

```sql
CREATE TABLE `copy_feedback` (
    `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `expert_id` bigint unsigned NOT NULL COMMENT '专家ID',
    `expert_type` varchar(255) NOT NULL COMMENT '专家模型类名（多态关联）',
    `expert_user_id` bigint unsigned NOT NULL COMMENT '专家用户ID（冗余字段）',
    `type` tinyint NOT NULL COMMENT '专家类型：1-合约，2-现货',
    `feedback_type` tinyint NOT NULL COMMENT '反馈类型：1-问题反馈，2-身份撤销',
    `problem_type` varchar(50) NOT NULL COMMENT '问题类型',
    `content` text NOT NULL COMMENT '反馈内容',
    `refund_account_type` tinyint NULL COMMENT '资金退回账户类型：1-现货账户，2-合约账户（仅合约专家撤销）',
    `refund_amount` decimal(20,8) NULL COMMENT '退回金额（仅身份撤销）',
    `created_at` timestamp NULL COMMENT '创建时间',
    `updated_at` timestamp NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    INDEX `idx_expert` (`expert_id`, `expert_type`)
) COMMENT='问题反馈表';
```

### 2.7 合约跟单记录表

```sql
CREATE TABLE `copy_contract_order` (
    `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `follower_user_id` bigint unsigned NOT NULL COMMENT '跟单者用户ID',
    `expert_id` bigint unsigned NOT NULL COMMENT '专家ID',
    `expert_user_id` bigint unsigned NOT NULL COMMENT '专家用户ID（冗余字段）',
    `mode` tinyint NOT NULL COMMENT '跟单模式：1-智能比例，2-多元探索',
    `expert_order_id` bigint unsigned NOT NULL COMMENT '专家订单ID（关联trade_perpetual_order）',
    `expert_position_id` bigint unsigned NOT NULL COMMENT '专家仓位ID（关联trade_perpetual_position）',
    `follower_order_id` bigint unsigned NOT NULL COMMENT '跟单者订单ID（关联trade_perpetual_order）',
    `follower_position_id` bigint unsigned NOT NULL COMMENT '跟单者仓位ID（关联trade_perpetual_position）',
    `profit_sharing_rate` decimal(8,2) NOT NULL COMMENT '分润比例 %',
    `is_exclusive` tinyint NOT NULL DEFAULT '0' COMMENT '是否尊享模式：1-是，0-否',
    `copy_settings_snapshot` json NOT NULL COMMENT '跟单配置参数快照',
    `created_at` timestamp NULL COMMENT '创建时间',
    `updated_at` timestamp NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    INDEX `idx_follower_user_id` (`follower_user_id`),
    INDEX `idx_expert` (`expert_id`),
    INDEX `idx_expert_user_id` (`expert_user_id`),
    INDEX `idx_expert_order_id` (`expert_order_id`, `expert_position_id`),
    INDEX `idx_follower_order_id` (`follower_order_id`, `follower_position_id`)
) COMMENT='合约跟单记录表';
```

### 2.8 合约跟单仓位记录表

```sql
CREATE TABLE `copy_contract_position` (
    `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `follower_user_id` bigint unsigned NOT NULL COMMENT '跟单者用户ID',
    `expert_id` bigint unsigned NOT NULL COMMENT '专家ID',
    `expert_user_id` bigint unsigned NOT NULL COMMENT '专家用户ID（冗余字段）',
    `expert_position_id` bigint unsigned NOT NULL COMMENT '专家仓位ID（关联trade_perpetual_position）',
    `follower_position_id` bigint unsigned NOT NULL COMMENT '跟单者仓位ID（关联trade_perpetual_position）',
    `profit_sharing_rate` decimal(8,2) NOT NULL COMMENT '分润比例 %',
    `is_exclusive` tinyint NOT NULL DEFAULT '0' COMMENT '是否尊享模式：1-是，0-否',
    `copy_settings_snapshot` json NOT NULL COMMENT '跟单配置参数快照',
    `created_at` timestamp NULL COMMENT '创建时间',
    `updated_at` timestamp NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    INDEX `idx_follower_user_id` (`follower_user_id`),
    INDEX `idx_expert` (`expert_id`),
    INDEX `idx_expert_user_id` (`expert_user_id`),
    INDEX `idx_expert_position_id` (`expert_position_id`),
    INDEX `idx_follower_position_id` (`follower_position_id`)
) COMMENT='合约跟单仓位记录表';
```

### 2.9 现货跟单记录表

```sql
CREATE TABLE `copy_spot_order` (
    `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `follower_user_id` bigint unsigned NOT NULL COMMENT '跟单者用户ID',
    `expert_id` bigint unsigned NOT NULL COMMENT '专家ID',
    `expert_user_id` bigint unsigned NOT NULL COMMENT '专家用户ID（冗余字段）',
    `mode` tinyint NOT NULL COMMENT '跟单模式：1-智能比例，2-多元探索',
    `expert_order_id` bigint unsigned NOT NULL COMMENT '专家订单ID（关联trade_spot_order）',
    `follower_order_id` bigint unsigned NOT NULL COMMENT '跟单者订单ID（关联trade_spot_order）',
    `profit_sharing_rate` decimal(8,2) NOT NULL COMMENT '分润比例 %',
    `is_exclusive` tinyint NOT NULL DEFAULT '0' COMMENT '是否尊享模式：1-是，0-否',
    `copy_settings_snapshot` json NOT NULL COMMENT '跟单配置参数快照',
    `created_at` timestamp NULL COMMENT '创建时间',
    `updated_at` timestamp NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    INDEX `idx_follower_user_id` (`follower_user_id`),
    INDEX `idx_expert` (`expert_id`),
    INDEX `idx_expert_user_id` (`expert_user_id`),
    INDEX `idx_expert_order_id` (`expert_order_id`),
    INDEX `idx_follower_order_id` (`follower_order_id`)
) COMMENT='现货跟单记录表';
```

### 2.10 合约跟单配置表

```sql
CREATE TABLE `copy_contract_user_setting` (
    `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `follower_user_id` bigint unsigned NOT NULL COMMENT '跟单者用户ID',
    `expert_id` bigint unsigned NOT NULL COMMENT '专家ID',
    `expert_user_id` bigint unsigned NOT NULL COMMENT '专家用户ID（冗余字段）',
    `mode` tinyint NOT NULL COMMENT '跟单模式：1-智能比例，2-多元探索',
    `investment_amount` decimal(20,8) NULL COMMENT '投资金额（USDT）（智能比例模式）',
    `copy_type` tinyint NULL COMMENT '跟单方式：1-固定额度，2-倍率（多元探索模式）',
    `fixed_amount` decimal(20,8) NULL COMMENT '固定额度（USDT）（多元探索模式）',
    `rate` decimal(8,2) NULL COMMENT '倍率 %（多元探索模式）',
    `stop_loss_rate` decimal(8,2) NULL COMMENT '止损比例 %',
    `take_profit_rate` decimal(8,2) NULL COMMENT '止盈比例 %',
    `max_follow_amount` decimal(20,8) NULL COMMENT '最大跟随金额',
    `slippage_rate` decimal(8,2) NULL COMMENT '滑点比例 %',
    `auto_new_pairs` tinyint NOT NULL DEFAULT '0' COMMENT '自动跟随新币对：1-是，0-否',
    `is_exclusive` tinyint NOT NULL DEFAULT '0' COMMENT '是否尊享模式：1-是，0-否',
    `copy_currencies` json NULL COMMENT '跟单币种配置（支持多币种）',
    `net_value_guardian` tinyint NOT NULL DEFAULT '0' COMMENT '净值守护者：1-开启，0-关闭（智能比例模式）',
    `max_loss_amount` decimal(20,8) NULL COMMENT '最大亏损金额（触发则解除跟单）（智能比例模式）',
    `min_net_value` decimal(20,8) NULL COMMENT '最小净值金额（触发则解除跟单）（智能比例模式）',
    `copy_all_positions` tinyint NOT NULL DEFAULT '0' COMMENT '跟单后是否复制全部仓位：1-是，0-否（智能比例模式）',
    `margin_mode` tinyint NOT NULL DEFAULT '1' COMMENT '保证金模式：1-跟随专家，2-全仓，3-逐仓',
    `leverage_mode` tinyint NOT NULL DEFAULT '1' COMMENT '杠杆设置：1-跟随专家，2-指定杠杆',
    `custom_leverage` int NULL COMMENT '自定义杠杆倍数（仅当leverage_mode=2时有效）',
    `status` tinyint NOT NULL DEFAULT '1' COMMENT '状态：1-跟单中，2-暂停',
    `created_at` timestamp NULL COMMENT '创建时间',
    `updated_at` timestamp NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_follower_expert` (`follower_user_id`, `expert_id`),
    INDEX `idx_expert` (`expert_id`),
    INDEX `idx_expert_user_id` (`expert_user_id`),
    INDEX `idx_status` (`status`)
) COMMENT='用户跟单配置表';
```

### 2.11 现货跟单配置表

```sql
CREATE TABLE `copy_spot_user_setting` (
    `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `follower_user_id` bigint unsigned NOT NULL COMMENT '跟单者用户ID',
    `expert_id` bigint unsigned NOT NULL COMMENT '专家ID',
    `expert_user_id` bigint unsigned NOT NULL COMMENT '专家用户ID（冗余字段）',
    `copy_type` tinyint NULL COMMENT '跟单方式：1-固定额度，2-倍率',
    `fixed_amount` decimal(20,8) NULL COMMENT '固定额度（USDT）',
    `rate` decimal(8,2) NULL COMMENT '倍率 %',
    `stop_loss_rate` decimal(8,2) NULL COMMENT '止损比例 %',
    `take_profit_rate` decimal(8,2) NULL COMMENT '止盈比例 %',
    `max_follow_amount` decimal(20,8) NULL COMMENT '最大跟随金额',
    `auto_new_pairs` tinyint NOT NULL DEFAULT '0' COMMENT '自动跟随新币对：1-是，0-否',
    `is_exclusive` tinyint NOT NULL DEFAULT '0' COMMENT '是否尊享模式：1-是，0-否',
    `copy_currencies` json NULL COMMENT '跟单币种配置（支持多币种）',
    `status` tinyint NOT NULL DEFAULT '1' COMMENT '状态：1-跟单中，2-暂停',
    `created_at` timestamp NULL COMMENT '创建时间',
    `updated_at` timestamp NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_follower_expert` (`follower_user_id`, `expert_id`),
    INDEX `idx_expert` (`expert_id`),
    INDEX `idx_expert_user_id` (`expert_user_id`),
    INDEX `idx_status` (`status`)
) COMMENT='用户跟单配置表';
```

### 2.12 合约多元探索跟单高级设置表

```sql
CREATE TABLE `copy_contract_advanced_setting` (
    `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `follower_user_id` bigint unsigned NOT NULL COMMENT '跟单者用户ID',
    `expert_id` bigint unsigned NOT NULL COMMENT '专家ID',
    `expert_user_id` bigint unsigned NOT NULL COMMENT '专家用户ID（冗余字段）',
    `currency_id` bigint unsigned NOT NULL COMMENT '币种ID',
    `copy_type` tinyint NOT NULL COMMENT '跟单方式：1-固定额度，2-倍率',
    `fixed_amount` decimal(20,8) NULL COMMENT '固定额度（USDT）',
    `rate` decimal(8,2) NULL COMMENT '倍率 %',
    `stop_loss_rate` decimal(8,2) NULL COMMENT '止损比例 %',
    `take_profit_rate` decimal(8,2) NULL COMMENT '止盈比例 %',
    `max_follow_amount` decimal(20,8) NULL COMMENT '最大跟随金额',
    `slippage_rate` decimal(8,2) NULL COMMENT '滑点比例 %',
    `margin_mode` tinyint NOT NULL DEFAULT '1' COMMENT '保证金模式：1-跟随专家，2-全仓，3-逐仓',
    `leverage_mode` tinyint NOT NULL DEFAULT '1' COMMENT '杠杆设置：1-跟随专家，2-指定杠杆',
    `custom_leverage` int NULL COMMENT '自定义杠杆倍数（仅当leverage_mode=2时有效）',
    `created_at` timestamp NULL COMMENT '创建时间',
    `updated_at` timestamp NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_follower_expert_currency` (`follower_user_id`, `expert_id`, `currency_id`),
    INDEX `idx_expert` (`expert_id`),
    INDEX `idx_expert_user_id` (`expert_user_id`),
    INDEX `idx_currency_id` (`currency_id`)
) COMMENT='合约多元探索跟单高级设置表';
```

### 2.13 现货多元探索跟单高级设置表

```sql
CREATE TABLE `copy_spot_advanced_setting` (
    `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `follower_user_id` bigint unsigned NOT NULL COMMENT '跟单者用户ID',
    `expert_id` bigint unsigned NOT NULL COMMENT '专家ID',
    `expert_user_id` bigint unsigned NOT NULL COMMENT '专家用户ID（冗余字段）',
    `currency_id` bigint unsigned NOT NULL COMMENT '币种ID',
    `copy_type` tinyint NOT NULL COMMENT '跟单方式：1-固定额度，2-倍率',
    `fixed_amount` decimal(20,8) NULL COMMENT '固定额度（USDT）',
    `rate` decimal(8,2) NULL COMMENT '倍率 %',
    `stop_loss_rate` decimal(8,2) NULL COMMENT '止损比例 %',
    `take_profit_rate` decimal(8,2) NULL COMMENT '止盈比例 %',
    `max_follow_amount` decimal(20,8) NULL COMMENT '最大跟随金额',
    `created_at` timestamp NULL COMMENT '创建时间',
    `updated_at` timestamp NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_follower_expert_currency` (`follower_user_id`, `expert_id`, `currency_id`),
    INDEX `idx_expert` (`expert_id`),
    INDEX `idx_expert_user_id` (`expert_user_id`),
    INDEX `idx_currency_id` (`currency_id`)
) COMMENT='现货多元探索跟单高级设置表';
```

### 2.14 尊享模式邀请表（新增分润比例字段）

```sql
CREATE TABLE `copy_exclusive_invitation` (
    `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `expert_id` bigint unsigned NOT NULL COMMENT '专家ID',
    `expert_type` varchar(255) NOT NULL COMMENT '专家模型类名（多态关联）',
    `expert_user_id` bigint unsigned NOT NULL COMMENT '专家用户ID（冗余字段）',
    `type` tinyint NOT NULL COMMENT '专家类型：1-合约，2-现货',
    `title` varchar(100) NULL COMMENT '链接标题',
    `invite_code` varchar(32) NOT NULL COMMENT '邀请码',
    `max_count` int NULL COMMENT '最大邀请人数',
    `current_count` int NULL DEFAULT '0' COMMENT '当前邀请人数',
    `profit_sharing_rate` decimal(8,2) NULL COMMENT '分润比例 %',
    `expired_at` timestamp NULL COMMENT '过期时间',
    `created_at` timestamp NULL COMMENT '创建时间',
    `updated_at` timestamp NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_invite_code` (`invite_code`),
    INDEX `idx_expert` (`expert_id`, `expert_type`),
    INDEX `idx_expert_user_id` (`expert_user_id`)
) COMMENT='尊享模式邀请表';
```

### 2.15 尊享模式成员表（调整字段）

```sql
CREATE TABLE `copy_exclusive_member` (
    `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `invitation_id` bigint unsigned NOT NULL COMMENT '邀请记录ID',
    `expert_id` bigint unsigned NOT NULL COMMENT '专家ID',
    `expert_type` varchar(255) NOT NULL COMMENT '专家模型类名（多态关联）',
    `expert_user_id` bigint unsigned NOT NULL COMMENT '专家用户ID（冗余字段）',
    `type` tinyint NOT NULL COMMENT '专家类型：1-合约，2-现货',
    `follower_user_id` bigint unsigned NOT NULL COMMENT '跟单者用户ID',
    `profit_sharing_rate` decimal(8,2) NOT NULL COMMENT '分润比例（可单独修改）%',
    `joined_at` timestamp NOT NULL COMMENT '加入时间',
    `created_at` timestamp NULL COMMENT '创建时间',
    `updated_at` timestamp NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_invitation_follower` (`invitation_id`, `follower_user_id`),
    INDEX `idx_expert` (`expert_id`, `expert_type`),
    INDEX `idx_expert_user_id` (`expert_user_id`),
    INDEX `idx_follower_user_id` (`follower_user_id`)
) COMMENT='尊享模式成员表';
```

### 2.16 合约分润记录表

```sql
CREATE TABLE `copy_contract_profit_sharing` (
    `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `expert_id` bigint unsigned NOT NULL COMMENT '专家ID',
    `expert_user_id` bigint unsigned NOT NULL COMMENT '专家用户ID（冗余字段）',
    `follower_user_id` bigint unsigned NOT NULL COMMENT '跟单者用户ID',
    `copy_position_id` bigint unsigned NOT NULL COMMENT '跟单仓位记录ID',
    `expert_position_id` bigint unsigned NOT NULL COMMENT '专家仓位ID（关联trade_perpetual_position）',
    `follower_position_id` bigint unsigned NOT NULL COMMENT '跟单者仓位ID（关联trade_perpetual_position）',
    `profit` decimal(20,8) NOT NULL COMMENT '盈亏金额',
    `profit_sharing` decimal(20,8) NOT NULL COMMENT '分润金额',
    `profit_sharing_rate` decimal(8,2) NOT NULL COMMENT '分润比例 %',
    `currency_id` bigint unsigned NOT NULL COMMENT '分润币种ID（默认USDT）',
    `created_at` timestamp NULL COMMENT '创建时间',
    `updated_at` timestamp NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    INDEX `idx_expert` (`expert_id`),
    INDEX `idx_expert_user_id` (`expert_user_id`),
    INDEX `idx_follower_user_id` (`follower_user_id`),
    INDEX `idx_copy_position_id` (`copy_position_id`),
    INDEX `idx_expert_position_id` (`expert_position_id`),
    INDEX `idx_follower_position_id` (`follower_position_id`)
) COMMENT='合约分润记录表';
```

### 2.17 现货分润记录表

```sql
CREATE TABLE `copy_spot_profit_sharing` (
    `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `expert_id` bigint unsigned NOT NULL COMMENT '专家ID',
    `expert_user_id` bigint unsigned NOT NULL COMMENT '专家用户ID（冗余字段）',
    `follower_user_id` bigint unsigned NOT NULL COMMENT '跟单者用户ID',
    `copy_order_id` bigint unsigned NOT NULL COMMENT '跟单记录ID',
    `expert_order_id` bigint unsigned NOT NULL COMMENT '专家订单ID（关联trade_spot_order）',
    `follower_order_id` bigint unsigned NOT NULL COMMENT '跟单者订单ID（关联trade_spot_order）',
    `profit` decimal(20,8) NOT NULL COMMENT '盈亏金额',
    `profit_sharing` decimal(20,8) NOT NULL COMMENT '分润金额',
    `profit_sharing_rate` decimal(8,2) NOT NULL COMMENT '分润比例 %',
    `currency_id` bigint unsigned NOT NULL COMMENT '分润币种ID（默认USDT）',
    `created_at` timestamp NULL COMMENT '创建时间',
    `updated_at` timestamp NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    INDEX `idx_expert` (`expert_id`),
    INDEX `idx_expert_user_id` (`expert_user_id`),
    INDEX `idx_follower_user_id` (`follower_user_id`),
    INDEX `idx_copy_order_id` (`copy_order_id`),
    INDEX `idx_expert_order_id` (`expert_order_id`),
    INDEX `idx_follower_order_id` (`follower_order_id`)
) COMMENT='现货分润记录表';
```

### 2.18 分润比例修改记录表

```sql
CREATE TABLE `copy_profit_rate_log` (
    `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `expert_id` bigint unsigned NOT NULL COMMENT '专家ID',
    `expert_type` varchar(255) NOT NULL COMMENT '专家模型类名（多态关联）',
    `expert_user_id` bigint unsigned NOT NULL COMMENT '专家用户ID（冗余字段）',
    `type` tinyint NOT NULL COMMENT '专家类型：1-合约，2-现货',
    `old_rate` decimal(8,2) NULL COMMENT '原分润比例 %',
    `new_rate` decimal(8,2) NOT NULL COMMENT '新分润比例 %',
    `is_exclusive` tinyint NOT NULL DEFAULT '0' COMMENT '是否尊享模式：1-是，0-否',
    `created_at` timestamp NULL COMMENT '创建时间',
    `updated_at` timestamp NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    INDEX `idx_expert` (`expert_id`, `expert_type`),
    INDEX `idx_expert_user_id` (`expert_user_id`)
) COMMENT='分润比例修改记录表';
```

### 2.19 合约交易专家统计表

```sql
CREATE TABLE `copy_contract_expert_statistics` (
    `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `expert_id` bigint unsigned NOT NULL COMMENT '专家ID',
    `expert_user_id` bigint unsigned NOT NULL COMMENT '专家用户ID（冗余字段）',
    -- 总盈利、7日总盈利、30日总盈利、90日总盈利、180日总盈利
    `total_profit` decimal(20,8) NOT NULL DEFAULT '0' COMMENT '总盈利',
    `total_profit_7d` decimal(20,8) NOT NULL DEFAULT '0' COMMENT '7日总盈利',
    `total_profit_30d` decimal(20,8) NOT NULL DEFAULT '0' COMMENT '30日总盈利',
    `total_profit_90d` decimal(20,8) NOT NULL DEFAULT '0' COMMENT '90日总盈利',
    `total_profit_180d` decimal(20,8) NOT NULL DEFAULT '0' COMMENT '180日总盈利',
    -- 总亏损、7日总亏损、30日总亏损、90日总亏损、180日总亏损
    `total_loss` decimal(20,8) NOT NULL DEFAULT '0' COMMENT '总亏损',
    `total_loss_7d` decimal(20,8) NOT NULL DEFAULT '0' COMMENT '7日总亏损',
    `total_loss_30d` decimal(20,8) NOT NULL DEFAULT '0' COMMENT '30日总亏损',
    `total_loss_90d` decimal(20,8) NOT NULL DEFAULT '0' COMMENT '90日总亏损',
    `total_loss_180d` decimal(20,8) NOT NULL DEFAULT '0' COMMENT '180日总亏损',
    -- 盈利订单数（仓位单位）、7日盈利订单数（仓位单位）、30日盈利订单数（仓位单位）、90日盈利订单数（仓位单位）、180日盈利订单数（仓位单位）
    `profit_order_count` int NOT NULL DEFAULT '0' COMMENT '盈利订单数',
    `profit_order_count_7d` int NOT NULL DEFAULT '0' COMMENT '7日盈利订单数',
    `profit_order_count_30d` int NOT NULL DEFAULT '0' COMMENT '30日盈利订单数',
    `profit_order_count_90d` int NOT NULL DEFAULT '0' COMMENT '90日盈利订单数',
    `profit_order_count_180d` int NOT NULL DEFAULT '0' COMMENT '180日盈利订单数',
    -- 亏损订单数（仓位单位）、7日亏损订单数（仓位单位）、30日亏损订单数（仓位单位）、90日亏损订单数（仓位单位）、180日亏损订单数（仓位单位）
    `loss_order_count` int NOT NULL DEFAULT '0' COMMENT '亏损订单数',
    `loss_order_count_7d` int NOT NULL DEFAULT '0' COMMENT '7日亏损订单数',
    `loss_order_count_30d` int NOT NULL DEFAULT '0' COMMENT '30日亏损订单数',
    `loss_order_count_90d` int NOT NULL DEFAULT '0' COMMENT '90日亏损订单数',
    `loss_order_count_180d` int NOT NULL DEFAULT '0' COMMENT '180日亏损订单数',
    -- 平均盈利、7日平均盈利、30日平均盈利、90日平均盈利、180日平均盈利
    `average_profit` decimal(20,8) NOT NULL DEFAULT '0' COMMENT '平均盈利',
    `average_profit_7d` decimal(20,8) NOT NULL DEFAULT '0' COMMENT '7日平均盈利',
    `average_profit_30d` decimal(20,8) NOT NULL DEFAULT '0' COMMENT '30日平均盈利',
    `average_profit_90d` decimal(20,8) NOT NULL DEFAULT '0' COMMENT '90日平均盈利',
    `average_profit_180d` decimal(20,8) NOT NULL DEFAULT '0' COMMENT '180日平均盈利',
    -- 平均亏损、7日平均亏损、30日平均亏损、90日平均亏损、180日平均亏损
    `average_loss` decimal(20,8) NOT NULL DEFAULT '0' COMMENT '平均亏损',
    `average_loss_7d` decimal(20,8) NOT NULL DEFAULT '0' COMMENT '7日平均亏损',
    `average_loss_30d` decimal(20,8) NOT NULL DEFAULT '0' COMMENT '30日平均亏损',
    `average_loss_90d` decimal(20,8) NOT NULL DEFAULT '0' COMMENT '90日平均亏损',
    `average_loss_180d` decimal(20,8) NOT NULL DEFAULT '0' COMMENT '180日平均亏损',
    -- 盈亏金额
    `profit` decimal(20,8) NOT NULL DEFAULT '0' COMMENT '盈亏金额',
    `profit_7d` decimal(20,8) NOT NULL DEFAULT '0' COMMENT '7日盈亏金额',
    `profit_30d` decimal(20,8) NOT NULL DEFAULT '0' COMMENT '30日盈亏金额',
    `profit_90d` decimal(20,8) NOT NULL DEFAULT '0' COMMENT '90日盈亏金额',
    `profit_180d` decimal(20,8) NOT NULL DEFAULT '0' COMMENT '180日盈亏金额',
    -- 胜率、7日胜率、30日胜率、90日胜率、180日胜率
    `win_rate` decimal(8,2) NOT NULL DEFAULT '0' COMMENT '胜率 %',
    `win_rate_7d` decimal(8,2) NOT NULL DEFAULT '0' COMMENT '7日胜率 %',
    `win_rate_30d` decimal(8,2) NOT NULL DEFAULT '0' COMMENT '30日胜率 %',
    `win_rate_90d` decimal(8,2) NOT NULL DEFAULT '0' COMMENT '90日胜率 %',
    `win_rate_180d` decimal(8,2) NOT NULL DEFAULT '0' COMMENT '180日胜率 %',
    -- 收益率、7日收益率、30日收益率、90日收益率、180日收益率
    `profit_rate` decimal(8,2) NOT NULL DEFAULT '0' COMMENT '收益率 %',
    `profit_rate_7d` decimal(8,2) NOT NULL DEFAULT '0' COMMENT '7日收益率 %',
    `profit_rate_30d` decimal(8,2) NOT NULL DEFAULT '0' COMMENT '30日收益率 %',
    `profit_rate_90d` decimal(8,2) NOT NULL DEFAULT '0' COMMENT '90日收益率 %',
    `profit_rate_180d` decimal(8,2) NOT NULL DEFAULT '0' COMMENT '180日收益率 %',
    -- 跟单者收益、7日跟单者收益、30日跟单者收益、90日跟单者收益、180日跟单者收益
    `follower_profit` decimal(20,8) NOT NULL DEFAULT '0' COMMENT '跟单者收益',
    `follower_profit_7d` decimal(20,8) NOT NULL DEFAULT '0' COMMENT '7日跟单者收益',
    `follower_profit_30d` decimal(20,8) NOT NULL DEFAULT '0' COMMENT '30日跟单者收益',
    `follower_profit_90d` decimal(20,8) NOT NULL DEFAULT '0' COMMENT '90日跟单者收益',
    `follower_profit_180d` decimal(20,8) NOT NULL DEFAULT '0' COMMENT '180日跟单者收益',
    -- 最大回撤、7日最大回撤、30日最大回撤、90日最大回撤、180日最大回撤
    `max_drawdown` decimal(20,8) NOT NULL DEFAULT '0' COMMENT '最大回撤',
    `max_drawdown_7d` decimal(20,8) NOT NULL DEFAULT '0' COMMENT '7日最大回撤',
    `max_drawdown_30d` decimal(20,8) NOT NULL DEFAULT '0' COMMENT '30日最大回撤',
    `max_drawdown_90d` decimal(20,8) NOT NULL DEFAULT '0' COMMENT '90日最大回撤',
    `max_drawdown_180d` decimal(20,8) NOT NULL DEFAULT '0' COMMENT '180日最大回撤',
    -- 资产管理规模
    `aum` decimal(20,8) NOT NULL DEFAULT '0' COMMENT '资产管理规模',
    -- 交易频率、7日交易频率、30日交易频率、90日交易频率、180日交易频率
    `trade_frequency` int NOT NULL DEFAULT '0' COMMENT '交易频率',
    `trade_frequency_7d` int NOT NULL DEFAULT '0' COMMENT '7日交易频率',
    `trade_frequency_30d` int NOT NULL DEFAULT '0' COMMENT '30日交易频率',
    `trade_frequency_90d` int NOT NULL DEFAULT '0' COMMENT '90日交易频率',
    `trade_frequency_180d` int NOT NULL DEFAULT '0' COMMENT '180日交易频率',
    -- 累计跟单人数
    `total_follower_count` int NOT NULL DEFAULT '0' COMMENT '累计跟单人数',
    `created_at` timestamp NULL COMMENT '创建时间',
    `updated_at` timestamp NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    INDEX `idx_expert` (`expert_id`),
    INDEX `idx_expert_user_id` (`expert_user_id`)
) COMMENT='合约交易专家统计表';
```

### 2.20 现货交易专家统计表

```sql
CREATE TABLE `copy_spot_expert_statistics` (
    `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `expert_id` bigint unsigned NOT NULL COMMENT '专家ID',
    `expert_user_id` bigint unsigned NOT NULL COMMENT '专家用户ID（冗余字段）',
    `profit` decimal(20,8) NOT NULL DEFAULT '0' COMMENT '收益金额',
    `profit_7d` decimal(20,8) NOT NULL DEFAULT '0' COMMENT '7日收益金额',
    `profit_30d` decimal(20,8) NOT NULL DEFAULT '0' COMMENT '30日收益金额',
    `profit_90d` decimal(20,8) NOT NULL DEFAULT '0' COMMENT '90日收益金额',
    `profit_180d` decimal(20,8) NOT NULL DEFAULT '0' COMMENT '180日收益金额',
    -- 盈利订单数（仓位单位）、7日盈利订单数（仓位单位）、30日盈利订单数（仓位单位）、90日盈利订单数（仓位单位）、180日盈利订单数（仓位单位）
    `profit_order_count` int NOT NULL DEFAULT '0' COMMENT '盈利订单数',
    `profit_order_count_7d` int NOT NULL DEFAULT '0' COMMENT '7日盈利订单数',
    `profit_order_count_30d` int NOT NULL DEFAULT '0' COMMENT '30日盈利订单数',
    `profit_order_count_90d` int NOT NULL DEFAULT '0' COMMENT '90日盈利订单数',
    `profit_order_count_180d` int NOT NULL DEFAULT '0' COMMENT '180日盈利订单数',
    -- 亏损订单数（仓位单位）、7日亏损订单数（仓位单位）、30日亏损订单数（仓位单位）、90日亏损订单数（仓位单位）、180日亏损订单数（仓位单位）
    `loss_order_count` int NOT NULL DEFAULT '0' COMMENT '亏损订单数',
    `loss_order_count_7d` int NOT NULL DEFAULT '0' COMMENT '7日亏损订单数',
    `loss_order_count_30d` int NOT NULL DEFAULT '0' COMMENT '30日亏损订单数',
    `loss_order_count_90d` int NOT NULL DEFAULT '0' COMMENT '90日亏损订单数',
    `loss_order_count_180d` int NOT NULL DEFAULT '0' COMMENT '180日亏损订单数',
    `win_rate` decimal(8,2) NOT NULL DEFAULT '0' COMMENT '胜率 %',
    `win_rate_7d` decimal(8,2) NOT NULL DEFAULT '0' COMMENT '7日胜率 %',
    `win_rate_30d` decimal(8,2) NOT NULL DEFAULT '0' COMMENT '30日胜率 %',
    `win_rate_90d` decimal(8,2) NOT NULL DEFAULT '0' COMMENT '90日胜率 %',
    `win_rate_180d` decimal(8,2) NOT NULL DEFAULT '0' COMMENT '180日胜率 %',
    `profit_rate` decimal(8,2) NOT NULL DEFAULT '0' COMMENT '收益率 %',
    `profit_rate_7d` decimal(8,2) NOT NULL DEFAULT '0' COMMENT '7日收益率 %',
    `profit_rate_30d` decimal(8,2) NOT NULL DEFAULT '0' COMMENT '30日收益率 %',
    `profit_rate_90d` decimal(8,2) NOT NULL DEFAULT '0' COMMENT '90日收益率 %',
    `profit_rate_180d` decimal(8,2) NOT NULL DEFAULT '0' COMMENT '180日收益率 %',
    `follower_profit` decimal(20,8) NOT NULL DEFAULT '0' COMMENT '跟单者收益',
    `follower_profit_7d` decimal(20,8) NOT NULL DEFAULT '0' COMMENT '7日跟单者收益',
    `follower_profit_30d` decimal(20,8) NOT NULL DEFAULT '0' COMMENT '30日跟单者收益',
    `follower_profit_90d` decimal(20,8) NOT NULL DEFAULT '0' COMMENT '90日跟单者收益',
    `follower_profit_180d` decimal(20,8) NOT NULL DEFAULT '0' COMMENT '180日跟单者收益',
    -- 资产管理规模
    `aum` decimal(20,8) NOT NULL DEFAULT '0' COMMENT '资产管理规模',
    -- 交易频率、7日交易频率、30日交易频率、90日交易频率、180日交易频率
    `trade_frequency` int NOT NULL DEFAULT '0' COMMENT '交易频率',
    `trade_frequency_7d` int NOT NULL DEFAULT '0' COMMENT '7日交易频率',
    `trade_frequency_30d` int NOT NULL DEFAULT '0' COMMENT '30日交易频率',
    `trade_frequency_90d` int NOT NULL DEFAULT '0' COMMENT '90日交易频率',
    `trade_frequency_180d` int NOT NULL DEFAULT '0' COMMENT '180日交易频率',
    -- 累计跟单人数
    `total_follower_count` int NOT NULL DEFAULT '0' COMMENT '累计跟单人数',
    `created_at` timestamp NULL COMMENT '创建时间',
    `updated_at` timestamp NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    INDEX `idx_expert` (`expert_id`),
    INDEX `idx_expert_user_id` (`expert_user_id`)
) COMMENT='现货交易专家统计表';
```

## 3. 多态关联说明

### 3.1 多态关联字段规范

- **expert_type**: 存储模型类名，如 `App\Model\Copy\CopyContractExpert`、`App\Model\Copy\CopySpotExpert`
- **expert_user_id**: 冗余字段，便于查询

### 3.2 多态关联实现说明

多态关联用于实现灵活的专家类型关联：

- 使用 morphTo 定义反向多态关联
- 使用 morphMany 定义正向多态关联
- 通过 expert_type 和 expert_id 字段建章关联关系
- 支持合约专家和现货专家的统一关联处理

## 4. 系统设置 Seeder 文件

### 4.1 配置组 Seeder

跟单设置配置组数据：

- 配置组名称：跟单设置
- 配置组标识：copy_order_setting
- 创建者：1（系统管理员）
- 备注：跟单模块相关配置

### 4.2 配置项 Seeder

跟单系统配置项包括：

1. **合约专家申请是否需要审核**

   - 配置键：`contract_expert_need_review`
   - 输入类型：switch
   - 默认值：1（需要审核）

2. **现货专家申请是否需要审核**

   - 配置键：`spot_expert_need_review`
   - 输入类型：switch
   - 默认值：1（需要审核）

3. **合约专家最小划转资金**

   - 配置键：`contract_expert_min_transfer`
   - 输入类型：input
   - 默认值：100 USDT

4. **问题反馈类型预设**

   - 配置键：`feedback_problem_types`
   - 输入类型：keyValuePair
   - 预设值：技术问题、资金问题、策略问题、平台问题、其他问题

5. **合约交易专家展示条件**

   - 配置键：`contract_expert_display_conditions`
   - 输入类型：keyValuePair
   - 条件项：
     - currency_count（币对数量）：1
     - order_count（订单数量）：1
     - copy_account_assets（账户资产）：100 USDT

6. **现货交易专家展示条件**

   - 配置键：`spot_expert_display_conditions`
   - 输入类型：keyValuePair
   - 条件项：
     - currency_count（币对数量）：1
     - order_count（订单数量）：1
     - spot_account_assets（账户资产）：100 USDT

7. **合约跟单最小资金**

   - 配置键：`contract_min_follow_amount`
   - 输入类型：input
   - 默认值：100 USDT

8. **现货跟单最小资金**

   - 配置键：`spot_min_follow_amount`
   - 输入类型：input
   - 默认值：100 USDT

系统设置项获取方式：

```php
$setting = \Plugin\West\SysSettings\Helper\Helper::getSysSettingByTypeCode('key');
// 获取配置项的值
$value = $setting->value;
// 获取配置选项参数（input_type 为 keyValuePair 类型时）
$configSelectData = $setting->config_select_data;
```

## 5. 枚举类设计

### 5.1 交易专家类型枚举

交易专家类型定义：

- CONTRACT = 1：合约交易专家
- SPOT = 2：现货交易专家

使用多语言文件存储显示名称：`copy.expert_type.contract`、`copy.expert_type.spot`

### 5.2 推荐参数类型枚举

推荐参数类型：

- FIXED_AMOUNT = 1：固定额度
- RATE = 2：跟单倍率

### 5.3 跟单状态枚举

跟单状态定义：

- FOLLOWING = 1：跟单中
- PAUSED = 2：暂停

### 5.4 跟单模式枚举

跟单模式：

- SMART_RATE = 1：智能比例跟单
- MULTI_EXPLORE = 2：多元探索跟单

### 5.5 订单状态枚举

订单状态：

- OPEN = 1：未平仓
- CLOSED = 2：已平仓
- CANCELLED = 3：已取消

### 5.6 保证金模式枚举

保证金模式：

- FOLLOW_EXPERT = 1：跟随专家
- CROSS = 2：全仓模式
- ISOLATED = 3：逐仓模式

### 5.7 杠杆模式枚举

杠杆模式：

- FOLLOW_EXPERT = 1：跟随专家
- CUSTOM = 2：自定义杠杆

## 6. 接口目录结构

### 6.1 专家端接口

```
app/
|──Model/
|   └── Copy/ # 跟单模块模型目录
|       └── Enums/ # 跟单枚举类目录
└── Http/
    └── Api/
        ├── Controller/
        │   └── Copy/ # 跟单模块控制器目录
        │       └── Expert/ # 专家端控制器目录（合约、现货合并接口控制器直接放到此目录）
        │       │   └── Contract/ # 专家端合约控制器目录
        │       │   │   └── XxxController.php
        │       │   └── Spot/ # 专家端现货控制器目录
        │       │   │   └── XxxController.php
        │       │   └── XxxController.php
        │       └── User/ # 用户端控制器目录（合约、现货合并接口控制器直接放到此目录）
        │       │   └── Contract/ # 用户端合约控制器目录
        │       │   │   └── XxxController.php
        │       │   └── Spot/ # 用户端现货控制器目录
        │       │   │   └── XxxController.php
        │       │   └── XxxController.php
        └── Request/
        |   └── Copy/ # 跟单模块请求验证器目录
        |   │   └── Expert/ # 专家端请求验证器目录（合约、现货合并接口验证器直接放到此目录）
        |   │   │   └── Contract/ # 专家端合约请求验证器目录
        |   │   │   │   └── XxxRequest.php
        |   │   │   └── Spot/ # 专家端现货请求验证器目录
        |   │   │   │   └── XxxRequest.php
        |   │   │   └── XxxRequest.php
        |   │   └── User/ # 用户端请求验证器目录（合约、现货合并接口验证器直接放到此目录）
        |   │   │   └── Contract/ # 用户端合约请求验证器目录
        |   │   │   │   └── XxxRequest.php
        |   │   │   └── Spot/ # 用户端现货请求验证器目录
        |   │   │   │   └── XxxRequest.php
        |   │   │   └── XxxRequest.php
        └── Service/
            └── Copy/ # 跟单模块服务类目录
                └── Expert/ # 专家端服务类目录（合约、现货合并服务类直接放到此目录）
                │   └── Contract/ # 专家端合约服务类目录
                │   │   └── XxxService.php
                │   └── Spot/ # 专家端现货服务类目录
                │   │   └── XxxService.php
                │   └── XxxService.php
                └── User/ # 用户端服务类目录（合约、现货合并服务类直接放到此目录）
                    └── Contract/ # 用户端合约服务类目录
                    │   └── XxxService.php
                    └── Spot/ # 用户端现货服务类目录
                    │   └── XxxService.php
                    └── XxxService.php
```
