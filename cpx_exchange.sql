-- MySQL dump 10.13  Distrib 8.0.42, for macos15.2 (arm64)
--
-- Host: **************    Database: cpx_exchange
-- ------------------------------------------------------
-- Server version	8.0.42

/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!50503 SET NAMES utf8mb4 */;
/*!40103 SET @OLD_TIME_ZONE=@@TIME_ZONE */;
/*!40103 SET TIME_ZONE='+00:00' */;
/*!40014 SET @OLD_UNIQUE_CHECKS=@@UNIQUE_CHECKS, UNIQUE_CHECKS=0 */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;

--
-- Table structure for table `attachment`
--

DROP TABLE IF EXISTS `attachment`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `attachment` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键',
  `storage_mode` varchar(20) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'local' COMMENT '存储模式:local=本地,oss=阿里云,qiniu=七牛云,cos=腾讯云',
  `origin_name` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '原文件名',
  `object_name` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '新文件名',
  `hash` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '文件hash',
  `mime_type` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '资源类型',
  `storage_path` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '存储目录',
  `suffix` varchar(20) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '文件后缀',
  `size_byte` bigint DEFAULT NULL COMMENT '字节数',
  `size_info` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '文件大小',
  `url` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'url地址',
  `created_by` bigint NOT NULL DEFAULT '0' COMMENT '创建者',
  `updated_by` bigint NOT NULL DEFAULT '0' COMMENT '更新者',
  `created_at` datetime DEFAULT NULL,
  `updated_at` datetime DEFAULT NULL,
  `remark` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '备注',
  PRIMARY KEY (`id`),
  UNIQUE KEY `attachment_hash_unique` (`hash`),
  KEY `attachment_storage_path_index` (`storage_path`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='上传文件信息表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `binary_option_configs`
--

DROP TABLE IF EXISTS `binary_option_configs`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `binary_option_configs` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `currency_id` int NOT NULL COMMENT '币种ID',
  `time_period` int NOT NULL COMMENT '时间周期(秒)',
  `time_period_name` varchar(20) NOT NULL COMMENT '时间周期名称(1m,5m,1h等)',
  `price_source` tinyint(1) NOT NULL DEFAULT '1' COMMENT '价格来源:1现货,2合约',
  `win_rate_type` tinyint(1) NOT NULL DEFAULT '1' COMMENT '盈利比例类型:1固定,2动态',
  `lose_rate_type` tinyint(1) NOT NULL DEFAULT '1' COMMENT '亏损比例类型:1固定,2动态',
  `fixed_win_rate` decimal(8,4) DEFAULT NULL COMMENT '固定盈利比例(0.8000表示80%)',
  `fixed_lose_rate` decimal(8,4) DEFAULT NULL COMMENT '固定亏损比例(1.0000表示100%)',
  `dynamic_win_formula` text COMMENT '动态盈利计算公式:0.8 + ({price_diff_rate} * 0.5)',
  `dynamic_lose_formula` text COMMENT '动态亏损计算公式:1.0 - ({price_diff_rate} * 0.2)',
  `min_amount` decimal(20,8) NOT NULL COMMENT '最小下单金额',
  `max_amount` decimal(20,8) NOT NULL COMMENT '最大下单金额',
  `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态:0禁用,1启用',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_currency_period` (`currency_id`,`time_period`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='二元期权配置表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `binary_option_orders`
--

DROP TABLE IF EXISTS `binary_option_orders`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `binary_option_orders` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `order_no` varchar(32) NOT NULL COMMENT '订单号',
  `user_id` bigint NOT NULL COMMENT '用户ID',
  `currency_id` int NOT NULL COMMENT '币种ID',
  `time_period` int NOT NULL COMMENT '时间周期(秒)',
  `direction` tinyint(1) NOT NULL COMMENT '方向:1看涨,2看跌',
  `invest_amount` decimal(20,8) NOT NULL COMMENT '投资金额',
  `price_source` tinyint(1) NOT NULL COMMENT '价格来源:1现货,2合约',
  `open_price` decimal(20,8) NOT NULL COMMENT '开仓价格',
  `close_price` decimal(20,8) DEFAULT NULL COMMENT '结算价格',
  `win_rate` decimal(8,4) NOT NULL COMMENT '盈利比例',
  `lose_rate` decimal(8,4) NOT NULL COMMENT '亏损比例',
  `payout_amount` decimal(20,8) DEFAULT NULL COMMENT '结算金额(正数盈利,负数亏损)',
  `fee_amount` decimal(20,8) DEFAULT '0.********' COMMENT '手续费',
  `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态:1等待结算,2盈利,3亏损,4平局,5已取消',
  `open_time` timestamp NOT NULL COMMENT '开仓时间',
  `expire_time` timestamp NOT NULL COMMENT '到期时间',
  `settle_time` timestamp NULL DEFAULT NULL COMMENT '结算时间',
  `remark` varchar(255) DEFAULT NULL COMMENT '备注',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_order_no` (`order_no`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_status` (`status`),
  KEY `idx_expire_time` (`expire_time`),
  KEY `idx_currency_time` (`currency_id`,`time_period`),
  KEY `idx_settle_time` (`settle_time`)
) ENGINE=InnoDB AUTO_INCREMENT=5 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='二元期权订单表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `chain_currency`
--

DROP TABLE IF EXISTS `chain_currency`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `chain_currency` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `tokenId` varchar(128) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '币种token',
  `chainId` varchar(32) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '关联链id',
  `contractAddres` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '合约地址',
  `currency_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `symbol` varchar(16) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `icon` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `narketCap` decimal(20,2) DEFAULT NULL COMMENT '币种市值',
  `decimals` tinyint DEFAULT NULL COMMENT '精度',
  `hotTag` tinyint(1) DEFAULT '0' COMMENT '是否热门',
  `canTransfer` tinyint(1) DEFAULT '0' COMMENT '是否支持转账',
  `tradeDecimal` tinyint DEFAULT NULL COMMENT '交易精度',
  `alphaId` varchar(32) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '币安id',
  `score` int DEFAULT NULL COMMENT '评分',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_cid` (`tokenId`) USING BTREE,
  KEY `idx_to` (`symbol`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=306 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='币安alpha币种列表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `chain_currency_cate`
--

DROP TABLE IF EXISTS `chain_currency_cate`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `chain_currency_cate` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `chainId` varchar(32) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '链id',
  `chainIcon` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '链图标',
  `chainName` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '链名称',
  `transformAssets` json DEFAULT NULL COMMENT '交易币种',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=18 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='币安alpha 交易币栏目';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `chain_currency_mete`
--

DROP TABLE IF EXISTS `chain_currency_mete`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `chain_currency_mete` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `tokenId` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '币种token',
  `mate` json DEFAULT NULL COMMENT '币种详情',
  `description` json DEFAULT NULL COMMENT '币种介绍，多语言',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_token` (`tokenId`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=252 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='币安alpha币种详细数据';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `copy_contract_advanced_setting`
--

DROP TABLE IF EXISTS `copy_contract_advanced_setting`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `copy_contract_advanced_setting` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `follower_user_id` bigint unsigned NOT NULL COMMENT '跟单者用户ID',
  `expert_id` bigint unsigned NOT NULL COMMENT '专家ID',
  `expert_user_id` bigint unsigned NOT NULL COMMENT '专家用户ID（冗余字段）',
  `currency_id` bigint unsigned NOT NULL COMMENT '币种ID',
  `copy_type` tinyint NOT NULL COMMENT '跟单方式：1-固定额度，2-倍率',
  `fixed_amount` decimal(20,8) DEFAULT NULL COMMENT '固定额度（USDT）',
  `rate` decimal(8,2) DEFAULT NULL COMMENT '倍率 %',
  `stop_loss_rate` decimal(8,2) DEFAULT NULL COMMENT '止损比例 %',
  `take_profit_rate` decimal(8,2) DEFAULT NULL COMMENT '止盈比例 %',
  `max_follow_amount` decimal(20,8) DEFAULT NULL COMMENT '最大跟随金额',
  `slippage_rate` decimal(8,2) DEFAULT NULL COMMENT '滑点比例 %',
  `margin_mode` tinyint NOT NULL DEFAULT '1' COMMENT '保证金模式：1-跟随专家，2-全仓，3-逐仓',
  `leverage_mode` tinyint NOT NULL DEFAULT '1' COMMENT '杠杆设置：1-跟随专家，2-指定杠杆',
  `custom_leverage` int DEFAULT NULL COMMENT '自定义杠杆倍数（仅当leverage_mode=2时有效）',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_follower_expert_currency` (`follower_user_id`,`expert_id`,`currency_id`),
  KEY `idx_expert` (`expert_id`),
  KEY `idx_expert_user_id` (`expert_user_id`),
  KEY `idx_currency_id` (`currency_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='合约多元探索跟单高级设置表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `copy_contract_expert`
--

DROP TABLE IF EXISTS `copy_contract_expert`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `copy_contract_expert` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `user_id` bigint unsigned NOT NULL COMMENT '用户ID',
  `introduction` text COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '个人介绍',
  `transfer_from_account` tinyint NOT NULL COMMENT '划转资金来源账户，枚举：AppModelEnumsUserAccountType::class',
  `transfer_amount` decimal(20,8) NOT NULL COMMENT '划转金额（USDT）',
  `status` tinyint NOT NULL DEFAULT '1' COMMENT '申请状态：1-待审核，2-审核通过，3-审核拒绝',
  `review_remark` varchar(500) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '审核备注',
  `reviewed_at` timestamp NULL DEFAULT NULL COMMENT '审核时间',
  `reviewed_by` bigint unsigned DEFAULT NULL COMMENT '审核人ID',
  `is_active` tinyint NOT NULL DEFAULT '1' COMMENT '是否开启带单：1-是，0-否',
  `show_total_assets` tinyint NOT NULL DEFAULT '0' COMMENT '是否展示总资产：1-是，0-否',
  `show_expert_rating` tinyint NOT NULL DEFAULT '0' COMMENT '是否展示专家评分及排名：1-是，0-否',
  `position_protection` tinyint NOT NULL DEFAULT '1' COMMENT '未结仓位保护：1-开启，0-关闭',
  `min_follow_amount` decimal(20,8) DEFAULT NULL COMMENT '最小跟单金额（USDT）',
  `recommend_params` json DEFAULT NULL COMMENT '推荐参数配置',
  `currency_ids` json DEFAULT NULL COMMENT '跟单币种 id 配置（支持多币种）',
  `profit_sharing_rate` decimal(8,2) NOT NULL DEFAULT '0.00' COMMENT '分润比例 %',
  `level_id` bigint unsigned DEFAULT '1' COMMENT '专家等级ID',
  `rating` decimal(8,2) NOT NULL DEFAULT '0.00' COMMENT '专家评分',
  `exclusive_mode` tinyint NOT NULL DEFAULT '0' COMMENT '尊享模式：1-开启，0-关闭',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_user_id` (`user_id`),
  KEY `idx_status` (`status`),
  KEY `idx_is_active` (`is_active`),
  KEY `idx_level_id` (`level_id`),
  KEY `idx_exclusive_mode` (`exclusive_mode`)
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='合约交易专家表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `copy_contract_expert_statistics`
--

DROP TABLE IF EXISTS `copy_contract_expert_statistics`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `copy_contract_expert_statistics` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `expert_id` bigint unsigned NOT NULL COMMENT '专家ID',
  `expert_user_id` bigint unsigned NOT NULL COMMENT '专家用户ID（冗余字段）',
  `total_profit` decimal(20,8) NOT NULL DEFAULT '0.********' COMMENT '总盈利',
  `total_profit_7d` decimal(20,8) NOT NULL DEFAULT '0.********' COMMENT '7日总盈利',
  `total_profit_30d` decimal(20,8) NOT NULL DEFAULT '0.********' COMMENT '30日总盈利',
  `total_profit_90d` decimal(20,8) NOT NULL DEFAULT '0.********' COMMENT '90日总盈利',
  `total_profit_180d` decimal(20,8) NOT NULL DEFAULT '0.********' COMMENT '180日总盈利',
  `total_loss` decimal(20,8) NOT NULL DEFAULT '0.********' COMMENT '总亏损',
  `total_loss_7d` decimal(20,8) NOT NULL DEFAULT '0.********' COMMENT '7日总亏损',
  `total_loss_30d` decimal(20,8) NOT NULL DEFAULT '0.********' COMMENT '30日总亏损',
  `total_loss_90d` decimal(20,8) NOT NULL DEFAULT '0.********' COMMENT '90日总亏损',
  `total_loss_180d` decimal(20,8) NOT NULL DEFAULT '0.********' COMMENT '180日总亏损',
  `profit_order_count` int NOT NULL DEFAULT '0' COMMENT '盈利订单数',
  `profit_order_count_7d` int NOT NULL DEFAULT '0' COMMENT '7日盈利订单数',
  `profit_order_count_30d` int NOT NULL DEFAULT '0' COMMENT '30日盈利订单数',
  `profit_order_count_90d` int NOT NULL DEFAULT '0' COMMENT '90日盈利订单数',
  `profit_order_count_180d` int NOT NULL DEFAULT '0' COMMENT '180日盈利订单数',
  `loss_order_count` int NOT NULL DEFAULT '0' COMMENT '亏损订单数',
  `loss_order_count_7d` int NOT NULL DEFAULT '0' COMMENT '7日亏损订单数',
  `loss_order_count_30d` int NOT NULL DEFAULT '0' COMMENT '30日亏损订单数',
  `loss_order_count_90d` int NOT NULL DEFAULT '0' COMMENT '90日亏损订单数',
  `loss_order_count_180d` int NOT NULL DEFAULT '0' COMMENT '180日亏损订单数',
  `average_profit` decimal(20,8) NOT NULL DEFAULT '0.********' COMMENT '平均盈利',
  `average_profit_7d` decimal(20,8) NOT NULL DEFAULT '0.********' COMMENT '7日平均盈利',
  `average_profit_30d` decimal(20,8) NOT NULL DEFAULT '0.********' COMMENT '30日平均盈利',
  `average_profit_90d` decimal(20,8) NOT NULL DEFAULT '0.********' COMMENT '90日平均盈利',
  `average_profit_180d` decimal(20,8) NOT NULL DEFAULT '0.********' COMMENT '180日平均盈利',
  `average_loss` decimal(20,8) NOT NULL DEFAULT '0.********' COMMENT '平均亏损',
  `average_loss_7d` decimal(20,8) NOT NULL DEFAULT '0.********' COMMENT '7日平均亏损',
  `average_loss_30d` decimal(20,8) NOT NULL DEFAULT '0.********' COMMENT '30日平均亏损',
  `average_loss_90d` decimal(20,8) NOT NULL DEFAULT '0.********' COMMENT '90日平均亏损',
  `average_loss_180d` decimal(20,8) NOT NULL DEFAULT '0.********' COMMENT '180日平均亏损',
  `profit` decimal(20,8) NOT NULL DEFAULT '0.********' COMMENT '盈亏金额',
  `profit_7d` decimal(20,8) NOT NULL DEFAULT '0.********' COMMENT '7日盈亏金额',
  `profit_30d` decimal(20,8) NOT NULL DEFAULT '0.********' COMMENT '30日盈亏金额',
  `profit_90d` decimal(20,8) NOT NULL DEFAULT '0.********' COMMENT '90日盈亏金额',
  `profit_180d` decimal(20,8) NOT NULL DEFAULT '0.********' COMMENT '180日盈亏金额',
  `win_rate` decimal(8,2) NOT NULL DEFAULT '0.00' COMMENT '胜率 %',
  `win_rate_7d` decimal(8,2) NOT NULL DEFAULT '0.00' COMMENT '7日胜率 %',
  `win_rate_30d` decimal(8,2) NOT NULL DEFAULT '0.00' COMMENT '30日胜率 %',
  `win_rate_90d` decimal(8,2) NOT NULL DEFAULT '0.00' COMMENT '90日胜率 %',
  `win_rate_180d` decimal(8,2) NOT NULL DEFAULT '0.00' COMMENT '180日胜率 %',
  `profit_rate` decimal(8,2) NOT NULL DEFAULT '0.00' COMMENT '收益率 %',
  `profit_rate_7d` decimal(8,2) NOT NULL DEFAULT '0.00' COMMENT '7日收益率 %',
  `profit_rate_30d` decimal(8,2) NOT NULL DEFAULT '0.00' COMMENT '30日收益率 %',
  `profit_rate_90d` decimal(8,2) NOT NULL DEFAULT '0.00' COMMENT '90日收益率 %',
  `profit_rate_180d` decimal(8,2) NOT NULL DEFAULT '0.00' COMMENT '180日收益率 %',
  `follower_profit` decimal(20,8) NOT NULL DEFAULT '0.********' COMMENT '跟单者收益',
  `follower_profit_7d` decimal(20,8) NOT NULL DEFAULT '0.********' COMMENT '7日跟单者收益',
  `follower_profit_30d` decimal(20,8) NOT NULL DEFAULT '0.********' COMMENT '30日跟单者收益',
  `follower_profit_90d` decimal(20,8) NOT NULL DEFAULT '0.********' COMMENT '90日跟单者收益',
  `follower_profit_180d` decimal(20,8) NOT NULL DEFAULT '0.********' COMMENT '180日跟单者收益',
  `max_drawdown` decimal(20,8) NOT NULL DEFAULT '0.********' COMMENT '最大回撤',
  `max_drawdown_7d` decimal(20,8) NOT NULL DEFAULT '0.********' COMMENT '7日最大回撤',
  `max_drawdown_30d` decimal(20,8) NOT NULL DEFAULT '0.********' COMMENT '30日最大回撤',
  `max_drawdown_90d` decimal(20,8) NOT NULL DEFAULT '0.********' COMMENT '90日最大回撤',
  `max_drawdown_180d` decimal(20,8) NOT NULL DEFAULT '0.********' COMMENT '180日最大回撤',
  `aum` decimal(20,8) NOT NULL DEFAULT '0.********' COMMENT '资产管理规模',
  `trade_frequency` int NOT NULL DEFAULT '0' COMMENT '交易频率',
  `trade_frequency_7d` int NOT NULL DEFAULT '0' COMMENT '7日交易频率',
  `trade_frequency_30d` int NOT NULL DEFAULT '0' COMMENT '30日交易频率',
  `trade_frequency_90d` int NOT NULL DEFAULT '0' COMMENT '90日交易频率',
  `trade_frequency_180d` int NOT NULL DEFAULT '0' COMMENT '180日交易频率',
  `total_follower_count` int NOT NULL DEFAULT '0' COMMENT '累计跟单人数',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `idx_expert` (`expert_id`),
  KEY `idx_expert_user_id` (`expert_user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='合约交易专家统计表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `copy_contract_order`
--

DROP TABLE IF EXISTS `copy_contract_order`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `copy_contract_order` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `follower_user_id` bigint unsigned NOT NULL COMMENT '跟单者用户ID',
  `expert_id` bigint unsigned NOT NULL COMMENT '专家ID',
  `expert_user_id` bigint unsigned NOT NULL COMMENT '专家用户ID（冗余字段）',
  `mode` tinyint NOT NULL COMMENT '跟单模式：1-智能比例，2-多元探索',
  `expert_order_id` bigint unsigned NOT NULL COMMENT '专家订单ID（关联trade_perpetual_order）',
  `expert_position_id` bigint unsigned NOT NULL COMMENT '专家仓位ID（关联trade_perpetual_position）',
  `follower_order_id` bigint unsigned NOT NULL COMMENT '跟单者订单ID（关联trade_perpetual_order）',
  `follower_position_id` bigint unsigned NOT NULL COMMENT '跟单者仓位ID（关联trade_perpetual_position）',
  `profit_sharing_rate` decimal(8,2) NOT NULL COMMENT '分润比例 %',
  `is_exclusive` tinyint NOT NULL DEFAULT '0' COMMENT '是否尊享模式：1-是，0-否',
  `copy_settings_snapshot` json NOT NULL COMMENT '跟单配置参数快照',
  `status` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '0无效 1有效 跟单用户失败的情况下不会更新这个字段',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_follower_user_id` (`follower_user_id`),
  KEY `idx_expert` (`expert_id`),
  KEY `idx_expert_user_id` (`expert_user_id`),
  KEY `idx_expert_order_id` (`expert_order_id`,`expert_position_id`),
  KEY `idx_follower_order_id` (`follower_order_id`,`follower_position_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='合约跟单记录表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `copy_contract_position`
--

DROP TABLE IF EXISTS `copy_contract_position`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `copy_contract_position` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `follower_user_id` bigint unsigned NOT NULL COMMENT '跟单者用户ID',
  `expert_id` bigint unsigned NOT NULL COMMENT '专家ID',
  `expert_user_id` bigint unsigned NOT NULL COMMENT '专家用户ID（冗余字段）',
  `expert_position_id` bigint unsigned NOT NULL COMMENT '专家仓位ID（关联trade_perpetual_position）',
  `follower_position_id` bigint unsigned NOT NULL COMMENT '跟单者仓位ID（关联trade_perpetual_position）',
  `profit_sharing_rate` decimal(8,2) NOT NULL COMMENT '分润比例 %',
  `is_exclusive` tinyint NOT NULL DEFAULT '0' COMMENT '是否尊享模式：1-是，0-否',
  `copy_settings_snapshot` json NOT NULL COMMENT '跟单配置参数快照',
  `status` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '跟单状态 0失败 1成功',
  `open_order_id` bigint DEFAULT NULL COMMENT '跟单用户开单订单id',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_follower_user_id` (`follower_user_id`),
  KEY `idx_expert` (`expert_id`),
  KEY `idx_expert_user_id` (`expert_user_id`),
  KEY `idx_expert_position_id` (`expert_position_id`),
  KEY `idx_follower_position_id` (`follower_position_id`),
  KEY `idx_cid` (`follower_user_id`,`open_order_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=15 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='合约跟单仓位记录表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `copy_contract_profit_sharing`
--

DROP TABLE IF EXISTS `copy_contract_profit_sharing`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `copy_contract_profit_sharing` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `expert_id` bigint unsigned NOT NULL COMMENT '专家ID',
  `expert_user_id` bigint unsigned NOT NULL COMMENT '专家用户ID（冗余字段）',
  `follower_user_id` bigint unsigned NOT NULL COMMENT '跟单者用户ID',
  `copy_position_id` bigint unsigned NOT NULL COMMENT '跟单仓位记录ID',
  `expert_position_id` bigint unsigned NOT NULL COMMENT '专家仓位ID（关联trade_perpetual_position）',
  `follower_position_id` bigint unsigned NOT NULL COMMENT '跟单者仓位ID（关联trade_perpetual_position）',
  `profit` decimal(20,8) NOT NULL COMMENT '盈亏金额',
  `profit_sharing` decimal(20,8) NOT NULL COMMENT '分润金额',
  `profit_sharing_rate` decimal(8,2) NOT NULL COMMENT '分润比例 %',
  `currency_id` bigint unsigned NOT NULL COMMENT '分润币种ID（默认USDT）',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_expert` (`expert_id`),
  KEY `idx_expert_user_id` (`expert_user_id`),
  KEY `idx_follower_user_id` (`follower_user_id`),
  KEY `idx_copy_position_id` (`copy_position_id`),
  KEY `idx_expert_position_id` (`expert_position_id`),
  KEY `idx_follower_position_id` (`follower_position_id`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='合约分润记录表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `copy_contract_user_setting`
--

DROP TABLE IF EXISTS `copy_contract_user_setting`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `copy_contract_user_setting` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `follower_user_id` bigint unsigned NOT NULL COMMENT '跟单者用户ID',
  `expert_id` bigint unsigned NOT NULL COMMENT '专家ID',
  `expert_user_id` bigint unsigned NOT NULL COMMENT '专家用户ID（冗余字段）',
  `mode` tinyint NOT NULL COMMENT '跟单模式：1-智能比例，2-多元探索',
  `investment_amount` decimal(20,8) DEFAULT NULL COMMENT '投资金额（USDT）（智能比例模式）',
  `copy_type` tinyint DEFAULT NULL COMMENT '跟单方式：1-固定额度，2-倍率（多元探索模式）',
  `fixed_amount` decimal(20,8) DEFAULT NULL COMMENT '固定额度（USDT）（多元探索模式）',
  `rate` decimal(8,2) DEFAULT NULL COMMENT '倍率 %（多元探索模式）',
  `stop_loss_rate` decimal(8,2) DEFAULT NULL COMMENT '止损比例 %',
  `take_profit_rate` decimal(8,2) DEFAULT NULL COMMENT '止盈比例 %',
  `max_follow_amount` decimal(20,8) DEFAULT NULL COMMENT '最大跟随金额',
  `slippage_rate` decimal(8,2) DEFAULT NULL COMMENT '滑点比例 %',
  `auto_new_pairs` tinyint NOT NULL DEFAULT '0' COMMENT '自动跟随新币对：1-是，0-否',
  `is_exclusive` tinyint NOT NULL DEFAULT '0' COMMENT '是否尊享模式：1-是，0-否',
  `copy_currencies` json DEFAULT NULL COMMENT '跟单币种配置（支持多币种）',
  `net_value_guardian` tinyint NOT NULL DEFAULT '0' COMMENT '净值守护者：1-开启，0-关闭（智能比例模式）',
  `max_loss_amount` decimal(20,8) DEFAULT NULL COMMENT '最大亏损金额（触发则解除跟单）（智能比例模式）',
  `min_net_value` decimal(20,8) DEFAULT NULL COMMENT '最小净值金额（触发则解除跟单）（智能比例模式）',
  `copy_all_positions` tinyint NOT NULL DEFAULT '0' COMMENT '跟单后是否复制全部仓位：1-是，0-否（智能比例模式）',
  `margin_mode` tinyint NOT NULL DEFAULT '1' COMMENT '保证金模式：1-跟随专家，2-全仓，3-逐仓',
  `leverage_mode` tinyint NOT NULL DEFAULT '1' COMMENT '杠杆设置：1-跟随专家，2-指定杠杆',
  `custom_leverage` int DEFAULT NULL COMMENT '自定义杠杆倍数（仅当leverage_mode=2时有效）',
  `status` tinyint NOT NULL DEFAULT '1' COMMENT '状态：1-跟单中，2-暂停',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_follower_expert` (`follower_user_id`,`expert_id`),
  KEY `idx_expert` (`expert_id`),
  KEY `idx_expert_user_id` (`expert_user_id`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户合约跟单配置表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `copy_exclusive_invitation`
--

DROP TABLE IF EXISTS `copy_exclusive_invitation`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `copy_exclusive_invitation` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `expert_id` bigint unsigned NOT NULL COMMENT '专家ID',
  `expert_type` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '专家模型类名（多态关联）',
  `expert_user_id` bigint unsigned NOT NULL COMMENT '专家用户ID（冗余字段）',
  `type` tinyint NOT NULL COMMENT '专家类型：1-合约，2-现货',
  `title` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '链接标题',
  `invite_code` varchar(32) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '邀请码',
  `max_count` int DEFAULT NULL COMMENT '最大邀请人数',
  `current_count` int DEFAULT '0' COMMENT '当前邀请人数',
  `profit_sharing_rate` decimal(8,2) DEFAULT NULL COMMENT '分润比例 %',
  `expired_at` timestamp NULL DEFAULT NULL COMMENT '过期时间',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_invite_code` (`invite_code`),
  KEY `idx_expert` (`expert_id`,`expert_type`),
  KEY `idx_expert_user_id` (`expert_user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='尊享模式邀请表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `copy_exclusive_member`
--

DROP TABLE IF EXISTS `copy_exclusive_member`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `copy_exclusive_member` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `invitation_id` bigint unsigned NOT NULL COMMENT '邀请记录ID',
  `expert_id` bigint unsigned NOT NULL COMMENT '专家ID',
  `expert_type` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '专家模型类名（多态关联）',
  `expert_user_id` bigint unsigned NOT NULL COMMENT '专家用户ID（冗余字段）',
  `type` tinyint NOT NULL COMMENT '专家类型：1-合约，2-现货',
  `follower_user_id` bigint unsigned NOT NULL COMMENT '跟单者用户ID',
  `profit_sharing_rate` decimal(8,2) NOT NULL COMMENT '分润比例（可单独修改）%',
  `joined_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '加入时间',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_invitation_follower` (`invitation_id`,`follower_user_id`),
  KEY `idx_expert` (`expert_id`,`expert_type`),
  KEY `idx_expert_user_id` (`expert_user_id`),
  KEY `idx_follower_user_id` (`follower_user_id`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='尊享模式成员表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `copy_expert_level`
--

DROP TABLE IF EXISTS `copy_expert_level`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `copy_expert_level` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `type` tinyint NOT NULL COMMENT '类型：1-合约，2-现货',
  `level` int NOT NULL COMMENT '等级',
  `name` json NOT NULL COMMENT '等级名称',
  `icon` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '等级图标',
  `condition_amount` decimal(20,8) NOT NULL COMMENT '条件一：带单金额（USDT）',
  `condition_follow_amount` decimal(20,8) DEFAULT NULL COMMENT '条件二：跟单者总跟单资金（USDT）',
  `condition_follow_count` int DEFAULT NULL COMMENT '条件二：跟单交易人数（人）',
  `max_follow_count` int NOT NULL COMMENT '最大可带单人数',
  `max_profit_rate` decimal(8,2) NOT NULL COMMENT '最大分润比例 %',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_type_level` (`type`,`level`)
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='交易专家等级表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `copy_feedback`
--

DROP TABLE IF EXISTS `copy_feedback`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `copy_feedback` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `expert_id` bigint unsigned NOT NULL COMMENT '专家ID',
  `expert_type` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '专家模型类名（多态关联）',
  `expert_user_id` bigint unsigned NOT NULL COMMENT '专家用户ID（冗余字段）',
  `type` tinyint NOT NULL COMMENT '专家类型：1-合约，2-现货',
  `feedback_type` tinyint NOT NULL COMMENT '反馈类型：1-问题反馈，2-身份撤销',
  `problem_type` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '问题类型',
  `content` text COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '反馈内容',
  `refund_account_type` tinyint DEFAULT NULL COMMENT '资金退回账户类型：1-现货账户，2-合约账户（仅合约专家撤销）',
  `refund_amount` decimal(20,8) DEFAULT NULL COMMENT '退回金额（仅身份撤销）',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_expert` (`expert_id`,`expert_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='问题反馈表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `copy_follow`
--

DROP TABLE IF EXISTS `copy_follow`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `copy_follow` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `user_id` bigint unsigned NOT NULL COMMENT '关注者用户ID',
  `expert_id` bigint unsigned NOT NULL COMMENT '专家ID',
  `expert_type` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '专家模型类名（多态关联）',
  `expert_user_id` bigint unsigned NOT NULL COMMENT '专家用户ID（冗余字段）',
  `type` tinyint NOT NULL COMMENT '专家类型：1-合约，2-现货',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_user_expert` (`user_id`,`expert_id`,`type`),
  KEY `idx_expert` (`expert_id`,`expert_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='关注表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `copy_profit_rate_log`
--

DROP TABLE IF EXISTS `copy_profit_rate_log`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `copy_profit_rate_log` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `expert_id` bigint unsigned NOT NULL COMMENT '专家ID',
  `expert_type` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '专家模型类名（多态关联）',
  `expert_user_id` bigint unsigned NOT NULL COMMENT '专家用户ID（冗余字段）',
  `type` tinyint NOT NULL COMMENT '专家类型：1-合约，2-现货',
  `old_rate` decimal(8,2) DEFAULT NULL COMMENT '原分润比例 %',
  `new_rate` decimal(8,2) NOT NULL COMMENT '新分润比例 %',
  `is_exclusive` tinyint NOT NULL DEFAULT '0' COMMENT '是否尊享模式：1-是，0-否',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_expert` (`expert_id`,`expert_type`),
  KEY `idx_expert_user_id` (`expert_user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='分润比例修改记录表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `copy_spot_advanced_setting`
--

DROP TABLE IF EXISTS `copy_spot_advanced_setting`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `copy_spot_advanced_setting` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `follower_user_id` bigint unsigned NOT NULL COMMENT '跟单者用户ID',
  `expert_id` bigint unsigned NOT NULL COMMENT '专家ID',
  `expert_user_id` bigint unsigned NOT NULL COMMENT '专家用户ID（冗余字段）',
  `currency_id` bigint unsigned NOT NULL COMMENT '币种ID',
  `copy_type` tinyint NOT NULL COMMENT '跟单方式：1-固定额度，2-倍率',
  `fixed_amount` decimal(20,8) DEFAULT NULL COMMENT '固定额度（USDT）',
  `rate` decimal(8,2) DEFAULT NULL COMMENT '倍率 %',
  `stop_loss_rate` decimal(8,2) DEFAULT NULL COMMENT '止损比例 %',
  `take_profit_rate` decimal(8,2) DEFAULT NULL COMMENT '止盈比例 %',
  `max_follow_amount` decimal(20,8) DEFAULT NULL COMMENT '最大跟随金额',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_follower_expert_currency` (`follower_user_id`,`expert_id`,`currency_id`),
  KEY `idx_expert` (`expert_id`),
  KEY `idx_expert_user_id` (`expert_user_id`),
  KEY `idx_currency_id` (`currency_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='现货多元探索跟单高级设置表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `copy_spot_expert`
--

DROP TABLE IF EXISTS `copy_spot_expert`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `copy_spot_expert` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `user_id` bigint unsigned NOT NULL COMMENT '用户ID',
  `introduction` text COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '个人介绍',
  `status` tinyint NOT NULL DEFAULT '1' COMMENT '申请状态：1-待审核，2-审核通过，3-审核拒绝',
  `review_remark` varchar(500) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '审核备注',
  `reviewed_at` timestamp NULL DEFAULT NULL COMMENT '审核时间',
  `reviewed_by` bigint unsigned DEFAULT NULL COMMENT '审核人ID',
  `is_active` tinyint NOT NULL DEFAULT '0' COMMENT '是否开启现货带单：1-是，0-否',
  `show_total_assets` tinyint NOT NULL DEFAULT '0' COMMENT '是否展示总资产：1-是，0-否',
  `show_fund_composition` tinyint NOT NULL DEFAULT '0' COMMENT '是否展示资金构成：1-是，0-否',
  `new_currency_auto_copy` tinyint NOT NULL DEFAULT '0' COMMENT '新上线的交易对自动开启带单：1-是，0-否',
  `position_protection` tinyint NOT NULL DEFAULT '1' COMMENT '未结仓位保护：1-开启，0-关闭',
  `min_follow_amount` decimal(20,8) DEFAULT NULL COMMENT '最小跟单金额（USDT）',
  `recommend_params` json DEFAULT NULL COMMENT '推荐参数配置',
  `currency_ids` json DEFAULT NULL COMMENT '跟单币种 id 配置（支持多币种）',
  `profit_sharing_rate` decimal(8,2) NOT NULL DEFAULT '0.00' COMMENT '分润比例 %',
  `level_id` bigint unsigned DEFAULT NULL COMMENT '专家等级ID',
  `exclusive_mode` tinyint NOT NULL DEFAULT '0' COMMENT '尊享模式：1-开启，0-关闭',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_user_id` (`user_id`),
  KEY `idx_status` (`status`),
  KEY `idx_is_active` (`is_active`),
  KEY `idx_level_id` (`level_id`),
  KEY `idx_exclusive_mode` (`exclusive_mode`)
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='现货交易专家表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `copy_spot_expert_statistics`
--

DROP TABLE IF EXISTS `copy_spot_expert_statistics`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `copy_spot_expert_statistics` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `expert_id` bigint unsigned NOT NULL COMMENT '专家ID',
  `expert_user_id` bigint unsigned NOT NULL COMMENT '专家用户ID（冗余字段）',
  `profit` decimal(20,8) NOT NULL DEFAULT '0.********' COMMENT '收益金额',
  `profit_7d` decimal(20,8) NOT NULL DEFAULT '0.********' COMMENT '7日收益金额',
  `profit_30d` decimal(20,8) NOT NULL DEFAULT '0.********' COMMENT '30日收益金额',
  `profit_90d` decimal(20,8) NOT NULL DEFAULT '0.********' COMMENT '90日收益金额',
  `profit_180d` decimal(20,8) NOT NULL DEFAULT '0.********' COMMENT '180日收益金额',
  `win_rate` decimal(8,2) NOT NULL DEFAULT '0.00' COMMENT '胜率 %',
  `win_rate_7d` decimal(8,2) NOT NULL DEFAULT '0.00' COMMENT '7日胜率 %',
  `win_rate_30d` decimal(8,2) NOT NULL DEFAULT '0.00' COMMENT '30日胜率 %',
  `win_rate_90d` decimal(8,2) NOT NULL DEFAULT '0.00' COMMENT '90日胜率 %',
  `win_rate_180d` decimal(8,2) NOT NULL DEFAULT '0.00' COMMENT '180日胜率 %',
  `profit_rate` decimal(8,2) NOT NULL DEFAULT '0.00' COMMENT '收益率 %',
  `profit_rate_7d` decimal(8,2) NOT NULL DEFAULT '0.00' COMMENT '7日收益率 %',
  `profit_rate_30d` decimal(8,2) NOT NULL DEFAULT '0.00' COMMENT '30日收益率 %',
  `profit_rate_90d` decimal(8,2) NOT NULL DEFAULT '0.00' COMMENT '90日收益率 %',
  `profit_rate_180d` decimal(8,2) NOT NULL DEFAULT '0.00' COMMENT '180日收益率 %',
  `follower_profit` decimal(20,8) NOT NULL DEFAULT '0.********' COMMENT '跟单者收益',
  `follower_profit_7d` decimal(20,8) NOT NULL DEFAULT '0.********' COMMENT '7日跟单者收益',
  `follower_profit_30d` decimal(20,8) NOT NULL DEFAULT '0.********' COMMENT '30日跟单者收益',
  `follower_profit_90d` decimal(20,8) NOT NULL DEFAULT '0.********' COMMENT '90日跟单者收益',
  `follower_profit_180d` decimal(20,8) NOT NULL DEFAULT '0.********' COMMENT '180日跟单者收益',
  `aum` decimal(20,8) NOT NULL DEFAULT '0.********' COMMENT '资产管理规模',
  `trade_frequency` int NOT NULL DEFAULT '0' COMMENT '交易频率',
  `trade_frequency_7d` int NOT NULL DEFAULT '0' COMMENT '7日交易频率',
  `trade_frequency_30d` int NOT NULL DEFAULT '0' COMMENT '30日交易频率',
  `trade_frequency_90d` int NOT NULL DEFAULT '0' COMMENT '90日交易频率',
  `trade_frequency_180d` int NOT NULL DEFAULT '0' COMMENT '180日交易频率',
  `total_follower_count` int NOT NULL DEFAULT '0' COMMENT '累计跟单人数',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `idx_expert` (`expert_id`),
  KEY `idx_expert_user_id` (`expert_user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='现货交易专家统计表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `copy_spot_order`
--

DROP TABLE IF EXISTS `copy_spot_order`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `copy_spot_order` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `follower_user_id` bigint unsigned NOT NULL COMMENT '跟单者用户ID',
  `expert_id` bigint unsigned NOT NULL COMMENT '专家ID',
  `expert_user_id` bigint unsigned NOT NULL COMMENT '专家用户ID（冗余字段）',
  `mode` tinyint NOT NULL COMMENT '跟单模式：1-智能比例，2-多元探索',
  `expert_order_id` bigint unsigned NOT NULL COMMENT '专家订单ID（关联trade_spot_order）',
  `follower_order_id` bigint unsigned NOT NULL COMMENT '跟单者订单ID（关联trade_spot_order）',
  `profit_sharing_rate` decimal(8,2) NOT NULL COMMENT '分润比例 %',
  `is_exclusive` tinyint NOT NULL DEFAULT '0' COMMENT '是否尊享模式：1-是，0-否',
  `copy_settings_snapshot` json NOT NULL COMMENT '跟单配置参数快照',
  `status` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '0买入 1已卖出',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_follower_user_id` (`follower_user_id`),
  KEY `idx_expert` (`expert_id`),
  KEY `idx_expert_user_id` (`expert_user_id`),
  KEY `idx_expert_order_id` (`expert_order_id`),
  KEY `idx_follower_order_id` (`follower_order_id`)
) ENGINE=InnoDB AUTO_INCREMENT=25 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='现货跟单记录表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `copy_spot_position`
--

DROP TABLE IF EXISTS `copy_spot_position`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `copy_spot_position` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `follower_user_id` int unsigned NOT NULL COMMENT '跟单者用户ID',
  `expert_id` int unsigned NOT NULL COMMENT '专家ID',
  `expert_user_id` int unsigned NOT NULL COMMENT '专家用户ID（冗余字段）',
  `currency_id` int unsigned NOT NULL COMMENT '币种ID',
  `total_buy_quantity` decimal(20,8) NOT NULL DEFAULT '0.********' COMMENT '总买入数量',
  `total_sell_quantity` decimal(20,8) NOT NULL DEFAULT '0.********' COMMENT '总卖出数量',
  `available_quantity` decimal(20,8) NOT NULL DEFAULT '0.********' COMMENT '可用持仓数量',
  `frozen_quantity` decimal(20,8) NOT NULL DEFAULT '0.********' COMMENT '冻结数量（平仓中）',
  `avg_buy_price` decimal(20,8) NOT NULL DEFAULT '0.********' COMMENT '平均买入价格',
  `total_buy_amount` decimal(20,8) NOT NULL DEFAULT '0.********' COMMENT '总买入金额',
  `avg_sell_price` decimal(20,8) NOT NULL DEFAULT '0.********' COMMENT '平均卖出价格',
  `total_sell_amount` decimal(20,8) NOT NULL DEFAULT '0.********' COMMENT '总卖出金额',
  `realized_pnl` decimal(20,8) NOT NULL DEFAULT '0.********' COMMENT '已实现盈亏',
  `status` tinyint unsigned NOT NULL DEFAULT '1' COMMENT '状态：1-持仓中，2-已平仓',
  `created_at` timestamp NULL DEFAULT NULL COMMENT '创建时间',
  `updated_at` timestamp NULL DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_expert_currency` (`expert_id`,`currency_id`),
  KEY `idx_follower_user` (`follower_user_id`),
  KEY `idx_status` (`status`),
  KEY `uk_follower_expert_currency` (`follower_user_id`,`expert_id`,`currency_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=27 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='现货跟单持仓表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `copy_spot_profit_sharing`
--

DROP TABLE IF EXISTS `copy_spot_profit_sharing`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `copy_spot_profit_sharing` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `expert_id` bigint unsigned NOT NULL COMMENT '专家ID',
  `expert_user_id` bigint unsigned NOT NULL COMMENT '专家用户ID（冗余字段）',
  `follower_user_id` bigint unsigned NOT NULL COMMENT '跟单者用户ID',
  `copy_order_id` bigint unsigned NOT NULL COMMENT '跟单记录ID',
  `expert_order_id` bigint unsigned NOT NULL COMMENT '专家订单ID（关联trade_spot_order）',
  `follower_order_id` bigint unsigned NOT NULL COMMENT '跟单者订单ID（关联trade_spot_order）',
  `profit` decimal(20,8) NOT NULL COMMENT '盈亏金额',
  `profit_sharing` decimal(20,8) NOT NULL COMMENT '分润金额',
  `profit_sharing_rate` decimal(8,2) NOT NULL COMMENT '分润比例 %',
  `currency_id` bigint unsigned NOT NULL COMMENT '分润币种ID（默认USDT）',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_expert` (`expert_id`),
  KEY `idx_expert_user_id` (`expert_user_id`),
  KEY `idx_follower_user_id` (`follower_user_id`),
  KEY `idx_copy_order_id` (`copy_order_id`),
  KEY `idx_expert_order_id` (`expert_order_id`),
  KEY `idx_follower_order_id` (`follower_order_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='现货分润记录表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `copy_spot_user_setting`
--

DROP TABLE IF EXISTS `copy_spot_user_setting`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `copy_spot_user_setting` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `follower_user_id` bigint unsigned NOT NULL COMMENT '跟单者用户ID',
  `expert_id` bigint unsigned NOT NULL COMMENT '专家ID',
  `expert_user_id` bigint unsigned NOT NULL COMMENT '专家用户ID（冗余字段）',
  `copy_type` tinyint unsigned NOT NULL DEFAULT '2' COMMENT '1额度 2比例百分比',
  `fixed_amount` decimal(20,8) DEFAULT NULL COMMENT '固定额度（USDT）',
  `rate` decimal(8,2) DEFAULT NULL COMMENT '倍率 %',
  `stop_loss_rate` decimal(8,2) DEFAULT NULL COMMENT '止损比例 %',
  `take_profit_rate` decimal(8,2) DEFAULT NULL COMMENT '止盈比例 %',
  `max_follow_amount` decimal(20,8) DEFAULT NULL COMMENT '最大跟随金额',
  `auto_new_pairs` tinyint NOT NULL DEFAULT '0' COMMENT '自动跟随新币对：1-是，0-否',
  `is_exclusive` tinyint NOT NULL DEFAULT '0' COMMENT '是否尊享模式：1-是，0-否',
  `copy_currencies` json DEFAULT NULL COMMENT '跟单币种配置（支持多币种）',
  `status` tinyint NOT NULL DEFAULT '1' COMMENT '状态：1-跟单中，2-暂停',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_follower_expert` (`follower_user_id`,`expert_id`),
  KEY `idx_expert` (`expert_id`),
  KEY `idx_expert_user_id` (`expert_user_id`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户现货跟单配置表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `cpx_agent`
--

DROP TABLE IF EXISTS `cpx_agent`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `cpx_agent` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `user_id` bigint NOT NULL COMMENT '代理商用户ID',
  `parent_agent_id` bigint DEFAULT NULL COMMENT '上级代理商ID（如果在用户表中上级不是代理则这里为null）',
  `spot_commission_rate` decimal(8,6) NOT NULL DEFAULT '0.000000' COMMENT '现货返佣比例',
  `contract_commission_rate` decimal(8,6) NOT NULL DEFAULT '0.000000' COMMENT '合约返佣比例',
  `trader_spot_commission_rate` decimal(8,6) NOT NULL DEFAULT '0.000000' COMMENT '交易员现货返佣比例',
  `trader_contract_commission_rate` decimal(8,6) NOT NULL DEFAULT '0.000000' COMMENT '交易员合约返佣比例',
  `status` tinyint NOT NULL DEFAULT '1' COMMENT '状态:0=禁用,1=正常',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `cpx_agent_user_id_unique` (`user_id`),
  KEY `cpx_agent_parent_agent_id_index` (`parent_agent_id`),
  KEY `cpx_agent_status_index` (`status`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='代理商表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `cpx_agent_application`
--

DROP TABLE IF EXISTS `cpx_agent_application`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `cpx_agent_application` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `user_id` bigint NOT NULL COMMENT '申请用户ID',
  `company_name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '公司名称',
  `contact_person` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '联系人',
  `contact_phone` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '联系电话',
  `contact_email` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '联系邮箱',
  `business_license` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '营业执照',
  `business_description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci COMMENT '业务描述',
  `marketing_plan` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci COMMENT '营销计划',
  `expected_monthly_volume` decimal(20,8) NOT NULL DEFAULT '0.********' COMMENT '预期月交易量',
  `expected_user_count` int NOT NULL DEFAULT '0' COMMENT '预期用户数量',
  `contact_method` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '联系方法 Telegram、WhatsApp、KaKaoTalk、LINE、Wechat',
  `contact_account` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '联系账号',
  `additional_info` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci COMMENT '附加信息',
  `status` tinyint NOT NULL DEFAULT '0' COMMENT '申请状态:0=待审核,1=已通过,2=已拒绝',
  `rejection_reason` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci COMMENT '拒绝原因',
  `submitted_at` timestamp NOT NULL COMMENT '提交时间',
  `reviewed_at` timestamp NULL DEFAULT NULL COMMENT '审核时间',
  `reviewer_id` bigint DEFAULT NULL COMMENT '审核员ID',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `cpx_agent_application_user_id_unique` (`user_id`) USING BTREE,
  KEY `cpx_agent_application_status_index` (`status`) USING BTREE,
  KEY `cpx_agent_application_submitted_at_index` (`submitted_at`) USING BTREE,
  KEY `cpx_agent_application_reviewed_at_index` (`reviewed_at`) USING BTREE,
  KEY `cpx_agent_application_reviewer_id_index` (`reviewer_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='代理商申请表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `cpx_agent_client`
--

DROP TABLE IF EXISTS `cpx_agent_client`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `cpx_agent_client` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `agent_id` bigint NOT NULL COMMENT '代理商ID',
  `invite_code_id` bigint NOT NULL COMMENT '邀请码ID',
  `user_id` bigint NOT NULL COMMENT '客户用户ID',
  `spot_commission_rate` decimal(8,6) NOT NULL DEFAULT '0.000000' COMMENT '现货返佣比例',
  `contract_commission_rate` decimal(8,6) NOT NULL DEFAULT '0.000000' COMMENT '合约返佣比例',
  `trader_spot_commission_rate` decimal(8,6) NOT NULL DEFAULT '0.000000' COMMENT '交易员现货返佣比例',
  `trader_contract_commission_rate` decimal(8,6) NOT NULL DEFAULT '0.000000' COMMENT '交易员合约返佣比例',
  `remark` text COLLATE utf8mb4_unicode_ci COMMENT '备注',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `cpx_agent_client_agent_id_index` (`agent_id`),
  KEY `cpx_agent_client_invite_code_id_index` (`invite_code_id`),
  KEY `cpx_agent_client_user_id_index` (`user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='代理商直推用户(直客)表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `cpx_agent_commission_income`
--

DROP TABLE IF EXISTS `cpx_agent_commission_income`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `cpx_agent_commission_income` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `agent_id` bigint NOT NULL COMMENT '代理商ID',
  `sub_agent_id` bigint DEFAULT NULL COMMENT '下级代理商ID（如果开单用户为下级代理）',
  `agent_client_id` bigint DEFAULT NULL COMMENT '代理商直客ID（如果开单用户为直客）',
  `trade_user_id` bigint NOT NULL COMMENT '开单用户ID',
  `order_id` bigint NOT NULL COMMENT '订单ID',
  `trade_type` tinyint NOT NULL COMMENT '交易类型:1=现货,2=合约,3=现货杠杆',
  `trade_amount` decimal(20,8) NOT NULL COMMENT '交易金额',
  `fee_amount` decimal(20,8) NOT NULL COMMENT '手续费金额',
  `commission_income_rate` decimal(8,6) NOT NULL COMMENT '返佣收益比例 = 代理商返佣比例 - 下级返佣比例',
  `commission_income_amount` decimal(20,8) NOT NULL COMMENT '返佣收益金额',
  `status` tinyint NOT NULL DEFAULT '0' COMMENT '状态:0=待结算,1=已结算,2=已取消',
  `trade_time` timestamp NOT NULL COMMENT '交易时间',
  `settled_at` timestamp NULL DEFAULT NULL COMMENT '结算时间',
  `remark` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci COMMENT '备注',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  KEY `cpx_agent_commission_income_agent_id_status_index` (`agent_id`,`status`) USING BTREE,
  KEY `cpx_agent_commission_income_trade_user_id_index` (`trade_user_id`) USING BTREE,
  KEY `cpx_agent_commission_income_order_id_index` (`order_id`) USING BTREE,
  KEY `cpx_agent_commission_income_trade_type_index` (`trade_type`) USING BTREE,
  KEY `cpx_agent_commission_income_trade_time_index` (`trade_time`) USING BTREE,
  KEY `cpx_agent_commission_income_settled_at_index` (`settled_at`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='代理商佣金收益记录表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `cpx_agent_invite_code`
--

DROP TABLE IF EXISTS `cpx_agent_invite_code`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `cpx_agent_invite_code` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `user_id` bigint NOT NULL COMMENT '用户ID',
  `agent_id` bigint NOT NULL COMMENT '代理商ID',
  `invite_code` varchar(32) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '邀请码',
  `spot_commission_rate` decimal(8,6) NOT NULL DEFAULT '0.000000' COMMENT '直客现货返佣比例',
  `contract_commission_rate` decimal(8,6) NOT NULL DEFAULT '0.000000' COMMENT '直客合约返佣比例',
  `trader_spot_commission_rate` decimal(8,6) NOT NULL DEFAULT '0.000000' COMMENT '直客交易员现货返佣比例',
  `trader_contract_commission_rate` decimal(8,6) NOT NULL DEFAULT '0.000000' COMMENT '直客交易员合约返佣比例',
  `is_default` tinyint NOT NULL DEFAULT '0' COMMENT '是否默认:0=否,1=是',
  `click_count` int NOT NULL DEFAULT '0' COMMENT '点击量',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `cpx_agent_invite_code_invite_code_unique` (`invite_code`),
  KEY `cpx_agent_invite_code_agent_id_index` (`agent_id`)
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='代理商邀请码表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `cpx_article`
--

DROP TABLE IF EXISTS `cpx_article`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `cpx_article` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键',
  `category_id` bigint NOT NULL DEFAULT '0' COMMENT '分类ID',
  `title` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '标题',
  `content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '内容',
  `summary` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '摘要',
  `remark` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '备注',
  `currency` varchar(200) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '关联币种',
  `created_by` bigint NOT NULL DEFAULT '0' COMMENT '创建者',
  `updated_by` bigint NOT NULL DEFAULT '0' COMMENT '更新者',
  `created_at` datetime DEFAULT NULL,
  `updated_at` datetime DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=45 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='文章表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `cpx_bank`
--

DROP TABLE IF EXISTS `cpx_bank`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `cpx_bank` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键',
  `name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '银行名称',
  `created_by` bigint NOT NULL DEFAULT '0' COMMENT '创建者',
  `updated_by` bigint NOT NULL DEFAULT '0' COMMENT '更新者',
  `created_at` datetime DEFAULT NULL,
  `updated_at` datetime DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=4 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='银行表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `cpx_category`
--

DROP TABLE IF EXISTS `cpx_category`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `cpx_category` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键',
  `key` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '分类标识',
  `name` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '分类名称',
  `sort` smallint NOT NULL DEFAULT '0' COMMENT '排序',
  `created_by` bigint NOT NULL DEFAULT '0' COMMENT '创建者',
  `updated_by` bigint NOT NULL DEFAULT '0' COMMENT '更新者',
  `created_at` datetime DEFAULT NULL,
  `updated_at` datetime DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=18 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='分类表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `cpx_dynamics`
--

DROP TABLE IF EXISTS `cpx_dynamics`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `cpx_dynamics` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键',
  `pid_id` bigint NOT NULL DEFAULT '0' COMMENT '上级ID',
  `user_id` bigint NOT NULL DEFAULT '0' COMMENT '会员ID',
  `liked` bigint DEFAULT '0' COMMENT '点赞数量',
  `liked_uids` text COLLATE utf8mb4_unicode_ci COMMENT '点赞用户ID',
  `title` text COLLATE utf8mb4_unicode_ci COMMENT '标题',
  `content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '内容',
  `images` text COLLATE utf8mb4_unicode_ci COMMENT '图片',
  `forward_uids` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci COMMENT '转发人 user_id',
  `collect_uids` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci COMMENT '收藏人 user_ids',
  `look_auth` tinyint DEFAULT '1' COMMENT '查看权限\n1、公开，所有人将看到该动态\n2、关注我的，关注您的用户将看到该动态\n3、私密，将保存到您的笔记，仅本人可见',
  `look_num` bigint DEFAULT '0' COMMENT '查看数量',
  `dynamics_currency` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '关联币种ID集合',
  `hot_topic_id` bigint DEFAULT NULL COMMENT '管理话题ID',
  `type` tinyint DEFAULT '1' COMMENT '动态类型，1观点，2文章',
  `created_by` bigint NOT NULL DEFAULT '0' COMMENT '创建者',
  `updated_by` bigint NOT NULL DEFAULT '0' COMMENT '更新者',
  `created_at` datetime DEFAULT NULL,
  `updated_at` datetime DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=9 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='动态表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `cpx_dynamics_currency`
--

DROP TABLE IF EXISTS `cpx_dynamics_currency`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `cpx_dynamics_currency` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键',
  `symbol_id` bigint NOT NULL DEFAULT '0' COMMENT '币种ID',
  `symbol` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '币种标的',
  `url` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci COMMENT '链接',
  `created_by` bigint NOT NULL DEFAULT '0' COMMENT '创建者',
  `updated_by` bigint NOT NULL DEFAULT '0' COMMENT '更新者',
  `created_at` datetime DEFAULT NULL,
  `updated_at` datetime DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=8 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='动态币种表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `cpx_flat_currency`
--

DROP TABLE IF EXISTS `cpx_flat_currency`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `cpx_flat_currency` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键',
  `payment_type` varchar(200) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '0' COMMENT '支付方式 比如，支付宝，微信，多选',
  `title` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '0' COMMENT '名称',
  `icon` varchar(200) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '0' COMMENT '图标',
  `created_by` bigint NOT NULL DEFAULT '0' COMMENT '创建者',
  `updated_by` bigint NOT NULL DEFAULT '0' COMMENT '更新者',
  `created_at` datetime DEFAULT NULL,
  `updated_at` datetime DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='法币表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `cpx_hot_topic`
--

DROP TABLE IF EXISTS `cpx_hot_topic`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `cpx_hot_topic` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键',
  `currency` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci COMMENT '关联币种ID（cpx_dynamics_currency表）',
  `title` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci COMMENT '标题',
  `content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '内容',
  `look_num` bigint DEFAULT '0' COMMENT '查看数量',
  `created_by` bigint NOT NULL DEFAULT '0' COMMENT '创建者',
  `updated_by` bigint NOT NULL DEFAULT '0' COMMENT '更新者',
  `created_at` datetime DEFAULT NULL,
  `updated_at` datetime DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=4 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='热门话题表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `cpx_message`
--

DROP TABLE IF EXISTS `cpx_message`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `cpx_message` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键',
  `category_id` bigint NOT NULL DEFAULT '0' COMMENT '分类ID',
  `user_ids` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci COMMENT '发送用户ID',
  `title` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '标题',
  `content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '内容',
  `created_by` bigint NOT NULL DEFAULT '0' COMMENT '创建者',
  `updated_by` bigint NOT NULL DEFAULT '0' COMMENT '更新者',
  `created_at` datetime DEFAULT NULL,
  `updated_at` datetime DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=4 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='消息表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `cpx_notice`
--

DROP TABLE IF EXISTS `cpx_notice`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `cpx_notice` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键',
  `category_id` bigint NOT NULL DEFAULT '0' COMMENT '分类ID',
  `title` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '标题',
  `content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '内容',
  `look_num` bigint DEFAULT '0' COMMENT '查看数量',
  `created_by` bigint NOT NULL DEFAULT '0' COMMENT '创建者',
  `updated_by` bigint NOT NULL DEFAULT '0' COMMENT '更新者',
  `created_at` datetime DEFAULT NULL,
  `updated_at` datetime DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=42 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='公告表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `cpx_payment`
--

DROP TABLE IF EXISTS `cpx_payment`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `cpx_payment` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键',
  `payment_type` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '支付方式',
  `name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '选项名称',
  `field` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '选项字段',
  `type` tinyint NOT NULL COMMENT '表单类型，比如图片，文本',
  `created_by` bigint NOT NULL DEFAULT '0' COMMENT '创建者',
  `updated_by` bigint NOT NULL DEFAULT '0' COMMENT '更新者',
  `created_at` datetime DEFAULT NULL,
  `updated_at` datetime DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='支付方式表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `cpx_tipoffs`
--

DROP TABLE IF EXISTS `cpx_tipoffs`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `cpx_tipoffs` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键',
  `dynamics_id` bigint NOT NULL DEFAULT '0' COMMENT '动态ID',
  `user_id` bigint NOT NULL DEFAULT '0' COMMENT '会员ID',
  `tipoffs_user_id` bigint DEFAULT NULL COMMENT '被举报人 user_id',
  `content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '举报内容',
  `voucher` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci COMMENT '凭证图片',
  `reason` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci COMMENT '举报理由',
  `created_by` bigint NOT NULL DEFAULT '0' COMMENT '创建者',
  `updated_by` bigint NOT NULL DEFAULT '0' COMMENT '更新者',
  `created_at` datetime DEFAULT NULL,
  `updated_at` datetime DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=10 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='举报表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `cpx_user`
--

DROP TABLE IF EXISTS `cpx_user`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `cpx_user` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '用户ID，主键',
  `parent_id` bigint DEFAULT NULL COMMENT '上级ID',
  `account` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '用户account',
  `username` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '用户名',
  `display_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '展示名称，默认为用户名',
  `email` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '邮箱',
  `phone` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '手机号',
  `phone_country_code` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '手机国家代码',
  `password` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '登录密码',
  `fund_password` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '资金密码',
  `avatar` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '头像URL',
  `invite_code` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '邀请码',
  `register_type` tinyint NOT NULL DEFAULT '1' COMMENT '注册类型:1=邮箱,2=手机,3=苹果,4=谷歌',
  `status` tinyint NOT NULL DEFAULT '1' COMMENT '状态:1=正常,2=禁用,3=注销',
  `last_login_at` timestamp NULL DEFAULT NULL COMMENT '最后登录时间',
  `last_login_ip` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '最后登录IP',
  `last_login_device` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '最后登录设备',
  `google2fa_secret` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '谷歌验证码密钥，加密保存',
  `third_party_auth` json DEFAULT NULL COMMENT '第三方登录认证信息(Apple/Google等)',
  `social_bindings` json DEFAULT NULL COMMENT '社交媒体绑定信息(Telegram/微信/Twitter等)',
  `agent_id` bigint DEFAULT NULL COMMENT '代理商ID（为代理商时写入代理商表ID，方便查询）',
  `agent_client_id` bigint DEFAULT NULL COMMENT '代理商直客ID（为代理商直客时写入代理商直客表ID，方便查询）',
  `contract_expert_id` bigint DEFAULT NULL COMMENT '合约交易专家ID',
  `spot_expert_id` bigint DEFAULT NULL COMMENT '现货交易专家ID ',
  `concern_uids` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci COMMENT '关注的user_ids',
  `introduction` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '个人简介',
  `language` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '语言偏好',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `cpx_user_account_unique` (`account`) USING BTREE,
  UNIQUE KEY `cpx_user_username_unique` (`username`) USING BTREE,
  UNIQUE KEY `cpx_user_display_name_unique` (`display_name`) USING BTREE,
  UNIQUE KEY `cpx_user_invite_code_unique` (`invite_code`) USING BTREE,
  UNIQUE KEY `cpx_user_email_unique` (`email`) USING BTREE,
  UNIQUE KEY `cpx_user_phone_unique` (`phone`) USING BTREE,
  KEY `cpx_user_account_index` (`account`) USING BTREE,
  KEY `cpx_user_invite_code_index` (`invite_code`) USING BTREE,
  KEY `cpx_user_parent_id_index` (`parent_id`) USING BTREE,
  KEY `cpx_user_status_index` (`status`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=6 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='交易所用户表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `cpx_user_device`
--

DROP TABLE IF EXISTS `cpx_user_device`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `cpx_user_device` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `user_id` bigint NOT NULL COMMENT '用户ID',
  `device_id` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '设备唯一标识',
  `device_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '设备名称',
  `device_type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '设备类型:web=网页,ios=苹果,android=安卓',
  `browser` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '浏览器 User-Agent',
  `os` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '操作系统',
  `app_version` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '应用版本',
  `first_login_ip` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '首次登录IP',
  `last_login_ip` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '最后登录IP',
  `first_login_at` timestamp NOT NULL COMMENT '首次登录时间',
  `last_login_at` timestamp NOT NULL COMMENT '最后登录时间',
  `token` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '设备登录令牌',
  `status` tinyint NOT NULL DEFAULT '1' COMMENT '设备状态:1=正常,2=禁用',
  `is_frequently_used` tinyint NOT NULL DEFAULT '0' COMMENT '是否经常使用:0=否,1=是',
  `push_token` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '推送通知令牌',
  `device_info` json DEFAULT NULL COMMENT '设备详细信息',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `cpx_user_device_user_id_device_id_unique` (`user_id`,`device_id`) USING BTREE,
  KEY `cpx_user_device_device_type_index` (`device_type`) USING BTREE,
  KEY `cpx_user_device_status_index` (`status`) USING BTREE,
  KEY `cpx_user_device_is_frequently_used_index` (`is_frequently_used`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=4 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户设备管理表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `cpx_user_fee_commission`
--

DROP TABLE IF EXISTS `cpx_user_fee_commission`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `cpx_user_fee_commission` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `parent_id` bigint DEFAULT NULL COMMENT '上级ID（如果存在上级，则存在此ID）冗余字段',
  `user_id` bigint NOT NULL COMMENT '客户用户ID',
  `agent_id` bigint DEFAULT NULL COMMENT '代理商ID（如果是代理商则存在此ID）冗余字段',
  `agent_client_id` bigint DEFAULT NULL COMMENT '代理商直客ID（如果是代理商直客则存在此ID）冗余字段',
  `order_id` bigint NOT NULL COMMENT '订单ID',
  `trade_type` tinyint NOT NULL COMMENT '交易类型:1=现货,2=合约，3=现货杠杆',
  `trade_amount` decimal(20,8) NOT NULL COMMENT '交易金额',
  `fee_amount` decimal(20,8) NOT NULL COMMENT '手续费金额',
  `commission_rate` decimal(8,6) NOT NULL COMMENT '返佣比例',
  `commission_amount` decimal(20,8) NOT NULL COMMENT '返佣金额',
  `status` tinyint NOT NULL DEFAULT '0' COMMENT '状态:0=待结算,1=已结算,2=已取消',
  `trade_time` timestamp NOT NULL COMMENT '交易时间',
  `settled_at` timestamp NULL DEFAULT NULL COMMENT '结算时间',
  `remark` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci COMMENT '备注',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  KEY `cpx_user_fee_commission_agent_id_status_index` (`agent_id`,`status`) USING BTREE,
  KEY `cpx_user_fee_commission_user_id_index` (`user_id`) USING BTREE,
  KEY `cpx_user_fee_commission_order_id_index` (`order_id`) USING BTREE,
  KEY `cpx_user_fee_commission_trade_type_index` (`trade_type`) USING BTREE,
  KEY `cpx_user_fee_commission_trade_time_index` (`trade_time`) USING BTREE,
  KEY `cpx_user_fee_commission_settled_at_index` (`settled_at`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户手续费返佣记录表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `cpx_user_kyc_verification`
--

DROP TABLE IF EXISTS `cpx_user_kyc_verification`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `cpx_user_kyc_verification` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `user_id` bigint NOT NULL COMMENT '用户ID',
  `first_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '名',
  `middle_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '中间名',
  `last_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '姓',
  `status` tinyint NOT NULL DEFAULT '0' COMMENT '认证状态:0=未提交,1=审核中,2=已通过,3=已拒绝',
  `document_type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '证件类型:passport=护照,id_card=身份证,driver_license=驾驶证',
  `document_number` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '证件号码',
  `document_country` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '证件签发国家',
  `document_expiry_date` date DEFAULT NULL COMMENT '证件到期日期',
  `document_front_image` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '证件正面照片',
  `document_back_image` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '证件背面照片',
  `selfie_image` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '手持证件自拍照',
  `birthday` date DEFAULT NULL COMMENT '生日',
  `rejection_reason` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci COMMENT '拒绝原因',
  `submitted_at` timestamp NULL DEFAULT NULL COMMENT '提交时间',
  `reviewed_at` timestamp NULL DEFAULT NULL COMMENT '审核时间',
  `reviewer_id` bigint DEFAULT NULL COMMENT '审核员ID',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `cpx_user_kyc_verification_user_id_unique` (`user_id`) USING BTREE,
  KEY `cpx_user_kyc_verification_status_index` (`status`) USING BTREE,
  KEY `cpx_user_kyc_verification_document_type_index` (`document_type`) USING BTREE,
  KEY `cpx_user_kyc_verification_submitted_at_index` (`submitted_at`) USING BTREE,
  KEY `cpx_user_kyc_verification_reviewed_at_index` (`reviewed_at`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户KYC认证表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `cpx_user_login_log`
--

DROP TABLE IF EXISTS `cpx_user_login_log`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `cpx_user_login_log` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `user_id` bigint NOT NULL COMMENT '用户ID',
  `ip_address` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '登录IP地址',
  `user_agent` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '用户代理 请求头中的User-Agent',
  `device_type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '设备类型:web=网页,ios=苹果,android=安卓,api=API',
  `device_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '设备名称',
  `device_id` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '设备唯一标识',
  `browser` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '浏览器',
  `os` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '操作系统',
  `location_country` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '登录国家',
  `location_province` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '登录省份',
  `location_city` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '登录城市',
  `login_result` tinyint NOT NULL DEFAULT '1' COMMENT '登录结果:1=成功,2=失败',
  `failure_reason` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '失败原因',
  `login_time` timestamp NOT NULL COMMENT '登录时间',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  KEY `cpx_user_login_log_user_id_index` (`user_id`) USING BTREE,
  KEY `cpx_user_login_log_ip_address_index` (`ip_address`) USING BTREE,
  KEY `cpx_user_login_log_device_type_index` (`device_type`) USING BTREE,
  KEY `cpx_user_login_log_login_result_index` (`login_result`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=83 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户登录日志表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `cpx_user_notification_setting`
--

DROP TABLE IF EXISTS `cpx_user_notification_setting`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `cpx_user_notification_setting` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `user_id` bigint NOT NULL COMMENT '用户ID',
  `system_message_enabled` tinyint NOT NULL DEFAULT '1' COMMENT '系统消息:0=关闭,1=开启',
  `trading_message_enabled` tinyint NOT NULL DEFAULT '1' COMMENT '交易消息:0=关闭,1=开启',
  `security_message_enabled` tinyint NOT NULL DEFAULT '1' COMMENT '安全消息:0=关闭,1=开启',
  `promotion_message_enabled` tinyint NOT NULL DEFAULT '1' COMMENT '推广消息:0=关闭,1=开启',
  `email_login_enabled` tinyint NOT NULL DEFAULT '1' COMMENT '登录邮件通知:0=关闭,1=开启',
  `email_trading_enabled` tinyint NOT NULL DEFAULT '1' COMMENT '交易邮件通知:0=关闭,1=开启',
  `email_withdrawal_enabled` tinyint NOT NULL DEFAULT '1' COMMENT '提现邮件通知:0=关闭,1=开启',
  `email_security_enabled` tinyint NOT NULL DEFAULT '1' COMMENT '安全邮件通知:0=关闭,1=开启',
  `email_promotion_enabled` tinyint NOT NULL DEFAULT '0' COMMENT '推广邮件通知:0=关闭,1=开启',
  `email_news_enabled` tinyint NOT NULL DEFAULT '0' COMMENT '新闻邮件通知:0=关闭,1=开启',
  `push_login_enabled` tinyint NOT NULL DEFAULT '1' COMMENT '登录推送通知:0=关闭,1=开启',
  `push_trading_enabled` tinyint NOT NULL DEFAULT '1' COMMENT '交易推送通知:0=关闭,1=开启',
  `push_price_alert_enabled` tinyint NOT NULL DEFAULT '1' COMMENT '价格提醒推送:0=关闭,1=开启',
  `push_security_enabled` tinyint NOT NULL DEFAULT '1' COMMENT '安全推送通知:0=关闭,1=开启',
  `push_promotion_enabled` tinyint NOT NULL DEFAULT '0' COMMENT '推广推送通知:0=关闭,1=开启',
  `push_news_enabled` tinyint NOT NULL DEFAULT '0' COMMENT '新闻推送通知:0=关闭,1=开启',
  `sms_login_enabled` tinyint NOT NULL DEFAULT '0' COMMENT '登录短信通知:0=关闭,1=开启',
  `sms_trading_enabled` tinyint NOT NULL DEFAULT '0' COMMENT '交易短信通知:0=关闭,1=开启',
  `sms_withdrawal_enabled` tinyint NOT NULL DEFAULT '1' COMMENT '提现短信通知:0=关闭,1=开启',
  `sms_security_enabled` tinyint NOT NULL DEFAULT '1' COMMENT '安全短信通知:0=关闭,1=开启',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `cpx_user_notification_setting_user_id_unique` (`user_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户通知设置表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `cpx_user_passkey`
--

DROP TABLE IF EXISTS `cpx_user_passkey`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `cpx_user_passkey` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `user_id` bigint NOT NULL COMMENT '用户ID',
  `credential_id` varchar(512) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '凭证ID',
  `public_key` text COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '公钥',
  `counter` int unsigned NOT NULL DEFAULT '0' COMMENT '计数器',
  `attestation_type` varchar(32) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '认证类型',
  `device_info` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '设备信息',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `cpx_user_passkey_credential_id_unique` (`credential_id`),
  KEY `cpx_user_passkey_user_id_index` (`user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户通行密钥 Passkey 表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `cpx_user_password_history`
--

DROP TABLE IF EXISTS `cpx_user_password_history`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `cpx_user_password_history` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `user_id` bigint NOT NULL COMMENT '用户ID',
  `password_type` tinyint NOT NULL COMMENT '密码类型:1=登录密码,2=交易密码',
  `password_hash` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '密码哈希',
  `change_ip` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '修改IP',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  KEY `cpx_user_password_history_user_id_password_type_index` (`user_id`,`password_type`) USING BTREE,
  KEY `cpx_user_password_history_created_at_index` (`created_at`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=5 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户密码历史表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `cpx_user_payment`
--

DROP TABLE IF EXISTS `cpx_user_payment`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `cpx_user_payment` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键',
  `user_id` bigint DEFAULT NULL COMMENT '用户ID',
  `flat_currency_id` int DEFAULT NULL COMMENT '法币ID',
  `payment_type` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '支付方式 如 wechat',
  `account_info` json DEFAULT NULL COMMENT '账号信息json',
  `deleted_at` datetime DEFAULT NULL,
  `created_by` bigint NOT NULL DEFAULT '0' COMMENT '创建者',
  `updated_by` bigint NOT NULL DEFAULT '0' COMMENT '更新者',
  `created_at` datetime DEFAULT NULL,
  `updated_at` datetime DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户支付账号表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `cpx_user_relation`
--

DROP TABLE IF EXISTS `cpx_user_relation`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `cpx_user_relation` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `user_id` bigint NOT NULL COMMENT '用户ID',
  `parent_id` bigint DEFAULT NULL COMMENT '直接上级ID',
  `link` json DEFAULT NULL COMMENT '完整上级链路，格式：[4,3,2,1]（从直接上级到顶级）',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `cpx_user_relation_user_id_unique` (`user_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户关系表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `cpx_user_vip_level`
--

DROP TABLE IF EXISTS `cpx_user_vip_level`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `cpx_user_vip_level` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `user_id` bigint NOT NULL COMMENT '用户ID',
  `vip_level_id` bigint NOT NULL COMMENT 'VIP等级ID',
  `current_spot_trading_volume` decimal(20,8) NOT NULL DEFAULT '0.********' COMMENT '当前现货交易量',
  `current_futures_trading_volume` decimal(20,8) NOT NULL DEFAULT '0.********' COMMENT '当前合约交易量',
  `current_total_asset` decimal(20,8) NOT NULL DEFAULT '0.********' COMMENT '当前总资产',
  `current_specific_asset_amount` decimal(20,8) NOT NULL DEFAULT '0.********' COMMENT '当前特定币种资产',
  `level_achieved_at` timestamp NOT NULL COMMENT '达到等级时间',
  `level_expires_at` timestamp NULL DEFAULT NULL COMMENT '等级过期时间',
  `is_active` tinyint NOT NULL DEFAULT '1' COMMENT '是否当前等级:0=否,1=是',
  `gift_received` tinyint NOT NULL DEFAULT '0' COMMENT '是否领取礼包:0=否,1=是',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `cpx_user_vip_level_user_id_unique` (`user_id`) USING BTREE,
  KEY `cpx_user_vip_level_vip_level_id_index` (`vip_level_id`) USING BTREE,
  KEY `cpx_user_vip_level_is_active_index` (`is_active`) USING BTREE,
  KEY `cpx_user_vip_level_level_achieved_at_index` (`level_achieved_at`) USING BTREE,
  KEY `cpx_user_vip_level_level_expires_at_index` (`level_expires_at`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户VIP等级关联表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `cpx_user_withdrawal_setting`
--

DROP TABLE IF EXISTS `cpx_user_withdrawal_setting`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `cpx_user_withdrawal_setting` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `user_id` bigint NOT NULL COMMENT '用户ID',
  `small_withdrawal_enabled` tinyint NOT NULL DEFAULT '0' COMMENT '是否启用小额免密提现:0=否,1=是',
  `small_withdrawal_limit` decimal(20,8) DEFAULT NULL COMMENT '小额免密提现限额',
  `withdrawal_whitelist_enabled` tinyint NOT NULL DEFAULT '0' COMMENT '是否启用提现白名单:0=否,1=是',
  `withdrawal_whitelist` json DEFAULT NULL COMMENT '提现白名单地址（网络类型、提现地址、备注）',
  `withdrawal_revoke_enabled` tinyint NOT NULL DEFAULT '0' COMMENT '是否开启撤销提现（开启后1分钟内可撤销提现）:0=否,1=是',
  `preferred_networks` json DEFAULT NULL COMMENT '偏好网络列表',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `cpx_user_withdrawal_setting_user_id_unique` (`user_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户提现设置表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `cpx_vip_level`
--

DROP TABLE IF EXISTS `cpx_vip_level`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `cpx_vip_level` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '等级名称 VIP0,VIP1',
  `level` int NOT NULL COMMENT '等级数值',
  `icon` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '等级图标',
  `color` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '等级颜色',
  `spot_trading_volume_requirement` decimal(20,8) NOT NULL DEFAULT '0.********' COMMENT '现货交易量要求',
  `futures_trading_volume_requirement` decimal(20,8) NOT NULL DEFAULT '0.********' COMMENT '合约交易量要求',
  `total_asset_requirement` decimal(20,8) NOT NULL DEFAULT '0.********' COMMENT '总资产要求',
  `specific_asset_symbol` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '特定币种资产要求符号',
  `specific_asset_amount` decimal(20,8) NOT NULL DEFAULT '0.********' COMMENT '特定币种资产要求数量',
  `spot_maker_fee_rate` decimal(8,6) NOT NULL DEFAULT '0.001000' COMMENT '现货挂单手续费率',
  `spot_taker_fee_rate` decimal(8,6) NOT NULL DEFAULT '0.001000' COMMENT '现货吃单手续费率',
  `futures_maker_fee_rate` decimal(8,6) NOT NULL DEFAULT '0.000200' COMMENT '合约挂单手续费率',
  `futures_taker_fee_rate` decimal(8,6) NOT NULL DEFAULT '0.000400' COMMENT '合约吃单手续费率',
  `daily_withdrawal_limit` decimal(20,8) NOT NULL DEFAULT '0.********' COMMENT '日提现限额',
  `vip_gift` json DEFAULT NULL COMMENT 'VIP升级礼包（分trading（交易）,assets（资产）两种类型）',
  `vip_privileges` json DEFAULT NULL COMMENT 'VIP权限列表',
  `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci COMMENT '等级描述',
  `status` tinyint NOT NULL DEFAULT '1' COMMENT '状态:1=启用,2=禁用',
  `sort` int NOT NULL DEFAULT '0' COMMENT '排序',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `cpx_vip_level_name_unique` (`name`) USING BTREE,
  UNIQUE KEY `cpx_vip_level_level_unique` (`level`) USING BTREE,
  KEY `cpx_vip_level_level_index` (`level`) USING BTREE,
  KEY `cpx_vip_level_status_index` (`status`) USING BTREE,
  KEY `cpx_vip_level_sort_index` (`sort`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=7 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='会员等级表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `ctc_user_merchant`
--

DROP TABLE IF EXISTS `ctc_user_merchant`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `ctc_user_merchant` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键',
  `flat_currency_id` bigint DEFAULT NULL COMMENT '法币市场（cpx_flat_currency表ID）',
  `user_id` bigint NOT NULL COMMENT '用户ID（关联cpx_user用户表）',
  `user_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '姓名',
  `id_card` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '身份证号',
  `id_card_front` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '身份证正面图片',
  `id_card_back` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '身份证反面图片',
  `store_name` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '店铺名称',
  `store_logo` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '店铺Logo图片',
  `contact_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '联系人姓名',
  `contact_phone` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '联系电话',
  `contact_email` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '联系邮箱',
  `status` tinyint NOT NULL DEFAULT '0' COMMENT '审核状态: 0-待审核 1-审核通过 2-拒绝',
  `remark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '审核备注',
  `created_by` bigint NOT NULL DEFAULT '0' COMMENT '创建者',
  `updated_by` bigint NOT NULL DEFAULT '0' COMMENT '更新者',
  `created_at` datetime DEFAULT NULL,
  `updated_at` datetime DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  KEY `idx_user_id` (`user_id`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='c2c商家申请表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `ctc_user_merchant_money`
--

DROP TABLE IF EXISTS `ctc_user_merchant_money`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `ctc_user_merchant_money` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键',
  `user_id` bigint NOT NULL COMMENT '用户ID（关联cpx_user用户表）',
  `order_sn` varchar(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '订单号',
  `money` decimal(11,2) DEFAULT NULL COMMENT '保证金',
  `status` tinyint NOT NULL DEFAULT '0' COMMENT '审核状态: 0-待支付 1-已支付 2-支付失败',
  `created_by` bigint NOT NULL DEFAULT '0' COMMENT '创建者',
  `updated_by` bigint NOT NULL DEFAULT '0' COMMENT '更新者',
  `created_at` datetime DEFAULT NULL,
  `updated_at` datetime DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  KEY `idx_user_id` (`user_id`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='c2c保证金表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `currency`
--

DROP TABLE IF EXISTS `currency`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `currency` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `symbol` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '交易标的',
  `base_asset` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '基础标的',
  `base_assets_precision` tinyint unsigned NOT NULL DEFAULT '8' COMMENT '基础资产显示精度',
  `quote_asset` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '计价币种symbol',
  `quote_assets_id` int DEFAULT NULL COMMENT '计价币种id',
  `s_price_precision` tinyint unsigned NOT NULL DEFAULT '2' COMMENT '现货交易资产价格精度',
  `s_quantity_precision` tinyint unsigned NOT NULL DEFAULT '5' COMMENT '现货交易数量精度',
  `m_price_precision` tinyint unsigned DEFAULT '2' COMMENT '合约价格显示精度',
  `m_quantity_precision` tinyint unsigned DEFAULT '3' COMMENT '合约交易数量精度',
  `is_spotTrade` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '是否支持现货交易',
  `is_marginTrade` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '是否支持合约交易',
  `market_type` tinyint(1) DEFAULT NULL COMMENT '标的交易市场类型 : 1加密数字货币 2美股 3外汇 4期货',
  `trading_start` char(12) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '交易时段开始时间：h:i | 0为不限制',
  `trading_end` char(12) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '交易时段结束时间：h:i | 0为不限制',
  `trading_timezone` char(12) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '交易时间时区:UTC',
  `status` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '是否启用标的',
  `icon` varchar(200) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '图标',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1274 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='平台所有交易标的数据';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `currency_category`
--

DROP TABLE IF EXISTS `currency_category`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `currency_category` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `cate_name` json DEFAULT NULL COMMENT '栏目名称',
  `cate_desc` json DEFAULT NULL COMMENT '栏目简介',
  `sort` tinyint DEFAULT '0' COMMENT '排序',
  `status` tinyint(1) DEFAULT '1' COMMENT '是否启用',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=46 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='数字货币币种栏目';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `currency_margin_borrow`
--

DROP TABLE IF EXISTS `currency_margin_borrow`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `currency_margin_borrow` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `currency_id` int DEFAULT NULL,
  `is_cross` tinyint unsigned DEFAULT NULL COMMENT '1 全仓 2逐仓',
  `base_level` json DEFAULT NULL COMMENT '基础币币种对应的档位数据{vip,day_rate,borrowlimit}',
  `quote_level` json DEFAULT NULL COMMENT '计价币的借贷档位配置',
  `is_base_borrow` tinyint unsigned DEFAULT '1' COMMENT '基础币是否可借',
  `is_quote_borrow` tinyint unsigned DEFAULT '1' COMMENT '计价币是否可借',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_union` (`currency_id`,`is_cross`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=820 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='杠杆交易借贷利率配置';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `currency_mate`
--

DROP TABLE IF EXISTS `currency_mate`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `currency_mate` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `currency_id` int NOT NULL COMMENT '交易标的id',
  `mate_info` json DEFAULT NULL COMMENT '交易标的原数据',
  `description` json DEFAULT NULL COMMENT '简介 多语言',
  `logo` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '币种图标',
  `cateIds` json DEFAULT NULL COMMENT '币种标签',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `idx_currency` (`currency_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1054 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='数字货币简介和原始介绍数据';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `currency_ticker`
--

DROP TABLE IF EXISTS `currency_ticker`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `currency_ticker` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `currency_id` int NOT NULL COMMENT '交易标的',
  `market_type` tinyint NOT NULL COMMENT '交易市场',
  `price_change` decimal(20,8) NOT NULL COMMENT '价格变化',
  `price_changeP` decimal(10,2) NOT NULL COMMENT '价格变化百分比',
  `pre_close_price` decimal(20,8) NOT NULL COMMENT '上周期收盘价',
  `last_price` decimal(20,8) NOT NULL COMMENT '最新价格	',
  `last_qty` decimal(20,2) NOT NULL COMMENT '最新交易量',
  `open_price` decimal(20,8) NOT NULL COMMENT '开盘价格',
  `high_price` decimal(20,8) NOT NULL COMMENT '最高价格',
  `low_price` decimal(20,8) NOT NULL COMMENT '最低价格',
  `volume` decimal(20,8) DEFAULT NULL COMMENT '交易日成交量',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `idx_union` (`currency_id`,`market_type`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='交易对24小时价格变化数据';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `currency_transfer`
--

DROP TABLE IF EXISTS `currency_transfer`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `currency_transfer` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `currency_id` int DEFAULT NULL,
  `transfer` tinyint DEFAULT '0' COMMENT '是否可以划转',
  `chains` json DEFAULT NULL COMMENT '支持的链列表	',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_currency` (`currency_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=378 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='数字货币充值 提币 区块链相关信息';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `margin_liquidation_record`
--

DROP TABLE IF EXISTS `margin_liquidation_record`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `margin_liquidation_record` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `user_id` bigint unsigned NOT NULL COMMENT '用户ID',
  `position_id` bigint unsigned NOT NULL COMMENT '仓位ID',
  `currency_id` int unsigned NOT NULL COMMENT '币种ID',
  `side` tinyint NOT NULL COMMENT '仓位方向：1-做多，2-做空',
  `account_type` tinyint unsigned NOT NULL COMMENT '账户类型：2-全仓杠杆，3-逐仓杠杆',
  `liquidation_type` tinyint NOT NULL COMMENT '强平类型：1-保证金不足，2-ADL自动减仓，3-到期强平',
  `trigger_source` tinyint NOT NULL DEFAULT '1' COMMENT '触发来源：1-系统自动，2-人工干预',
  `liquidation_order_id` bigint unsigned DEFAULT NULL COMMENT '强平执行订单ID',
  `original_quantity` decimal(30,18) NOT NULL COMMENT '原始持仓数量',
  `liquidated_quantity` decimal(30,18) NOT NULL COMMENT '强平数量',
  `liquidation_price` decimal(30,18) NOT NULL COMMENT '强平价格',
  `mark_price` decimal(30,18) NOT NULL COMMENT '触发时标记价格',
  `margin_ratio` decimal(10,4) NOT NULL COMMENT '强平时保证金率',
  `liquidation_fee` decimal(30,18) NOT NULL DEFAULT '0.****************00' COMMENT '强平手续费',
  `insurance_fund` decimal(30,18) NOT NULL DEFAULT '0.****************00' COMMENT '保险基金使用',
  `bankruptcy_amount` decimal(30,18) NOT NULL DEFAULT '0.****************00' COMMENT '穿仓金额',
  `trigger_time` timestamp NOT NULL COMMENT '强平触发时间',
  `completed_time` timestamp NULL DEFAULT NULL COMMENT '强平完成时间',
  `status` tinyint NOT NULL DEFAULT '1' COMMENT '状态：1-待执行，2-执行中，3-已完成，4-执行失败，5-已取消',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注信息',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_position_id` (`position_id`),
  KEY `idx_currency_id` (`currency_id`),
  KEY `idx_account_type` (`account_type`),
  KEY `idx_liquidation_type` (`liquidation_type`),
  KEY `idx_trigger_time` (`trigger_time`),
  KEY `idx_status` (`status`),
  KEY `idx_liquidation_order_id` (`liquidation_order_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='杠杆强平记录表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `match_orders`
--

DROP TABLE IF EXISTS `match_orders`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `match_orders` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `user_id` bigint DEFAULT NULL COMMENT '用户id',
  `currency_id` int DEFAULT NULL COMMENT '交易标的',
  `order_id` bigint DEFAULT NULL COMMENT '由系统生成',
  `market_type` tinyint DEFAULT NULL COMMENT '市场类型',
  `side` tinyint NOT NULL COMMENT '1买-1卖',
  `quantity` decimal(20,8) DEFAULT NULL COMMENT '交易数量',
  `fill_quantity` decimal(20,8) DEFAULT NULL COMMENT '成交数量',
  `price` decimal(20,8) DEFAULT NULL COMMENT '挂单价格',
  `avg_price` decimal(20,8) DEFAULT NULL COMMENT '平均成交价格',
  `order_type` tinyint unsigned DEFAULT NULL COMMENT '1 市价 2 限价',
  `trade_type` tinyint NOT NULL DEFAULT '0' COMMENT '-1：撤单 0 待成交 1:完全成交 2 部分成交 ',
  `order_force` char(5) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'gtc等待全部成交\nioc立即成交，剩余部分取消\nfok全部成交或者全部取消',
  `fill_time` bigint DEFAULT NULL COMMENT '成交时间 毫秒',
  `status` tinyint DEFAULT '-1' COMMENT '-1 待加入撮合引擎\n1 pending 等待成交\n2 partial_filler 部分成交\n3 filled 全部成交\n0 订单取消',
  `reason` varchar(32) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '撤单原因',
  `has_trade` tinyint NOT NULL DEFAULT '0' COMMENT '是否处理过成交',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `idx_union` (`user_id`,`currency_id`,`order_id`) USING BTREE,
  KEY `idx_oid` (`order_id`) USING BTREE,
  KEY `idx_c_m` (`currency_id`,`market_type`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=721 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户交易订单表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `match_trades`
--

DROP TABLE IF EXISTS `match_trades`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `match_trades` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `currency_id` int DEFAULT NULL COMMENT '交易标的id',
  `buy_order_id` bigint DEFAULT NULL COMMENT '买单交易id',
  `sell_order_id` bigint DEFAULT NULL COMMENT '卖单交易id',
  `buy_user_id` bigint DEFAULT NULL COMMENT '买单用户',
  `sell_user_id` bigint DEFAULT NULL COMMENT '卖单用户',
  `market_type` tinyint DEFAULT NULL COMMENT '交易市场类型',
  `trade_id` bigint DEFAULT NULL COMMENT '撮合成交id',
  `price` decimal(20,8) DEFAULT NULL COMMENT '成交价格',
  `quantity` decimal(20,8) DEFAULT NULL COMMENT '成交数量',
  `is_bot` tinyint unsigned DEFAULT '0' COMMENT '是否机器人',
  `match_time` bigint DEFAULT NULL COMMENT '成交时间 毫秒',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `idx_union` (`currency_id`,`market_type`,`trade_id`) USING BTREE,
  KEY `idx_user` (`buy_user_id`) USING BTREE,
  KEY `idx_users` (`sell_user_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=818 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户交易订单成交记录表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `menu`
--

DROP TABLE IF EXISTS `menu`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `menu` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键',
  `parent_id` bigint unsigned NOT NULL COMMENT '父ID',
  `name` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '菜单名称',
  `meta` json DEFAULT NULL COMMENT '附加属性',
  `path` varchar(60) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '路径',
  `component` varchar(150) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '组件路径',
  `redirect` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '重定向地址',
  `status` tinyint NOT NULL DEFAULT '1' COMMENT '状态:1=正常,2=停用',
  `sort` smallint NOT NULL DEFAULT '0' COMMENT '排序',
  `created_by` bigint NOT NULL DEFAULT '0' COMMENT '创建者',
  `updated_by` bigint NOT NULL DEFAULT '0' COMMENT '更新者',
  `created_at` datetime DEFAULT NULL,
  `updated_at` datetime DEFAULT NULL,
  `remark` varchar(60) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '备注',
  PRIMARY KEY (`id`),
  UNIQUE KEY `menu_name_unique` (`name`)
) ENGINE=InnoDB AUTO_INCREMENT=112 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='菜单信息表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `migrations`
--

DROP TABLE IF EXISTS `migrations`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `migrations` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `migration` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `batch` int NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=32 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `role`
--

DROP TABLE IF EXISTS `role`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `role` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键',
  `name` varchar(30) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '角色名称',
  `code` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '角色代码',
  `status` tinyint NOT NULL DEFAULT '1' COMMENT '状态:1=正常,2=停用',
  `sort` smallint NOT NULL DEFAULT '0' COMMENT '排序',
  `created_by` bigint NOT NULL DEFAULT '0' COMMENT '创建者',
  `updated_by` bigint NOT NULL DEFAULT '0' COMMENT '更新者',
  `created_at` datetime DEFAULT NULL,
  `updated_at` datetime DEFAULT NULL,
  `remark` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '备注',
  PRIMARY KEY (`id`),
  UNIQUE KEY `role_code_unique` (`code`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='角色信息表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `role_belongs_menu`
--

DROP TABLE IF EXISTS `role_belongs_menu`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `role_belongs_menu` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `role_id` bigint NOT NULL COMMENT '角色id',
  `menu_id` bigint NOT NULL COMMENT '菜单id',
  `created_at` datetime DEFAULT NULL,
  `updated_at` datetime DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `rules`
--

DROP TABLE IF EXISTS `rules`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `rules` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `ptype` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `v0` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `v1` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `v2` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `v3` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `v4` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `v5` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `system_setting_config`
--

DROP TABLE IF EXISTS `system_setting_config`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `system_setting_config` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `group_id` bigint NOT NULL DEFAULT '0' COMMENT '组id',
  `key` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '配置键名',
  `value` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '配置值',
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '配置名称',
  `input_type` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '数据输入类型',
  `config_select_data` json DEFAULT NULL COMMENT '配置选项数据',
  `sort` smallint unsigned NOT NULL DEFAULT '0' COMMENT '排序',
  `remark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '备注',
  `created_by` bigint DEFAULT NULL COMMENT '创建者',
  `updated_by` bigint DEFAULT NULL COMMENT '更新者',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `system_setting_config_key_unique` (`key`)
) ENGINE=InnoDB AUTO_INCREMENT=28 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `system_setting_config_group`
--

DROP TABLE IF EXISTS `system_setting_config_group`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `system_setting_config_group` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键',
  `name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '配置组名称',
  `code` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '配置组标识',
  `icon` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '配置组图标',
  `created_by` bigint DEFAULT NULL COMMENT '创建者',
  `updated_by` bigint DEFAULT NULL COMMENT '更新者',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `remark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=9 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `trade_config`
--

DROP TABLE IF EXISTS `trade_config`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `trade_config` (
  `id` int NOT NULL AUTO_INCREMENT,
  `currency_id` int DEFAULT NULL,
  `market_type` tinyint DEFAULT NULL,
  `min_trade_num` decimal(10,5) DEFAULT NULL COMMENT '限价最小下单数量',
  `max_trade_num` decimal(20,5) DEFAULT NULL COMMENT '限价最大下单数量',
  `min_trade_price` decimal(20,8) DEFAULT NULL COMMENT '最小下单价格',
  `max_trade_price` decimal(20,8) DEFAULT NULL COMMENT '最大下单价格',
  `limit_price_rate` double DEFAULT NULL COMMENT '限价订单价格上下限百分比，也就是挂单不能超过最新价格的这个范围',
  `maker_limit` decimal(20,2) DEFAULT NULL COMMENT '市价单单笔最大开仓数量',
  `limit_limit` decimal(20,2) DEFAULT NULL COMMENT '限价单单笔最大开仓数量',
  `order_limit` int NOT NULL DEFAULT '100' COMMENT '最大挂单数量（所有委托的订单最大挂单数量）',
  `trigger_protect` double DEFAULT NULL COMMENT '价差保护百分比，指数和最新价格',
  `liquidation_fee` decimal(10,6) unsigned NOT NULL DEFAULT '0.000000' COMMENT '强平费率',
  `tick_size` decimal(10,8) DEFAULT NULL COMMENT '最小价格变化',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_currency` (`currency_id`,`market_type`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=2079 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='加密数字货币现货和合约开单交易规则限制';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `trade_margin_brackets`
--

DROP TABLE IF EXISTS `trade_margin_brackets`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `trade_margin_brackets` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `currency_id` int DEFAULT NULL,
  `level` tinyint DEFAULT NULL COMMENT '档位等级',
  `hold_start` decimal(30,2) DEFAULT NULL COMMENT '持仓范围起点',
  `hold_end` varchar(30) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '持仓范围结束',
  `max_lever` decimal(10,2) DEFAULT NULL COMMENT '最大杠杆',
  `keep_rate` decimal(10,8) DEFAULT NULL COMMENT '维持保证金率',
  `init_rate` decimal(10,8) DEFAULT NULL COMMENT '初始保证金率',
  `is_cross` tinyint DEFAULT NULL COMMENT '1全仓 2逐仓',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_union` (`currency_id`,`is_cross`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=12999 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='杠杆交易风险控制配置数据表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `trade_margin_conditional_orders`
--

DROP TABLE IF EXISTS `trade_margin_conditional_orders`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `trade_margin_conditional_orders` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键',
  `user_id` bigint NOT NULL COMMENT '用户ID',
  `currency_id` int NOT NULL COMMENT '币种ID',
  `margin_type` tinyint NOT NULL COMMENT '保证金类型:1=全仓,2=逐仓',
  `position_side` tinyint NOT NULL COMMENT '仓位方向:1=做多,2=做空',
  `order_type` tinyint NOT NULL COMMENT '委托类型:1=止盈单,2=止损单',
  `trigger_price` decimal(20,8) NOT NULL COMMENT '触发价格',
  `close_quantity` decimal(20,8) NOT NULL COMMENT '平仓数量',
  `execution_price` decimal(20,8) unsigned DEFAULT NULL COMMENT '执行价格',
  `execution_type` tinyint unsigned DEFAULT NULL COMMENT '执行类型 1市价 2限价',
  `trigger_type` tinyint NOT NULL DEFAULT '1' COMMENT '触发条件:1=最新价触发,2=标记价触发',
  `status` tinyint NOT NULL DEFAULT '1' COMMENT '状态:1=等待触发,2=已触发,3=已撤销,4=触发失败',
  `trigger_time` timestamp NULL DEFAULT NULL COMMENT '触发时间',
  `triggered_order_id` bigint DEFAULT NULL COMMENT '触发后生成的杠杆订单ID',
  `failure_reason` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '触发失败原因',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_currency_id` (`currency_id`),
  KEY `idx_status` (`status`),
  KEY `idx_trigger_price` (`trigger_price`)
) ENGINE=InnoDB AUTO_INCREMENT=4 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='杠杆委托订单表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `trade_margin_level`
--

DROP TABLE IF EXISTS `trade_margin_level`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `trade_margin_level` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `currency_id` int DEFAULT NULL COMMENT '交易对id',
  `margin_max` decimal(20,2) DEFAULT NULL COMMENT '持仓名义价值区间',
  `margin_min` decimal(20,2) DEFAULT NULL COMMENT '持仓名义价值区间',
  `leverage_min` tinyint DEFAULT NULL COMMENT '最低杠杆',
  `leverage_max` tinyint DEFAULT NULL COMMENT '最高杠杆',
  `margin_rate` decimal(10,5) DEFAULT NULL COMMENT '维持保证金比率',
  `cum_amount` decimal(20,2) DEFAULT NULL COMMENT '维持保证金速算额',
  `level` tinyint unsigned DEFAULT NULL COMMENT '层级',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_currency` (`currency_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=821 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='合约市场交易保证金与杠杆';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `trade_margin_order`
--

DROP TABLE IF EXISTS `trade_margin_order`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `trade_margin_order` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `user_id` bigint unsigned NOT NULL COMMENT '用户ID',
  `currency_id` bigint unsigned NOT NULL COMMENT '交易币种ID',
  `match_order_id` bigint unsigned NOT NULL COMMENT '撮合引擎订单ID',
  `margin_type` tinyint NOT NULL COMMENT '保证金类型：1-全仓，2-逐仓',
  `leverage` decimal(10,2) unsigned NOT NULL COMMENT '杠杆倍数',
  `position_side` tinyint NOT NULL COMMENT '仓位方向：1-做多，2-做空',
  `margin_amount` decimal(20,8) NOT NULL COMMENT '保证金金额',
  `reduce_only` tinyint NOT NULL DEFAULT '0' COMMENT '只减仓：0-否，1-是',
  `frozen_amount` decimal(30,18) DEFAULT NULL COMMENT '冻结资金',
  `used_amount` decimal(30,18) DEFAULT NULL COMMENT '使用资金',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_match_order_id` (`match_order_id`),
  KEY `idx_margin_type` (`margin_type`),
  KEY `idx_position_side` (`position_side`),
  KEY `idx_union` (`user_id`,`currency_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=104 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='杠杆订单表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `trade_margin_position`
--

DROP TABLE IF EXISTS `trade_margin_position`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `trade_margin_position` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `user_id` bigint unsigned NOT NULL COMMENT '用户ID',
  `currency_id` bigint unsigned NOT NULL COMMENT '交易币种ID',
  `side` tinyint NOT NULL COMMENT '仓位方向：1-做多，2-做空',
  `margin_type` tinyint NOT NULL COMMENT '保证金类型：1-全仓，2-逐仓',
  `leverage` decimal(6,2) NOT NULL COMMENT '杠杆倍数',
  `quantity` decimal(20,8) NOT NULL COMMENT '持仓数量',
  `available_quantity` decimal(20,8) NOT NULL DEFAULT '0.********' COMMENT '可用数量（可平仓数量）',
  `frozen_quantity` decimal(20,8) NOT NULL DEFAULT '0.********' COMMENT '冻结数量（挂单占用）',
  `entry_price` decimal(20,8) NOT NULL COMMENT '开仓均价',
  `margin_amount` decimal(20,8) NOT NULL COMMENT '保证金金额',
  `initial_margin` decimal(20,8) NOT NULL COMMENT '初始保证金',
  `maintenance_margin` decimal(20,8) NOT NULL COMMENT '维持保证金',
  `realized_pnl` decimal(20,8) NOT NULL DEFAULT '0.********' COMMENT '已实现盈亏',
  `status` tinyint NOT NULL DEFAULT '1' COMMENT '状态：1-持仓中，2-已平仓，3-强制平仓',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_currency_id` (`currency_id`),
  KEY `idx_status` (`status`),
  KEY `uk_user_currency_side_type` (`user_id`,`currency_id`,`side`,`margin_type`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=25 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='杠杆仓位表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `trade_perpetual_adl`
--

DROP TABLE IF EXISTS `trade_perpetual_adl`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `trade_perpetual_adl` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '记录ID',
  `currency_id` int unsigned NOT NULL COMMENT '币种ID',
  `trigger_user_id` bigint unsigned NOT NULL COMMENT '触发用户ID',
  `target_user_id` bigint unsigned NOT NULL COMMENT '目标用户ID',
  `target_position_id` bigint unsigned NOT NULL COMMENT '目标仓位ID',
  `margin_mode` tinyint NOT NULL COMMENT '保证金模式：1-全仓，2-逐仓',
  `adl_quantity` decimal(20,8) NOT NULL COMMENT '减仓数量（币本位）',
  `adl_price` decimal(20,8) NOT NULL COMMENT '减仓价格',
  `compensation_amount` decimal(20,8) NOT NULL COMMENT '补偿金额',
  `status` tinyint NOT NULL DEFAULT '1' COMMENT '状态：1-执行中，2-已完成，3-失败',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_currency_id` (`currency_id`),
  KEY `idx_trigger_user_id` (`trigger_user_id`),
  KEY `idx_target_user_id` (`target_user_id`),
  KEY `idx_margin_mode` (`margin_mode`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='自动减仓记录表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `trade_perpetual_conditional_order`
--

DROP TABLE IF EXISTS `trade_perpetual_conditional_order`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `trade_perpetual_conditional_order` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '委托单ID',
  `user_id` bigint unsigned NOT NULL COMMENT '用户ID',
  `currency_id` int unsigned NOT NULL COMMENT '币种ID',
  `conditional_type` tinyint NOT NULL COMMENT '委托类型：1-止盈，2-止损，3-计划委托，4-追踪委托',
  `margin_mode` tinyint NOT NULL COMMENT '保证金模式：1-全仓，2-逐仓',
  `side` tinyint NOT NULL COMMENT '方向：1-买入开多，2-卖出开空，3-买入平空，4-卖出平多',
  `quantity` decimal(20,8) NOT NULL COMMENT '委托数量（币本位）',
  `leverage` decimal(6,2) NOT NULL COMMENT '杠杆倍数',
  `reduce_only` tinyint NOT NULL DEFAULT '0' COMMENT '只减仓：0-否，1-是',
  `time_in_force` tinyint NOT NULL DEFAULT '1' COMMENT '有效期：1-GTC，2-IOC，3-FOK',
  `trigger_price` decimal(20,8) NOT NULL COMMENT '触发价格',
  `trigger_condition` tinyint NOT NULL COMMENT '触发条件：1-大于等于，2-小于等于',
  `execution_type` tinyint NOT NULL COMMENT '执行方式：1-限价，2-市价',
  `execution_price` decimal(20,8) DEFAULT NULL COMMENT '执行价格（固定价格模式使用）',
  `callback_rate` decimal(10,4) DEFAULT NULL COMMENT '回调幅度（百分比）：止盈止损回调浮动模式、追踪委托回调幅度',
  `execution_mode` tinyint DEFAULT NULL COMMENT '执行模式：1-固定价格，2-回调浮动（止盈止损使用）',
  `status` tinyint NOT NULL DEFAULT '1' COMMENT '状态：1-等待触发，2-已触发，3-已执行，4-已撤销，5-已过期，6-执行失败',
  `triggered_at` timestamp NULL DEFAULT NULL COMMENT '触发时间',
  `executed_at` timestamp NULL DEFAULT NULL COMMENT '执行时间',
  `executed_order_id` bigint unsigned DEFAULT NULL COMMENT '执行后的订单ID',
  `failure_reason` varchar(500) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '失败原因',
  `expires_at` timestamp NULL DEFAULT NULL COMMENT '过期时间',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_currency_id` (`currency_id`),
  KEY `idx_conditional_type` (`conditional_type`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB AUTO_INCREMENT=6 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='永续合约委托单表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `trade_perpetual_funding_config`
--

DROP TABLE IF EXISTS `trade_perpetual_funding_config`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `trade_perpetual_funding_config` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `currency_id` int DEFAULT NULL,
  `base_rate` decimal(10,6) unsigned DEFAULT '0.000100' COMMENT '基础利率',
  `max` decimal(10,6) unsigned DEFAULT '0.000500' COMMENT '资金费用上限',
  `min` decimal(10,6) DEFAULT '-0.000500' COMMENT '资金费用下限',
  `interval` int unsigned DEFAULT '8' COMMENT '资金费率收取间隔时间',
  `status` tinyint unsigned DEFAULT '1' COMMENT '是否收取资金费率',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_crid` (`currency_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=17 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='合约交易市场资金费率配置';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `trade_perpetual_funding_fee`
--

DROP TABLE IF EXISTS `trade_perpetual_funding_fee`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `trade_perpetual_funding_fee` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '记录ID',
  `user_id` bigint unsigned NOT NULL COMMENT '用户ID',
  `contract_id` bigint unsigned NOT NULL COMMENT '合约ID',
  `position_id` bigint unsigned NOT NULL COMMENT '仓位ID',
  `margin_mode` tinyint NOT NULL COMMENT '保证金模式：1-全仓，2-逐仓',
  `side` tinyint NOT NULL COMMENT '仓位方向：1-多头，2-空头',
  `funding_rate` decimal(10,8) NOT NULL COMMENT '资金费率',
  `position_size` decimal(20,8) NOT NULL COMMENT '仓位大小（币本位）',
  `position_value` decimal(20,8) NOT NULL COMMENT '仓位价值',
  `funding_fee` decimal(20,8) NOT NULL COMMENT '资金费用（正数为支付，负数为收取）',
  `funding_time` timestamp NOT NULL COMMENT '计费时间',
  `status` tinyint NOT NULL DEFAULT '1' COMMENT '状态：1-待收取，2-已收取，3-收取失败',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_contract_id` (`contract_id`),
  KEY `idx_position_id` (`position_id`),
  KEY `idx_funding_time` (`funding_time`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户资金费用记录表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `trade_perpetual_funding_rate`
--

DROP TABLE IF EXISTS `trade_perpetual_funding_rate`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `trade_perpetual_funding_rate` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '记录ID',
  `contract_id` bigint unsigned NOT NULL COMMENT '合约ID',
  `funding_rate` decimal(10,8) NOT NULL COMMENT '资金费率',
  `funding_time` timestamp NOT NULL COMMENT '资金费率生效时间',
  `next_funding_time` timestamp NOT NULL COMMENT '下次资金费率时间',
  `premium_rate` decimal(10,8) NOT NULL COMMENT '溢价率',
  `interest_rate` decimal(10,8) NOT NULL DEFAULT '0.00010000' COMMENT '利率（日化0.01%）',
  `mark_price` decimal(20,8) NOT NULL COMMENT '标记价格',
  `index_price` decimal(20,8) NOT NULL COMMENT '指数价格',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_contract_id` (`contract_id`),
  KEY `idx_funding_time` (`funding_time`),
  KEY `idx_next_funding_time` (`next_funding_time`)
) ENGINE=InnoDB AUTO_INCREMENT=141 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='永续合约资金费率记录表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `trade_perpetual_liquidation`
--

DROP TABLE IF EXISTS `trade_perpetual_liquidation`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `trade_perpetual_liquidation` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '记录ID',
  `user_id` bigint unsigned NOT NULL COMMENT '用户ID',
  `position_id` bigint unsigned NOT NULL COMMENT '仓位ID',
  `currency_id` int unsigned NOT NULL COMMENT '币种ID',
  `margin_mode` tinyint NOT NULL COMMENT '保证金模式：1-全仓，2-逐仓',
  `liquidation_type` tinyint NOT NULL COMMENT '强平类型：1-保证金不足，2-ADL自动减仓',
  `trigger_source` tinyint NOT NULL DEFAULT '1' COMMENT '触发来源：1-系统自动，2-人工干预',
  `liquidation_order_id` bigint unsigned DEFAULT NULL COMMENT '强平执行订单ID',
  `original_quantity` decimal(20,8) NOT NULL COMMENT '原始持仓数量（币本位）',
  `liquidated_quantity` decimal(20,8) NOT NULL COMMENT '强平数量（币本位）',
  `liquidation_price` decimal(20,8) NOT NULL COMMENT '强平价格',
  `mark_price` decimal(20,8) NOT NULL COMMENT '触发时标记价格',
  `margin_ratio` decimal(10,4) NOT NULL COMMENT '强平时保证金率',
  `liquidation_fee` decimal(20,8) NOT NULL DEFAULT '0.********' COMMENT '强平手续费',
  `insurance_fund` decimal(20,8) NOT NULL DEFAULT '0.********' COMMENT '保险基金使用',
  `bankruptcy_amount` decimal(20,8) NOT NULL DEFAULT '0.********' COMMENT '穿仓金额',
  `trigger_time` timestamp NOT NULL COMMENT '强平触发时间',
  `completed_time` timestamp NULL DEFAULT NULL COMMENT '强平完成时间',
  `status` tinyint NOT NULL DEFAULT '1' COMMENT '状态：1-待执行，2-执行中，3-已完成，4-执行失败',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_position_id` (`position_id`),
  KEY `idx_currency_id` (`currency_id`),
  KEY `idx_margin_mode` (`margin_mode`),
  KEY `idx_trigger_time` (`trigger_time`),
  KEY `idx_status` (`status`),
  KEY `idx_liquid_order` (`liquidation_order_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=45 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='永续合约强制平仓记录表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `trade_perpetual_order`
--

DROP TABLE IF EXISTS `trade_perpetual_order`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `trade_perpetual_order` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '订单ID',
  `user_id` bigint unsigned NOT NULL COMMENT '用户ID',
  `currency_id` int unsigned NOT NULL COMMENT '币种ID',
  `match_order_id` bigint unsigned NOT NULL COMMENT '撮合引擎订单ID',
  `margin_mode` tinyint NOT NULL COMMENT '保证金模式：1-全仓，2-逐仓',
  `side` tinyint NOT NULL COMMENT '方向：1-买入开多，2-卖出开空，3-买入平空，4-卖出平多',
  `price` decimal(20,8) NOT NULL COMMENT '委托价格',
  `margin_amount` decimal(20,8) NOT NULL COMMENT '保证金金额',
  `leverage` decimal(6,2) NOT NULL COMMENT '杠杆倍数',
  `reduce_only` tinyint NOT NULL DEFAULT '0' COMMENT '只减仓：0-否，1-是',
  `stop_price` decimal(20,8) DEFAULT NULL COMMENT '触发价格（止盈止损单）',
  `trigger_type` tinyint DEFAULT NULL COMMENT '触发类型：1-标记价格，2-最新价格',
  `used_amount` decimal(30,18) DEFAULT NULL COMMENT '使用的保证金',
  `frozen_amount` decimal(30,18) DEFAULT NULL COMMENT '冻结保证金',
  `estimated_fee` decimal(20,8) NOT NULL DEFAULT '0.********' COMMENT '预估冻结手续费',
  `actual_fee` decimal(20,8) unsigned NOT NULL DEFAULT '0.********' COMMENT '实际手续费',
  `position_id` bigint DEFAULT NULL COMMENT '关联仓位id',
  `is_trader` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '是否交易员订单',
  `is_copy` tinyint unsigned DEFAULT '0' COMMENT '0 用户开单 1跟交易员单',
  `copy_order_id` bigint DEFAULT NULL COMMENT '跟单订单id',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_match_order_id` (`match_order_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_currency_id` (`currency_id`),
  KEY `idx_position` (`user_id`,`position_id`) USING BTREE,
  KEY `idx_copy` (`is_copy`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=333 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='永续合约订单表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `trade_perpetual_position`
--

DROP TABLE IF EXISTS `trade_perpetual_position`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `trade_perpetual_position` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '仓位ID',
  `user_id` bigint unsigned NOT NULL COMMENT '用户ID',
  `currency_id` int unsigned NOT NULL COMMENT '币种ID',
  `margin_mode` tinyint NOT NULL COMMENT '保证金模式：1-全仓，2-逐仓',
  `side` tinyint NOT NULL COMMENT '方向：1-多头，2-空头',
  `quantity` decimal(20,8) NOT NULL COMMENT '持仓数量（币本位）',
  `available_quantity` decimal(20,8) NOT NULL COMMENT '可用数量（可平仓数量）',
  `frozen_quantity` decimal(20,8) NOT NULL DEFAULT '0.********' COMMENT '冻结数量（挂单占用）',
  `entry_price` decimal(20,8) NOT NULL COMMENT '开仓均价',
  `margin_amount` decimal(20,8) NOT NULL COMMENT '保证金金额',
  `initial_margin` decimal(20,8) NOT NULL COMMENT '初始保证金',
  `maintenance_margin` decimal(20,8) NOT NULL COMMENT '维持保证金',
  `realized_pnl` decimal(20,8) NOT NULL DEFAULT '0.********' COMMENT '已实现盈亏',
  `leverage` decimal(6,2) NOT NULL COMMENT '杠杆倍数',
  `auto_add_margin` tinyint NOT NULL DEFAULT '0' COMMENT '自动追加保证金（仅逐仓）',
  `total_charge` decimal(20,8) NOT NULL DEFAULT '0.********' COMMENT '总手续费',
  `total_funding` decimal(20,8) NOT NULL DEFAULT '0.********' COMMENT '总资金费用',
  `status` tinyint NOT NULL DEFAULT '1' COMMENT '状态：1-持仓中，2-已平仓，3-强制平仓',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `is_copy` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '0 用户开单 1跟单',
  `copy_user` bigint DEFAULT NULL COMMENT '交易员用户id',
  PRIMARY KEY (`id`),
  KEY `idx_currency_id` (`currency_id`),
  KEY `idx_margin_mode` (`margin_mode`),
  KEY `idx_status` (`status`),
  KEY `uk_user_currency_side_mode` (`user_id`,`currency_id`,`side`) USING BTREE,
  KEY `idx_copy_id` (`is_copy`,`copy_user`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=112 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='永续合约仓位表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `trade_spot_commission`
--

DROP TABLE IF EXISTS `trade_spot_commission`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `trade_spot_commission` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `user_id` bigint DEFAULT NULL,
  `currency_id` bigint DEFAULT NULL,
  `trigger_price` decimal(20,8) DEFAULT NULL COMMENT '触发价格',
  `amount` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '委托数量',
  `trigger_type` tinyint(1) DEFAULT NULL COMMENT '触发后的下单方式 限价或者市价',
  `place_price` decimal(20,8) DEFAULT NULL COMMENT '触发后的下单价格',
  `order_type` tinyint DEFAULT NULL COMMENT '1止盈止损，2计划委托 3追踪委托',
  `side` tinyint(1) NOT NULL COMMENT '订单方向',
  `trigger_condition` tinyint(1) DEFAULT NULL COMMENT '1 大于等于 -1 小于等于',
  `order_id` bigint DEFAULT NULL COMMENT '关联的现货订单id，触发后生成更新',
  `status` tinyint DEFAULT NULL COMMENT '1完成 -1取消 0等待执行',
  `triggered_at` timestamp NULL DEFAULT NULL COMMENT '触发时间',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  `trigger_reason` varchar(32) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '失败原因',
  PRIMARY KEY (`id`),
  KEY `idx_union` (`user_id`,`currency_id`) USING BTREE,
  KEY `idx_order` (`order_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='现货委托交易订单';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `trade_spot_order`
--

DROP TABLE IF EXISTS `trade_spot_order`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `trade_spot_order` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `user_id` bigint NOT NULL,
  `currency_id` int unsigned NOT NULL,
  `direction` tinyint NOT NULL COMMENT '1买-1卖',
  `order_type` tinyint unsigned NOT NULL COMMENT '订单类型 参考 OrderType',
  `price` decimal(30,8) unsigned NOT NULL DEFAULT '0.********' COMMENT '价格',
  `amount` decimal(30,8) unsigned NOT NULL DEFAULT '0.********' COMMENT '数量',
  `bbo_level` tinyint unsigned DEFAULT '0' COMMENT '限价订单的深度档位',
  `charge` decimal(20,8) unsigned DEFAULT '0.********' COMMENT '手续费',
  `match_order` bigint DEFAULT NULL COMMENT '关联的撮合引擎订单表id',
  `used_amount` decimal(20,8) unsigned NOT NULL DEFAULT '0.********' COMMENT '已使用的冻结金额',
  `frozen_amount` decimal(20,8) unsigned NOT NULL DEFAULT '0.********' COMMENT '冻结资金记录',
  `is_trader` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '是否交易员订单',
  `copy_order_id` bigint unsigned DEFAULT NULL COMMENT '跟随的交易订单id',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_match` (`match_order`) USING BTREE,
  KEY `idx_union` (`user_id`,`currency_id`,`direction`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=149 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='现货交易订单记录表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `user`
--

DROP TABLE IF EXISTS `user`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `user` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '用户ID,主键',
  `username` varchar(20) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '用户名',
  `password` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '密码',
  `user_type` varchar(3) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '100' COMMENT '用户类型:100=系统用户',
  `nickname` varchar(30) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '用户昵称',
  `phone` varchar(11) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '手机',
  `email` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '用户邮箱',
  `avatar` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '用户头像',
  `signed` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '个人签名',
  `status` tinyint NOT NULL DEFAULT '1' COMMENT '状态:1=正常,2=停用',
  `login_ip` varchar(45) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '127.0.0.1' COMMENT '最后登陆IP',
  `login_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '最后登陆时间',
  `backend_setting` json DEFAULT NULL COMMENT '后台设置数据',
  `created_by` bigint NOT NULL DEFAULT '0' COMMENT '创建者',
  `updated_by` bigint NOT NULL DEFAULT '0' COMMENT '更新者',
  `created_at` datetime DEFAULT NULL,
  `updated_at` datetime DEFAULT NULL,
  `remark` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '备注',
  PRIMARY KEY (`id`),
  UNIQUE KEY `user_username_unique` (`username`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户信息表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `user_accounts_assets`
--

DROP TABLE IF EXISTS `user_accounts_assets`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `user_accounts_assets` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `user_id` bigint unsigned NOT NULL,
  `account_type` tinyint unsigned NOT NULL COMMENT '枚举值：AccountType',
  `currency_id` int unsigned NOT NULL COMMENT '对应币种',
  `available` decimal(30,18) unsigned NOT NULL DEFAULT '0.****************00' COMMENT '可用余额',
  `frozen` decimal(30,18) unsigned NOT NULL DEFAULT '0.****************00' COMMENT '冻结资金',
  `locked` decimal(30,18) NOT NULL DEFAULT '0.****************00' COMMENT '锁仓资金',
  `margin_quote` decimal(30,18) NOT NULL DEFAULT '0.****************00' COMMENT '逐仓杠杆交易账户资产',
  `margin_borrow` decimal(30,18) unsigned NOT NULL DEFAULT '0.****************00' COMMENT '杠杆计价货币借款金额',
  `borrowed_amount` decimal(30,18) unsigned NOT NULL DEFAULT '0.****************00' COMMENT '杠杆基础币借贷金额',
  `interest_amount` decimal(30,18) unsigned NOT NULL DEFAULT '0.****************00' COMMENT '杠杆利息金额',
  `status` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '账户状态',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uniod_idx` (`user_id`,`currency_id`,`account_type`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=511 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户账户资产数据表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `user_accounts_flows`
--

DROP TABLE IF EXISTS `user_accounts_flows`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `user_accounts_flows` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `user_id` bigint unsigned NOT NULL,
  `account_id` bigint unsigned NOT NULL DEFAULT '0' COMMENT '账户id',
  `currency_id` int unsigned NOT NULL COMMENT '交易标的',
  `account_type` int DEFAULT NULL COMMENT '交易账户类型',
  `type` int unsigned NOT NULL COMMENT '资金变动类型 枚举类型：FlowsType',
  `amount` decimal(30,18) unsigned NOT NULL DEFAULT '0.****************00' COMMENT '变动金额',
  `before` decimal(30,18) NOT NULL COMMENT '变动前余额',
  `after` decimal(30,18) NOT NULL COMMENT '变动后余额',
  `direction` tinyint(1) NOT NULL COMMENT '-1减 1 加',
  `related_id` bigint DEFAULT NULL COMMENT '关联记录id',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_union` (`user_id`,`account_id`,`currency_id`,`type`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=16322 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户账户资金流水';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `user_belongs_role`
--

DROP TABLE IF EXISTS `user_belongs_role`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `user_belongs_role` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `user_id` bigint NOT NULL COMMENT '用户id',
  `role_id` bigint NOT NULL COMMENT '角色id',
  `created_at` datetime DEFAULT NULL,
  `updated_at` datetime DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `user_custom_withdraw_address`
--

DROP TABLE IF EXISTS `user_custom_withdraw_address`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `user_custom_withdraw_address` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `user_id` bigint DEFAULT NULL COMMENT '用户id',
  `currency_id` int DEFAULT NULL COMMENT '币种id',
  `chain_id` varchar(32) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '链id',
  `address` varchar(256) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '地址',
  `remark` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '备注',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_user` (`user_id`,`currency_id`,`chain_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户快速提币地址数据';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `user_deposit_addresses`
--

DROP TABLE IF EXISTS `user_deposit_addresses`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `user_deposit_addresses` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `user_id` bigint unsigned NOT NULL COMMENT '用户ID',
  `chain_id` varchar(50) NOT NULL COMMENT '链ID',
  `currency_id` int unsigned DEFAULT NULL COMMENT '币种ID(可为空)',
  `address` varchar(255) NOT NULL COMMENT '充值地址',
  `address_type` tinyint NOT NULL DEFAULT '1' COMMENT '地址类型:1=普通地址',
  `public_key` text COMMENT '公钥',
  `private_key_encrypted` text NOT NULL COMMENT '加密私钥',
  `memo` varchar(100) DEFAULT NULL COMMENT '备注标签',
  `status` tinyint NOT NULL DEFAULT '1' COMMENT '状态:1=正常,2=禁用',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_user_chain` (`user_id`,`chain_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_address` (`address`),
  KEY `idx_currency` (`user_id`,`chain_id`,`currency_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=11 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='用户充值地址表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `user_diy_currency`
--

DROP TABLE IF EXISTS `user_diy_currency`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `user_diy_currency` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `user_id` bigint DEFAULT NULL,
  `title` varchar(32) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '自定义自选标题',
  `currency` json DEFAULT NULL COMMENT '自选的币种[currency_id=>markettype]',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_user` (`user_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户自定义栏目自选币种';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `user_login_log`
--

DROP TABLE IF EXISTS `user_login_log`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `user_login_log` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键',
  `username` varchar(20) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '用户名',
  `ip` varchar(45) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '登录IP地址',
  `os` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '操作系统',
  `browser` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '浏览器',
  `status` smallint NOT NULL DEFAULT '1' COMMENT '登录状态 (1成功 2失败)',
  `message` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '提示消息',
  `login_time` datetime NOT NULL COMMENT '登录时间',
  `remark` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`id`),
  KEY `user_login_log_username_index` (`username`)
) ENGINE=InnoDB AUTO_INCREMENT=34 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='登录日志表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `user_margin_borrow`
--

DROP TABLE IF EXISTS `user_margin_borrow`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `user_margin_borrow` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `user_id` bigint unsigned NOT NULL COMMENT '用户ID',
  `currency_id` int unsigned NOT NULL COMMENT '借贷币种ID',
  `account_type` tinyint unsigned NOT NULL COMMENT '账户类型：2-全仓杠杆，3-逐仓杠杆',
  `borrow_amount` decimal(30,18) NOT NULL COMMENT '借贷金额',
  `repaid_amount` decimal(30,18) NOT NULL DEFAULT '0.****************00' COMMENT '已还金额',
  `daily_rate` decimal(10,8) NOT NULL COMMENT '日利率',
  `borrow_source` tinyint NOT NULL DEFAULT '1' COMMENT '借贷来源：1-自动借贷，2-手动借贷',
  `borrow_time` timestamp NOT NULL COMMENT '借贷时间',
  `last_interest_time` timestamp NULL DEFAULT NULL COMMENT '最后计息时间',
  `repay_time` timestamp NULL DEFAULT NULL COMMENT '还款时间',
  `status` tinyint NOT NULL DEFAULT '1' COMMENT '状态：1-借贷中，2-已还清，3-逾期',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_currency_id` (`currency_id`),
  KEY `idx_account_type` (`account_type`),
  KEY `idx_status` (`status`),
  KEY `idx_borrow_time` (`borrow_time`),
  KEY `idx_last_interest_time` (`last_interest_time`)
) ENGINE=InnoDB AUTO_INCREMENT=55 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='用户杠杆借贷记录表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `user_margin_interest`
--

DROP TABLE IF EXISTS `user_margin_interest`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `user_margin_interest` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `user_id` bigint unsigned NOT NULL COMMENT '用户ID',
  `borrow_id` bigint unsigned NOT NULL COMMENT '借贷记录ID',
  `currency_id` int unsigned NOT NULL COMMENT '币种ID',
  `account_type` tinyint unsigned NOT NULL COMMENT '账户类型：2-全仓杠杆，3-逐仓杠杆',
  `interest_amount` decimal(30,18) NOT NULL COMMENT '利息金额',
  `interest_rate` decimal(10,8) NOT NULL COMMENT '利率',
  `calculation_period` int NOT NULL COMMENT '计息周期（小时数）',
  `start_time` timestamp NOT NULL COMMENT '计息开始时间',
  `end_time` timestamp NOT NULL COMMENT '计息结束时间',
  `deduct_time` timestamp NULL DEFAULT NULL COMMENT '实际扣除时间',
  `status` tinyint NOT NULL DEFAULT '1' COMMENT '状态：1-待扣除，2-已扣除，3-扣除失败，4-已豁免',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_borrow_id` (`borrow_id`),
  KEY `idx_currency_id` (`currency_id`),
  KEY `idx_account_type` (`account_type`),
  KEY `idx_status` (`status`),
  KEY `idx_start_time` (`start_time`),
  KEY `idx_deduct_time` (`deduct_time`)
) ENGINE=InnoDB AUTO_INCREMENT=205 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='杠杆利息记录表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `user_operation_log`
--

DROP TABLE IF EXISTS `user_operation_log`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `user_operation_log` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `username` varchar(20) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '用户名',
  `method` varchar(20) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '请求方式',
  `router` varchar(500) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '请求路由',
  `service_name` varchar(30) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '业务名称',
  `ip` varchar(45) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '请求IP地址',
  `created_at` timestamp NULL DEFAULT NULL COMMENT '创建时间',
  `updated_at` timestamp NULL DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`id`),
  KEY `user_operation_log_username_index` (`username`)
) ENGINE=InnoDB AUTO_INCREMENT=784 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='操作日志表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `user_perprtual_config`
--

DROP TABLE IF EXISTS `user_perprtual_config`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `user_perprtual_config` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `user_id` bigint DEFAULT NULL,
  `currency_id` int DEFAULT NULL COMMENT '交易对id',
  `hold_units` tinyint unsigned NOT NULL DEFAULT '1' COMMENT '1 数量 2成本价值 3名义价值',
  `hold_mode` tinyint unsigned NOT NULL DEFAULT '1' COMMENT '1单向持仓 2双向持仓',
  `pnl_source` tinyint unsigned NOT NULL DEFAULT '1' COMMENT '1标记价格 2最新价格',
  `tp_sl_source` tinyint unsigned NOT NULL DEFAULT '1' COMMENT '1最新价格 2标记价格',
  `aassets_mode` tinyint unsigned NOT NULL DEFAULT '1' COMMENT '1单币种保证金 2联合保证金',
  `price_protect` tinyint unsigned NOT NULL DEFAULT '1' COMMENT '价差保护是否开启',
  `lever` int NOT NULL DEFAULT '10' COMMENT '杠杆',
  `iso_long_lever` int NOT NULL DEFAULT '20' COMMENT '逐仓多仓杠杆',
  `iso_short_lever` int NOT NULL DEFAULT '20' COMMENT '逐仓空仓杠杆',
  `margin_type` tinyint DEFAULT NULL COMMENT '默认的保证金类型',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_user` (`user_id`,`currency_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=4 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户合约交易配置';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `user_transfer_records`
--

DROP TABLE IF EXISTS `user_transfer_records`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `user_transfer_records` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `user_id` bigint unsigned NOT NULL COMMENT '用户ID',
  `currency_id` int unsigned NOT NULL COMMENT '币种ID',
  `from_account_type` tinyint NOT NULL COMMENT '转出账户类型枚举：flowstype',
  `to_account_type` tinyint NOT NULL COMMENT '转入账户类型枚举：flowstype',
  `amount` decimal(20,8) NOT NULL COMMENT '划转金额',
  `isolated` tinyint(1) DEFAULT NULL COMMENT '0 基础币 1计价币',
  `status` tinyint NOT NULL DEFAULT '1' COMMENT '状态：1=成功,2=失败,3=处理中',
  `remark` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '备注',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_currency_id` (`currency_id`),
  KEY `idx_user_id` (`user_id`,`currency_id`,`from_account_type`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=15 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户资金划转记录表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `user_withdraw_records`
--

DROP TABLE IF EXISTS `user_withdraw_records`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `user_withdraw_records` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `user_id` bigint unsigned NOT NULL COMMENT '用户ID',
  `currency_id` int unsigned NOT NULL COMMENT '币种ID',
  `withdraw_type` tinyint NOT NULL COMMENT '提币类型:1=链上提币,2=内部邮箱,3=内部手机,4=内部UID',
  `amount` decimal(20,8) NOT NULL COMMENT '提币数量',
  `fee` decimal(20,8) NOT NULL DEFAULT '0.********' COMMENT '手续费',
  `actual_amount` decimal(20,8) NOT NULL COMMENT '实际到账数量',
  `chain_id` varchar(50) DEFAULT NULL COMMENT '提币链ID',
  `to_address` varchar(255) DEFAULT NULL COMMENT '提币地址',
  `memo` varchar(100) DEFAULT NULL COMMENT '备注标签',
  `tx_hash` varchar(255) DEFAULT NULL COMMENT '交易哈希',
  `confirmations` int DEFAULT '0' COMMENT '确认数',
  `to_user_id` bigint unsigned DEFAULT NULL COMMENT '接收用户ID',
  `to_email` varchar(255) DEFAULT NULL COMMENT '接收邮箱',
  `to_phone` varchar(20) DEFAULT NULL COMMENT '接收手机号',
  `to_phone_country_code` varchar(10) DEFAULT NULL COMMENT '手机国家代码',
  `to_uid` varchar(50) DEFAULT NULL COMMENT '接收用户UID',
  `status` tinyint NOT NULL DEFAULT '1' COMMENT '状态:1=待审核,2=处理中,3=已完成,4=已拒绝,5=已取消',
  `remark` text COMMENT '备注说明',
  `admin_remark` text COMMENT '管理员备注',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `completed_at` timestamp NULL DEFAULT NULL COMMENT '完成时间',
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_currency_id` (`currency_id`),
  KEY `idx_withdraw_type` (`withdraw_type`),
  KEY `idx_status` (`status`),
  KEY `idx_to_user_id` (`to_user_id`),
  KEY `idx_created_at` (`created_at`)
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='用户提币记录表';
/*!40101 SET character_set_client = @saved_cs_client */;
/*!40103 SET TIME_ZONE=@OLD_TIME_ZONE */;

/*!40101 SET SQL_MODE=@OLD_SQL_MODE */;
/*!40014 SET FOREIGN_KEY_CHECKS=@OLD_FOREIGN_KEY_CHECKS */;
/*!40014 SET UNIQUE_CHECKS=@OLD_UNIQUE_CHECKS */;
/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
/*!40111 SET SQL_NOTES=@OLD_SQL_NOTES */;

-- Dump completed on 2025-07-21 15:13:48
