#### 5 现货-带单数据统计接口

**GET** `/api/copy/expert/spot/statistics`

**中间件**: TokenMiddleware、SpotExpertMiddleware（验证是否为现货交易专家）

**Query 参数**：

- `days`: integer|nullable (0-全部, 7-7 天, 30-30 天, 90-90 天, 180-180 天)

响应示例：

```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "profit_rate": "12.30", // 收益率 %
    "total_profit": "800.00000000", // 总收益
    "follower_profit": "8000.00000000", // 跟单者收益
    "win_rate": "68.50", // 胜率 %
    "profit_order_count": 35, // 盈利笔数
    "loss_order_count": 16 // 亏损笔数
  }
}
```

**接口逻辑**：

- 根据时间过滤条件查询 copy_spot_expert_statistics 中对应的数据
- 返回统计数据
