<?php

declare(strict_types=1);

namespace HyperfTest\Feature\Copy\Expert\Contract;

use App\Http\Api\Service\Copy\Expert\Contract\ContractSyntheticalService;
use App\Model\Copy\CopyContractExpert;
use App\Model\Copy\CopyContractExpertStatistics;
use App\Model\Copy\CopyContractOrder;
use App\Model\Copy\CopyContractUserSetting;
use App\Model\Copy\CopyExpertLevel;
use App\Model\Copy\CopyFollow;
use App\Model\Copy\Enums\CopyStatus;
use App\Model\Copy\Enums\ExpertStatus;
use App\Model\Copy\Enums\ExpertType;
use App\Model\Enums\User\AccountType;
use App\Model\User\User;
use App\Model\User\UserAccountsAsset;
use App\Service\UserAccounts\UserAccountsAssetService;
use Hyperf\Testing\Client;
use Hyperf\Testing\TestCase;
use Mockery;
use Psr\Container\ContainerInterface;

/**
 * 合约专家综合数据接口测试
 */
class ContractSyntheticalTest extends TestCase
{
    /**
     * @var Client
     */
    protected $client;

    /**
     * @var ContainerInterface
     */
    protected $container;

    /**
     * @var ContractSyntheticalService
     */
    protected $service;

    /**
     * @var UserAccountsAssetService
     */
    protected $assetService;

    public function __construct($name = null, array $data = [], $dataName = '')
    {
        parent::__construct($name, $data, $dataName);
        $this->client = make(Client::class);
        $this->container = $this->client->getContainer();
        $this->service = $this->container->get(ContractSyntheticalService::class);
        $this->assetService = $this->container->get(UserAccountsAssetService::class);
    }

    public function tearDown(): void
    {
        Mockery::close();
    }

    /**
     * 测试获取合约专家综合数据
     */
    public function testGetSyntheticalData()
    {
        // 模拟专家数据
        $expert = new CopyContractExpert();
        $expert->id = 1;
        $expert->user_id = 100;
        $expert->level_id = 2;
        $expert->profit_sharing_rate = 10.00;

        // 模拟专家等级数据
        $level = new CopyExpertLevel();
        $level->max_follow_count = 100;
        $expert->setRelation('level', $level);

        // 模拟请求对象
        $request = Mockery::mock(\Hyperf\HttpServer\Contract\RequestInterface::class);
        $request->shouldReceive('getAttribute')
            ->with('contract_expert')
            ->andReturn($expert);

        // 设置服务类的请求对象
        $this->setProperty($this->service, 'request', $request);

        // 模拟用户跟单配置数据
        $this->mockUserSettings($expert->id);

        // 模拟专家统计数据
        $this->mockExpertStatistics($expert->id);

        // 模拟用户资产数据
        $this->mockUserAssets($expert->user_id);

        // 模拟最近交易时间
        $this->mockLastTradeTime($expert->id);

        // 模拟粉丝数量
        $this->mockFanCount($expert->id);

        // 调用服务方法
        $result = $this->service->getSyntheticalData();

        // 验证结果
        $this->assertIsArray($result);
        $this->assertArrayHasKey('current_follower_count', $result);
        $this->assertArrayHasKey('max_follower_count', $result);
        $this->assertArrayHasKey('total_follower_count', $result);
        $this->assertArrayHasKey('aum', $result);
        $this->assertArrayHasKey('total_assets', $result);
        $this->assertArrayHasKey('last_trade_time', $result);
        $this->assertArrayHasKey('profit_sharing_rate', $result);
        $this->assertArrayHasKey('fan_count', $result);

        // 验证数据值
        $this->assertEquals(5, $result['current_follower_count']);
        $this->assertEquals(100, $result['max_follower_count']);
        $this->assertEquals(150, $result['total_follower_count']);
        $this->assertEquals('50000.00000000', $result['aum']);
        $this->assertEquals('2500.00000000', $result['total_assets']);
        $this->assertNotNull($result['last_trade_time']);
        $this->assertEquals('10.00', $result['profit_sharing_rate']);
        $this->assertEquals(200, $result['fan_count']);
    }

    /**
     * 测试中间件验证（需要合约专家身份）
     */
    public function testContractExpertMiddleware()
    {
        // 测试未登录用户
        $response = $this->client->get('/api/copy/expert/contract/synthetical');
        $this->assertEquals(401, $response['code']);

        // 测试已登录但非专家用户
        $response = $this->client->get('/api/copy/expert/contract/synthetical', [], [
            'Authorization' => 'Bearer test_token'
        ]);

        // 应该返回403（需要专家身份）或401（token无效）
        $this->assertContains($response['code'], [401, 403]);
    }

    /**
     * 设置对象的私有属性值
     */
    private function setProperty($object, $propertyName, $value)
    {
        $reflection = new \ReflectionClass($object);
        $property = $reflection->getProperty($propertyName);
        $property->setAccessible(true);
        $property->setValue($object, $value);
    }

    /**
     * 模拟用户跟单配置数据
     */
    private function mockUserSettings(int $expertId)
    {
        $query = Mockery::mock('query');
        $query->shouldReceive('where')
            ->with('expert_id', $expertId)
            ->andReturnSelf();
        $query->shouldReceive('where')
            ->with('status', CopyStatus::FOLLOWING)
            ->andReturnSelf();
        $query->shouldReceive('count')
            ->andReturn(5);

        CopyContractUserSetting::shouldReceive('query')
            ->andReturn($query);
    }

    /**
     * 模拟专家统计数据
     */
    private function mockExpertStatistics(int $expertId)
    {
        $statistics = new CopyContractExpertStatistics();
        $statistics->total_follower_count = 150;
        $statistics->aum = 50000.00;

        $query = Mockery::mock('query');
        $query->shouldReceive('where')
            ->with('expert_id', $expertId)
            ->andReturnSelf();
        $query->shouldReceive('first')
            ->andReturn($statistics);

        CopyContractExpertStatistics::shouldReceive('query')
            ->andReturn($query);
    }

    /**
     * 模拟用户资产数据
     */
    private function mockUserAssets(int $userId)
    {
        $assets = [
            [
                'available' => 2000.00,
                'frozen' => 500.00,
            ]
        ];

        $this->assetService = Mockery::mock(UserAccountsAssetService::class);
        $this->assetService->shouldReceive('getUserAssets')
            ->with($userId, AccountType::COPY->value)
            ->andReturn($assets);

        $this->setProperty($this->service, 'userAccountsAssetService', $this->assetService);
    }

    /**
     * 模拟最近交易时间
     */
    private function mockLastTradeTime(int $expertId)
    {
        $lastOrder = new CopyContractOrder();
        $lastOrder->created_at = new \Carbon\Carbon('2025-01-01 12:30:00');

        $query = Mockery::mock('query');
        $query->shouldReceive('where')
            ->with('expert_id', $expertId)
            ->andReturnSelf();
        $query->shouldReceive('orderBy')
            ->with('created_at', 'desc')
            ->andReturnSelf();
        $query->shouldReceive('first')
            ->andReturn($lastOrder);

        CopyContractOrder::shouldReceive('query')
            ->andReturn($query);
    }

    /**
     * 模拟粉丝数量
     */
    private function mockFanCount(int $expertId)
    {
        $query = Mockery::mock('query');
        $query->shouldReceive('where')
            ->with('expert_id', $expertId)
            ->andReturnSelf();
        $query->shouldReceive('where')
            ->with('type', ExpertType::CONTRACT)
            ->andReturnSelf();
        $query->shouldReceive('count')
            ->andReturn(200);

        CopyFollow::shouldReceive('query')
            ->andReturn($query);
    }
}
