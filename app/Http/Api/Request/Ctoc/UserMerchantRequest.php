<?php

declare(strict_types=1);
/**
 * UserMerchantRequest
 * Author:XTeam
 * Contact:tg:@chenmaq
 * Version:1.0
 * Date:2025-07-20
 * Website:xxx
 */

namespace App\Http\Api\Request\Ctoc;

use App\Http\Api\Request\BaseFormRequest;
use Hyperf\Validation\Rule;

class UserMerchantRequest extends BaseFormRequest
{
     /**
     * 申请商家验证规则
     *
     * @return array
     */
    public function createRules(): array
    {
        return [
            'user_name' => ['required','string'],
            'id_card' => ['required','string'],
            'id_card_front' => ['required','string'],
            'id_card_back' => ['required','string'],
            'store_name' => ['required','string'],
            'store_logo' => ['required','string'],
            'contact_name' => ['required','string'],
            'contact_phone' => ['required','string'],
            'contact_email' => ['required','string'],
        ];
    }

    /**
     * 字段映射名称
     * return array
     */
    public function attributes(): array
    {
        return [
            'user_name'=> '姓名',
            'id_card'=> '身份证号',
            'id_card_front'=> '身份证正面图片',
            'id_card_back'=> '身份证反面图片',
            'store_name'=> '店铺名称',
            'store_logo'=> '店铺Logo图片',
            'contact_name'=> '联系人姓名',
            'contact_phone'=> '联系电话',
            'contact_email'=> '联系邮箱',
            'remark'=> '审核备注',
        ];
    }
}
