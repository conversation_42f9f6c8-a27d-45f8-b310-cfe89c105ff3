<?php

declare(strict_types=1);
/**
 * UserPaymentRequest
 * Author:XTeam
 * Contact:tg:@chenmaq
 * Version:1.0
 * Date:2025-07-18
 * Website:xxx
 */

namespace App\Http\Api\Request\Ctoc;

use App\Http\Api\Request\BaseFormRequest;
use Hyperf\Validation\Rule;

class UserPaymentRequest extends BaseFormRequest
{
    /**
     * 列表验证规则
     *
     * @return array
     */
    public function listRules(): array
    {
        return [
            'payment_type' => ['required','string']
        ];
    }
    /**
     * 创建验证规则
     *
     * @return array
     */
    public function createRules(): array
    {
        return [
            'payment_type' => ['required','string','exists:system_setting_config,value'],
            'flat_currency_id'=> ['required','integer'],
            'account_info'=> ['required','array'],
        ];
    }
    /**
     * 修改 验证规则
     *
     * @return array
     */
    public function udpateRules(): array
    {
        return [
            'payment_type' => ['required','string','exists:cpx_user_payment,id'],
            'flat_currency_id'=> ['required','integer'],
            'account_info'=> ['required','array'],
        ];
    }
    /**
     * 删除 验证规则
     *
     * @return array
     */
    public function deleteRules(): array
    {
        return [
            'id' => ['required','integer','exists:cpx_user_payment,id'],
        ];
    }

    /**
     * 字段映射名称
     * return array
     */
    public function attributes(): array
    {
        return [
            'payment_type'=>'支付方式',
            'bank_id'=>'银行名称',
            'flat_currency_id'=> '法币',
            'account_info'=>'账号信息'
        ];
    }
}
