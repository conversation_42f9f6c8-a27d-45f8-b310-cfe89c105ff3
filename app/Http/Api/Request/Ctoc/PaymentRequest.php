<?php

declare(strict_types=1);
/**
 * PaymentRequest
 * Author:XTeam
 * Contact:tg:@chenmaq
 * Version:1.0
 * Date:2025-07-17
 * Website:xxx
 */

namespace App\Http\Api\Request\Ctoc;

use App\Http\Api\Request\BaseFormRequest;
use Hyperf\Validation\Rule;

class PaymentRequest extends BaseFormRequest
{
    /**
     * 列表验证规则
     *
     * @return array
     */
    public function listRules(): array
    {
        return [
            'payment_type' => ['required','array']
        ];
    }

    /**
     * 字段映射名称
     * return array
     */
    public function attributes(): array
    {
        return [
            'payment_type' => '支付方式',
        ];
    }
}
