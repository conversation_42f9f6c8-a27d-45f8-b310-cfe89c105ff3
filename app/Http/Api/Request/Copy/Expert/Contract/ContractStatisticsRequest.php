<?php

declare(strict_types=1);
/**
 * 策略平台API
 * 合约专家统计请求验证器
 */

namespace App\Http\Api\Request\Copy\Expert\Contract;

use App\Http\Api\Request\BaseFormRequest;

/**
 * 合约专家统计请求验证器
 */
class ContractStatisticsRequest extends BaseFormRequest
{
    /**
     * 统计数据验证规则
     */
    public function statisticsRules(): array
    {
        return [
            'days' => ['nullable', 'integer', 'in:0,7,30,90,180'],
        ];
    }

    /**
     * 字段映射名称
     */
    public function attributes(): array
    {
        return [
            'days' => '统计天数',
        ];
    }

    /**
     * 自定义错误消息
     */
    public function messages(): array
    {
        return [
            'days.integer' => '统计天数必须为整数',
            'days.in' => '统计天数只能为0、7、30、90、180中的一个',
        ];
    }
}
