<?php

declare(strict_types=1);
/**
 * 策略平台API
 * 现货专家统计请求验证器
 */

namespace App\Http\Api\Request\Copy\Expert\Spot;

use App\Http\Api\Request\BaseFormRequest;

class SpotStatisticsRequest extends BaseFormRequest
{
    /**
     * 统计数据验证规则 - 对应控制器的 statistics 方法
     */
    public function statisticsRules(): array
    {
        return [
            'days' => ['nullable', 'integer', 'in:0,7,30,90,180'],
        ];
    }

    /**
     * 字段映射名称
     */
    public function attributes(): array
    {
        return [
            'days' => '统计天数',
        ];
    }
}
