<?php

declare(strict_types=1);
/**
 * 策略平台API
 * 交易专家申请请求验证器
 */

namespace App\Http\Api\Request\Copy\Expert;

use App\Model\Copy\Enums\ExpertType;
use App\Model\Enums\User\AccountType;
use Hyperf\Validation\Request\FormRequest;
use Hyperf\Validation\Rule;

/**
 * 交易专家申请请求验证器
 */
class ExpertApplicationRequest extends FormRequest
{
    /**
     * 确定用户是否有权发出此请求
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * 获取适用于请求的验证规则
     */
    public function rules(): array
    {
        return [
            'type' => [
                'required',
                'integer',
                Rule::in(array_column(ExpertType::cases(), 'value'))
            ],
            'display_name' => [
                'nullable',
                'string',
                'max:50'
            ],
            'introduction' => [
                'required',
                'string',
                'max:500'
            ],
            'transfer_from_account' => [
                'required_if:type,' . ExpertType::CONTRACT->value,
                'integer',
                Rule::in(array_column(AccountType::cases(), 'value'))
            ],
            'transfer_amount' => [
                'required_if:type,' . ExpertType::CONTRACT->value,
                'numeric',
                'min:0.********'
            ]
        ];
    }

    /**
     * 获取验证错误的自定义属性
     */
    public function attributes(): array
    {
        return [
            'type' => '专家类型',
            'display_name' => '展示名称',
            'introduction' => '个人介绍',
            'transfer_from_account' => '划转资金来源账户',
            'transfer_amount' => '划转金额'
        ];
    }

    /**
     * 获取验证错误的自定义消息
     */
    public function messages(): array
    {
        return [
            'type.required' => '专家类型不能为空',
            'type.integer' => '专家类型必须是整数',
            'type.in' => '专家类型无效',
            'display_name.string' => '展示名称必须是字符串',
            'display_name.max' => '展示名称不能超过50个字符',
            'introduction.required' => '个人介绍不能为空',
            'introduction.string' => '个人介绍必须是字符串',
            'introduction.max' => '个人介绍不能超过500个字符',
            'transfer_from_account.required_if' => '合约专家申请时划转资金来源账户不能为空',
            'transfer_from_account.integer' => '划转资金来源账户必须是整数',
            'transfer_from_account.in' => '划转资金来源账户无效',
            'transfer_amount.required_if' => '合约专家申请时划转金额不能为空',
            'transfer_amount.numeric' => '划转金额必须是数字',
            'transfer_amount.min' => '划转金额必须大于0'
        ];
    }
}
