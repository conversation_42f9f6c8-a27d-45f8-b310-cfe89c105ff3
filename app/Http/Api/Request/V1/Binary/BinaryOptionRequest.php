<?php

declare(strict_types=1);

namespace App\Http\Api\Request\V1\Binary;

use App\Model\Enums\Binary\BinaryOptionEnum;
use Hyperf\Validation\Rule;
use App\Http\Api\Request\BaseFormRequest;

class BinaryOptionRequest extends BaseFormRequest
{
    public function auhorize()
    {
        return true;
    }
    /**
     * 下单验证规则
     */
    public function placeOrderRules(): array
    {
        return [
            'currency_id' => ['required', 'integer', 'min:1'],
            'time_period' => ['required', 'integer', 'min:60'], // 最小1分钟
            'direction' => ['required', Rule::in(array_column(BinaryOptionEnum::cases(), 'value'))],
            'invest_amount' => ['required', 'numeric', 'min:0.00000001'],
        ];
    }

    /**
     * 获取配置验证规则
     */
    public function getConfigRules(): array
    {
        return [
            'currency_id' => ['nullable', 'integer', 'min:1'],
            'time_period' => ['nullable', 'integer', 'min:60'],
        ];
    }

    /**
     * 订单列表验证规则
     */
    public function orderListRules(): array
    {
        return [
            'currency_id' => ['nullable', 'integer', 'min:1'],
            'status' => ['nullable', 'integer', 'min:1'],
            'start_time' => ['nullable', 'date'],
            'end_time' => ['nullable', 'date', 'after:start_time'],
            'page' => ['nullable', 'integer', 'min:1'],
            'limit' => ['nullable', 'integer', 'min:1', 'max:100'],
        ];
    }

    /**
     * 订单详情验证规则
     */
    public function orderDetailRules(): array
    {
        return [
            'order_id' => ['required', 'integer', 'min:1'],
        ];
    }

    /**
     * 取消订单验证规则
     */
    public function cancelOrderRules(): array
    {
        return [
            'order_id' => ['required', 'integer', 'min:1'],
        ];
    }

    /**
     * 统计数据验证规则
     */
    public function statisticsRules(): array
    {
        return [
            'start_date' => ['nullable', 'date'],
            'end_date' => ['nullable', 'date', 'after:start_date'],
            'currency_id' => ['nullable', 'integer', 'min:1'],
        ];
    }

    /**
     * 错误消息
     */
    public function messages(): array
    {
        return [
            'currency_id.required' => '币种ID不能为空',
            'currency_id.integer' => '币种ID必须为整数',
            'time_period.required' => '时间周期不能为空',
            'time_period.min' => '时间周期最小为60秒',
            'direction.required' => '预测方向不能为空',
            'direction.in' => '预测方向无效',
            'invest_amount.required' => '投资金额不能为空',
            'invest_amount.numeric' => '投资金额必须为数字',
            'invest_amount.min' => '投资金额必须大于0',
        ];
    }
}
