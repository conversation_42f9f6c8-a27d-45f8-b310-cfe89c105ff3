<?php

/**
 * TradeSpotService.php
 * Author    chenmaq (<EMAIL>)
 * Version   1.0
 * Date      2025/7/4
 * @link     bbbtrade.net
 * @document bbbtrade.net
 */

namespace App\Http\Api\Service\V1;

use App\Enum\Config\UserVipLevelKey;
use App\Enum\CurrencyConfigKey;
use App\Enum\MarketData\TickerSyncKey;
use App\Enum\MarketType;
use App\Enum\OrderStatus;
use App\Enum\OrderType;
use App\Enum\TradeSide;
use App\Model\Currency\Currency;
use App\Model\Enums\User\AccountType;
use App\Model\Enums\User\FlowsType;
use App\Model\Match\MatchOrder;
use App\Model\Trade\Enums\CommissionOrderType;
use App\Model\Trade\Enums\TriggerCondition;
use App\Model\Trade\Enums\TriggerType;
use App\Model\Trade\TradeConfig;
use App\Model\Trade\TradeSpotOrder;
use App\Model\Match\MatchTrade;
use App\Model\User\UserVipLevel;
use App\Model\User\VipLevel;
use App\Service\MatchEngineOrderService;
use App\Service\RedisFactory\CacheRedis;
use App\Service\RedisFactory\MarketRedis;
use App\Service\UserAccounts\UserAccountsAssetService;
use App\Http\Api\Service\V1\Copy\TraderDetectionService;
use App\Event\Trade\TraderSpotOrderCreatedEvent;
use Hyperf\Context\ApplicationContext;
use Psr\Log\LoggerInterface;
use Hyperf\DbConnection\Db;
use Hyperf\Di\Annotation\Inject;
use Hyperf\Snowflake\IdGeneratorInterface;
use Psr\EventDispatcher\EventDispatcherInterface;

class TradeSpotService
{
    /**
     * 市价单价格波动缓冲比例（5%）
     * 预防市价单实际成交价格高于估算价格的情况
     */
    protected const MARKET_ORDER_BUFFER_RATE = 0.05;

    /**
     * 默认金额精度
     */
    protected const DEFAULT_PRECISION = 8;

    #[Inject]
    public CacheRedis $redis;

    #[Inject]
    protected MarketRedis $marketRedis;

    #[Inject]
    protected UserAccountsAssetService $assetService;

    #[Inject]
    protected MatchEngineOrderService $matchEngineService;

    #[Inject]
    protected IdGeneratorInterface $idGenerator;

    #[Inject]
    protected LoggerInterface $logger;

    #[Inject]
    protected TraderDetectionService $traderDetectionService;

    #[Inject]
    protected EventDispatcherInterface $eventDispatcher;

    /**
     * 获取现货交易配置信息
     * @param int $currency_id
     * @return TradeConfig[]|array|false|\Hyperf\Database\Model\Builder[]|\Hyperf\Database\Model\Collection
     */
    public function getTradeConfig(int $currency_id): array|\Hyperf\Database\Model\Collection|bool
    {
        if(!$this->redis->exists(CurrencyConfigKey::getCurrencyKey($currency_id))){
            return false;
        }
        return TradeConfig::query()
            ->where(TradeConfig::FIELD_CURRENCY_ID,$currency_id)
            ->where(TradeConfig::FIELD_MARKET_TYPE,MarketType::CRYPTO->value)
            ->get();
    }

    /**
     * 现货下单
     */
    public function placeOrder(int $userId, array $orderData): array
    {
        // 验证币种配置
        if (!$this->redis->exists(CurrencyConfigKey::getCurrencyKey($orderData['currency_id']))) {
            throw new \RuntimeException('invalid currency');
        }

        return Db::transaction(function () use ($userId, $orderData) {
            // 1. 动态调整订单数量（针对余额不足的市价买单）
            $adjustedOrderData = $this->adjustOrderQuantityForBalance($orderData, $userId);

            // 2. 计算订单所需资金
            $requiredAmount = $this->calculateRequiredAmount($adjustedOrderData);
            $requiredCurrencyId = $this->getRequiredCurrencyId($adjustedOrderData);

            // 3. 冻结资金
            $this->assetService->freezeAsset(
                $userId,
                AccountType::SPOT->value,
                $requiredCurrencyId,
                $requiredAmount,
                FlowsType::SPOT_TRADE->value
            );

            // 4. 检测交易员身份并设置标识
            $isTrader = $this->traderDetectionService->isActiveTrader($userId);
            if ($isTrader && $this->traderDetectionService->isTraderForCurrency($userId, $adjustedOrderData['currency_id'])) {
                $adjustedOrderData['is_trader'] = 1;
            }

            // 5. 创建现货订单记录（使用调整后的数量）
            $spotOrder = $this->createSpotOrder($userId, $adjustedOrderData);
            $spotOrder->frozen_amount = $requiredAmount; // 记录冻结金额
            $spotOrder->save();

            // 6. 创建撮合引擎订单记录
            $matchOrder = $this->createMatchOrder($userId, $adjustedOrderData, $spotOrder->id);

            // 7. 更新现货订单关联的撮合引擎订单ID
            $spotOrder->match_order = $matchOrder->id;
            $spotOrder->save();

            // 8. 提交到撮合引擎
            $symbol = $this->getCurrencySymbol($adjustedOrderData['currency_id']);
            $engineOrderData = $this->buildEngineOrderData($adjustedOrderData, $matchOrder->order_id, $userId,$symbol);

            $messageId = $this->matchEngineService->addOrder(
                MarketType::CRYPTO->value,
                $symbol,
                $userId,
                $engineOrderData
            );

            if (!$messageId) {
                throw new \RuntimeException('提交撮合引擎失败');
            }

            $result = [
                'order_id' => $spotOrder->id,
                'match_order_id' => $matchOrder->order_id,
                'message_id' => $messageId
            ];

            // 如果数量被调整，返回调整信息
            if ($adjustedOrderData['quantity_adjusted'] ?? false) {
                $result['quantity_adjusted'] = true;
                $result['original_quantity'] = $adjustedOrderData['original_quantity'];
                $result['actual_quantity'] = $adjustedOrderData['quantity'];
                $result['adjustment_reason'] = 'insufficient_balance';
            }

            // 9. 触发交易员开单事件
            if ($spotOrder->getIsTrader() === 1) {
                go(function()use($spotOrder){
                    $this->eventDispatcher->dispatch(new TraderSpotOrderCreatedEvent($spotOrder));
                });
            }

            // 10. 处理止盈止损委托订单
            $commissionOrders = $this->createStopProfitLossOrders($userId, $orderData, $spotOrder);
            if (!empty($commissionOrders)) {
                $result['commission_orders'] = $commissionOrders;
            }

            return $result;
        });
    }

    /**
     * 动态调整订单数量（针对余额不足的市价买单）
     */
    protected function adjustOrderQuantityForBalance(array $orderData, int $userId): array
    {
        if ($orderData['side'] !== 'buy' || $orderData['type'] !== 'market') {
            return $orderData; // 只处理市价买单
        }

        $currencyId = $orderData['currency_id'];
        $quoteCurrencyId = $this->getQuoteCurrencyId($currencyId);

        // 获取用户可用余额
        $userAsset = $this->assetService->getUserAsset($userId, AccountType::SPOT->value, $quoteCurrencyId);
        $availableBalance = $userAsset ? $userAsset->available : 0;

        // 获取用户手续费率（市价单使用taker费率）
        $feeRate = $this->getUserSpotFeeRate($userId, 1); // 1表示市价单
        $estimatedPrice = $this->getEstimatedPrice($currencyId);
        $requestedQuantity = (float)$orderData['quantity'];

        // 计算请求数量需要的总金额（含价格缓冲和手续费）
        $baseAmount = bcmul((string)$requestedQuantity, (string)$estimatedPrice, self::DEFAULT_PRECISION);
        
        // 增加5%的价格波动缓冲
        $bufferAmount = bcmul($baseAmount, (string)self::MARKET_ORDER_BUFFER_RATE, self::DEFAULT_PRECISION);
        $requestedAmount = bcadd($baseAmount, $bufferAmount, self::DEFAULT_PRECISION);
        
        // 加上手续费（基于缓冲后的金额计算）
        $totalRequired = bcadd($requestedAmount,
            bcmul($requestedAmount, (string)$feeRate, self::DEFAULT_PRECISION), self::DEFAULT_PRECISION);

        if (bccomp((string)$availableBalance, $totalRequired) >= 0) {
            // 余额充足，不需要调整
            return $orderData;
        }

        // 余额不足，需要调整数量
        $adjustedQuantity = $this->calculateMaxAffordableQuantity(
            $availableBalance,
            $estimatedPrice,
            $feeRate
        );

        if (bccomp($adjustedQuantity, '0') <= 0) {
            throw new \RuntimeException('余额不足，无法下单');
        }

        // 更新订单数量
        $orderData['quantity'] = (float)$adjustedQuantity;
        $orderData['original_quantity'] = $requestedQuantity; // 保存原始请求数量
        $orderData['quantity_adjusted'] = true; // 标记已调整

        return $orderData;
    }

    /**
     * 计算用户余额能购买的最大数量（考虑价格缓冲）
     */
    protected function calculateMaxAffordableQuantity(float $balance, float $price, float $feeRate): string
    {
        // 公式：balance = quantity × price × (1 + MARKET_ORDER_BUFFER_RATE) × (1 + feeRate)
        // 解得：quantity = balance ÷ (price × (1 + MARKET_ORDER_BUFFER_RATE) × (1 + feeRate))

        $priceWithBuffer = bcmul((string)$price, bcadd('1', (string)self::MARKET_ORDER_BUFFER_RATE, self::DEFAULT_PRECISION), self::DEFAULT_PRECISION);
        $priceWithBufferAndFee = bcmul($priceWithBuffer, bcadd('1', (string)$feeRate, self::DEFAULT_PRECISION), self::DEFAULT_PRECISION);
        $maxQuantity = bcdiv((string)$balance, $priceWithBufferAndFee, self::DEFAULT_PRECISION);

        // 预留一点额外缓冲，避免余额边界问题
        $extraBuffer = bcmul($maxQuantity, '0.001', self::DEFAULT_PRECISION); // 0.1%额外缓冲
        $safeQuantity = bcsub($maxQuantity, $extraBuffer, self::DEFAULT_PRECISION);

        return $safeQuantity;
    }

    /**
     * 获取用户现货交易费率
     */
    protected function getUserSpotFeeRate(int $userId, int $orderType): float
    {
        try {
            $userVipLevel = UserVipLevel::query()
                ->where('user_id', $userId)
                ->where('is_active', 1)
                ->first();

            if (!$userVipLevel) {
                return 0.001; // 默认费率 0.1%
            }

            // 根据订单类型选择费率字段
            // 限价单使用maker费率，市价单使用taker费率
            $feeRateField = ($orderType == 1) ? VipLevel::FIELD_SPOT_TAKER_FEE_RATE : VipLevel::FIELD_SPOT_MAKER_FEE_RATE;

            $vipLevelKey = UserVipLevelKey::getConfigKey($userVipLevel->vip_level_id);
            $feeRate = $this->redis->hGet($vipLevelKey, $feeRateField);

            return $feeRate !== false ? (float)$feeRate : 0.001;
        } catch (\Exception $e) {
            return 0.001; // 默认费率
        }
    }

    /**
     * 现货撤单
     */
    public function cancelOrder(int $userId, int $orderId): bool
    {
        return Db::transaction(function () use ($userId, $orderId) {
            // 查找订单
            $spotOrder = TradeSpotOrder::query()
                ->where('id', $orderId)
                ->where('user_id', $userId)
                ->first();

            if (!$spotOrder) {
                throw new \RuntimeException('订单不存在');
            }

            // 查找撮合引擎订单
            $matchOrder = MatchOrder::query()
                ->where('id', $spotOrder->match_order)
                ->first();

            if (!$matchOrder) {
                throw new \RuntimeException('撮合引擎订单不存在');
            }

            // 检查订单状态是否可以撤单
            if (!in_array($matchOrder->status, [OrderStatus::CREATED->value, OrderStatus::PENDING->value])) {
                throw new \RuntimeException('订单状态不允许撤单');
            }

            // 提交撤单到撮合引擎
            $symbol = $this->getCurrencySymbol($spotOrder->currency_id);
            $success = $this->matchEngineService->cancelOrder(
                MarketType::CRYPTO->value,
                $symbol,
                $matchOrder->order_id,
                $userId
            );

            if (!$success) {
                throw new \RuntimeException('提交撤单请求失败');
            }

            return true;
        });
    }

    /**
     * 计算订单所需资金
     */
    protected function calculateRequiredAmount(array $orderData): float
    {
        $side = $orderData['side'];
        $type = $orderData['type'];
        $quantity = (float)$orderData['quantity'];
        $price = (float)($orderData['price'] ?? 0);

        if ($side === TradeSide::BUY_STRING->value) {
            if ($type === OrderType::MARKET_STRING->value) {
                // 市价买单：需要估算金额并增加价格波动缓冲
                $estimatedPrice = $this->getEstimatedPrice($orderData['currency_id']);
                $baseAmount = bcmul((string)$quantity, (string)$estimatedPrice, self::DEFAULT_PRECISION);
                
                // 增加5%的价格波动缓冲，预防市价单实际成交价格高于估算价格
                $bufferAmount = bcmul($baseAmount, (string)self::MARKET_ORDER_BUFFER_RATE, self::DEFAULT_PRECISION);
                $totalAmount = bcadd($baseAmount, $bufferAmount, self::DEFAULT_PRECISION);
                
                return (float)$totalAmount;
            } else {
                // 限价买单：数量 * 价格（无需缓冲，价格固定）
                return (float)bcmul((string)$quantity, (string)$price, self::DEFAULT_PRECISION);
            }
        } else {
            // 卖单：需要基础币种数量（无需缓冲，数量固定）
            return $quantity;
        }
    }

    /**
     * 获取所需币种ID
     */
    protected function getRequiredCurrencyId(array $orderData): int
    {
        $side = $orderData['side'];

        if ($side === 'buy') {
            return $this->getQuoteCurrencyId($orderData['currency_id']);
        } else {
            // 卖单需要基础币种
            return $orderData['currency_id'];
        }
    }

    /**
     * 创建现货订单记录
     */
    protected function createSpotOrder(int $userId, array $orderData): TradeSpotOrder
    {
        $spotOrder = new TradeSpotOrder();
        $spotOrder->user_id = $userId;
        $spotOrder->currency_id = $orderData['currency_id'];
        $spotOrder->direction = $orderData['side'] === 'buy' ? 1 : -1;
        $spotOrder->order_type = OrderType::getOrderType($orderData['type']);
        $spotOrder->price = (float)($orderData['price'] ?? 0);
        $spotOrder->amount = (float)$orderData['quantity'];
        $spotOrder->bbo_level = (int)($orderData['bbo_level'] ?? 0);
        $spotOrder->charge = 0.0; // 手续费后续计算
        $spotOrder->match_order = 0; // 稍后更新
        $spotOrder->is_trader = (int)($orderData['is_trader'] ?? 0);
        $spotOrder->copy_order_id = $orderData['copy_order_id'] ?? null;

        if (!$spotOrder->save()) {
            throw new \RuntimeException('创建现货订单失败');
        }

        return $spotOrder;
    }

    /**
     * 创建撮合引擎订单记录
     */
    protected function createMatchOrder(int $userId, array $orderData, int $spotOrderId): MatchOrder
    {
        $matchOrder = new MatchOrder();
        $matchOrder->user_id = $userId;
        $matchOrder->currency_id = $orderData['currency_id'];
        $matchOrder->order_id = $this->idGenerator->generate();
        $matchOrder->market_type = MarketType::CRYPTO->value;
        $matchOrder->side = TradeSide::getSideInt($orderData['side']);
        $matchOrder->quantity = (float)$orderData['quantity'];
        $matchOrder->fill_quantity = 0.0;
        $matchOrder->price = (float)($orderData['price'] ?? 0);
        $matchOrder->avg_price = 0.0;
        $matchOrder->order_type = OrderType::getOrderType($orderData['type']);
        $matchOrder->trade_type = 0; // 待成交
        $matchOrder->order_force = $orderData['time_in_force'] ?? 'gtc';
        $matchOrder->fill_time = 0;
        $matchOrder->status = OrderStatus::getOrderStatus('created');
        $matchOrder->reason = '';

        if (!$matchOrder->save()) {
            throw new \RuntimeException('创建撮合引擎订单失败');
        }

        return $matchOrder;
    }

    /**
     * 构建撮合引擎订单数据
     */
    protected function buildEngineOrderData(array $orderData, int $orderId, int $userId,string $symbol): array
    {
        $engineData = [
            'order_id' => $orderId,
            'user_id' => (string)$userId,
            'side' => $orderData['side'],
            'type' => $orderData['type'],
            'quantity' => (float)$orderData['quantity'],
            'time_in_force' => $orderData['time_in_force'] ?? 'gtc',
            'leverage' => 1.0,
            'market_type' => MarketType::getMarketString(MarketType::CRYPTO->value),
            'symbol' => $symbol,
            'is_bot' => 0
        ];

        if ($orderData['type'] === 'limit') {
            $engineData['price'] = (float)$orderData['price'];
        }

        return $engineData;
    }

    /**
     * 计算撤单时需要解冻的资金
     * 
     * @deprecated 此方法不考虑市价单的价格缓冲，可能导致解冻金额不准确
     * 建议使用基于 frozen_amount 和 used_amount 的计算方式
     */
    protected function calculateUnfreezeAmount(TradeSpotOrder $spotOrder, MatchOrder $matchOrder): float
    {
        if ($spotOrder->direction == 1) { // 买单
            // 买单解冻：(原始数量 - 已成交数量) * 价格
            $remainingQuantity = bcsub((string)$matchOrder->quantity, (string)$matchOrder->fill_quantity);
            if ($spotOrder->order_type == 1) { // 市价单
                $estimatedPrice = $this->getEstimatedPrice($spotOrder->currency_id);
                return (float)bcmul($remainingQuantity, (string)$estimatedPrice);
            } else { // 限价单
                return (float)bcmul($remainingQuantity, (string)$spotOrder->price);
            }
        } else { // 卖单
            // 卖单解冻：原始数量 - 已成交数量
            return (float)bcsub((string)$matchOrder->quantity, (string)$matchOrder->fill_quantity);
        }
    }

    /**
     * 获取币种符号
     */
    protected function getCurrencySymbol(int $currencyId): string
    {
        try {
            // 从Redis获取币种配置
            $currencyKey = CurrencyConfigKey::getCurrencyKey($currencyId);
            $symbol = $this->redis->hGet($currencyKey, Currency::FIELD_SYMBOL);

            if ($symbol === false || $symbol === null) {
                throw new \RuntimeException("未找到币种 {$currencyId} 的符号配置");
            }

            return $symbol;

        } catch (\Exception $e) {
            throw new \RuntimeException("获取币种符号失败: " . $e->getMessage());
        }
    }

    /**
     * 获取计价币种ID
     */
    public function getQuoteCurrencyId(int $baseCurrencyId): int
    {
        try {
            // 从Redis获取币种配置
            $currencyKey = CurrencyConfigKey::getCurrencyKey($baseCurrencyId);
            $quoteCurrencyId = $this->redis->hGet($currencyKey, Currency::FIELD_QUOTE_ASSETS_ID);

            if ($quoteCurrencyId === false || $quoteCurrencyId === null) {
                throw new \RuntimeException("未找到币种 {$baseCurrencyId} 的计价币种配置");
            }

            return (int)$quoteCurrencyId;

        } catch (\Exception $e) {
            throw new \RuntimeException("获取计价币种ID失败: " . $e->getMessage());
        }
    }

    /**
     * 获取估算价格（用于市价单）
     */
    protected function getEstimatedPrice(int $currencyId): float
    {
        $key = TickerSyncKey::getOuterTradeKey($currencyId,MarketType::CRYPTO->value);
        $price = $this->marketRedis->hGet($key,'price');

        if (!$price) {
            throw new \RuntimeException('无法获取当前市价');
        }

        return (float)$price;
    }

    /**
     * 查询用户挂单（未完成订单）
     */
    public function getPendingOrders(int $userId, ?int $currencyId = null, int $page = 1, int $pageSize = 20): array
    {
        // 构建查询，使用模型关联进行条件过滤，但不预加载关联数据
        $query = TradeSpotOrder::query()
            ->where('user_id', $userId)
            ->whereHas('matchOrder', function ($query) {
                $query->where('status', 1); // 只查询 status = 1 的挂单
            })
            ->orderBy('created_at', 'desc');

        // 如果指定了币种，添加币种过滤
        if ($currencyId !== null) {
            $query->where('currency_id', $currencyId);
        }

        // 分页查询
        $offset = ($page - 1) * $pageSize;
        $total = $query->count();
        $orders = $query->offset($offset)->limit($pageSize)->get();

        return [
            'data' => $orders,
            'pagination' => [
                'current_page' => $page,
                'page_size' => $pageSize,
                'total' => $total,
                'total_pages' => ceil($total / $pageSize)
            ]
        ];
    }

    /**
     * 修改挂单
     */
    public function modifyOrder(int $userId, int $orderId, float $price, float $quantity): array
    {
        return Db::transaction(function () use ($userId, $orderId, $price, $quantity) {
            // 1. 查找现货订单
            $spotOrder = TradeSpotOrder::query()
                ->where('id', $orderId)
                ->where('user_id', $userId)
                ->first();

            if (!$spotOrder) {
                throw new \RuntimeException('订单不存在');
            }

            // 2. 查找撮合引擎订单
            $matchOrder = MatchOrder::query()
                ->where('id', $spotOrder->match_order)
                ->first();

            if (!$matchOrder) {
                throw new \RuntimeException('撮合引擎订单不存在');
            }

            // 3. 检查订单状态是否可以修改
            if (!in_array($matchOrder->status, [OrderStatus::CREATED->value, OrderStatus::PENDING->value])) {
                throw new \RuntimeException('订单状态不允许修改，当前状态：' . $matchOrder->status);
            }

            // 4. 检查订单类型是否可以修改（只有限价单可以修改）
            if ($spotOrder->order_type == intval(OrderType::MARKET_INT->value)) {
                throw new \RuntimeException('市价单不支持修改');
            }

            // 5. 检查是否有部分成交（有成交的订单不能修改）
            if ($matchOrder->fill_quantity > 0) {
                throw new \RuntimeException('已有部分成交的订单不能修改');
            }

            // 6. 验证币种配置
            $this->validateModifyParams($spotOrder->currency_id, $price, $quantity);

            // 7. 验证用户余额是否支持修改
            $this->validateModifyBalance($userId, $spotOrder, $price, $quantity);

            // 8. 提交修改到撮合引擎
            $symbol = $this->getCurrencySymbol($spotOrder->currency_id);
            $success = $this->matchEngineService->modifyOrder(
                $userId,
                MarketType::CRYPTO->value,
                $symbol,
                $matchOrder->order_id,
                $price,
                $quantity,
                $matchOrder->order_force
            );

            if (!$success) {
                throw new \RuntimeException('提交修改请求到撮合引擎失败');
            }

            return [
                'order_id' => $spotOrder->id,
                'match_order_id' => $matchOrder->order_id,
                'old_price' => (string)$matchOrder->price,
                'new_price' => (string)$price,
                'old_quantity' => (string)$matchOrder->quantity,
                'new_quantity' => (string)$quantity,
                'modify_time' => date('Y-m-d H:i:s'),
                'message_id' => $success
            ];
        });
    }

    /**
     * 验证修改订单的参数
     */
    protected function validateModifyParams(int $currencyId, float $price, float $quantity): void
    {
        // 验证币种配置是否存在
        if (!$this->redis->exists(CurrencyConfigKey::getCurrencyKey($currencyId))) {
            throw new \RuntimeException('币种配置不存在');
        }

        // 这里可以添加更多的价格和数量验证逻辑
        // 比如最小价格、最大价格、最小数量、最大数量等
        if ($price <= 0) {
            throw new \RuntimeException('价格必须大于0');
        }

        if ($quantity <= 0) {
            throw new \RuntimeException('数量必须大于0');
        }
    }

    /**
     * 验证用户余额是否支持修改订单
     */
    protected function validateModifyBalance(int $userId, TradeSpotOrder $spotOrder, float $price, float $quantity): void
    {
        // 计算新的资金需求
        $newRequiredAmount = $this->calculateModifyRequiredAmount($spotOrder, $price, $quantity);
        $oldFrozenAmount = $spotOrder->frozen_amount;
        $amountDifference = bcadd((string)$newRequiredAmount, bcmul((string)$oldFrozenAmount, '-1', self::DEFAULT_PRECISION), self::DEFAULT_PRECISION);

        // 如果需要额外冻结资金，检查余额是否充足
        if (bccomp($amountDifference, '0', self::DEFAULT_PRECISION) > 0) {
            $requiredCurrencyId = $this->getRequiredCurrencyIdForOrder($spotOrder);
            $userAsset = $this->assetService->getUserAsset($userId, AccountType::SPOT->value, $requiredCurrencyId);
            $availableBalance = $userAsset ? $userAsset->available : 0;

            if (bccomp((string)$availableBalance, $amountDifference, self::DEFAULT_PRECISION) < 0) {
                throw new \RuntimeException('余额不足，无法修改订单');
            }
        }
    }

    /**
     * 计算修改订单后的资金需求
     */
    protected function calculateModifyRequiredAmount(TradeSpotOrder $spotOrder, float $price, float $quantity): float
    {
        if ($spotOrder->direction == 1) { // 买单
            // 买单需要：数量 * 价格
            return (float)bcmul((string)$quantity, (string)$price, self::DEFAULT_PRECISION);
        } else { // 卖单
            // 卖单需要：数量
            return $quantity;
        }
    }

    /**
     * 获取订单所需的币种ID
     */
    protected function getRequiredCurrencyIdForOrder(TradeSpotOrder $spotOrder): int
    {
        if ($spotOrder->direction == 1) { // 买单
            // 买单需要计价币种
            return $this->getQuoteCurrencyId($spotOrder->currency_id);
        } else { // 卖单
            // 卖单需要基础币种
            return $spotOrder->currency_id;
        }
    }

    /**
     * 批量撤销所有未成交的挂单
     * @param int $userId 用户ID
     * @param int|null $currencyId 可选的币种ID，如果指定则只撤销该币种的订单
     * @return array 返回撤销结果统计
     */
    public function cancelAllOrders(int $userId, ?int $currencyId = null): array
    {
        return Db::transaction(function () use ($userId, $currencyId) {
            // 1. 查询所有未成交的挂单
            $pendingOrdersQuery = TradeSpotOrder::query()
                ->where('user_id', $userId)
                ->where('status', OrderStatus::PENDING->value)
                ->whereHas('matchOrder', function ($query) {
                    $query->where('status', 1); // 撮合引擎中状态为1的订单
                });

            // 如果指定了币种，添加币种过滤条件
            if ($currencyId !== null) {
                $pendingOrdersQuery->where('currency_id', $currencyId);
            }

            $pendingOrders = $pendingOrdersQuery->get();

            $totalCount = $pendingOrders->count();
            $successCount = 0;
            $failedCount = 0;
            $errors = [];

            if ($totalCount === 0) {
                return [
                    'total_count' => 0,
                    'success_count' => 0,
                    'failed_count' => 0,
                    'errors' => []
                ];
            }

            // 2. 逐个撤销订单
            foreach ($pendingOrders as $spotOrder) {
                try {
                    // 获取撮合引擎订单信息
                    $matchOrder = $spotOrder->matchOrder;
                    if (!$matchOrder) {
                        $errors[] = "订单 {$spotOrder->id} 缺少撮合引擎订单信息";
                        $failedCount++;
                        continue;
                    }

                    // 获取币种交易对符号
                    $symbol = $this->getCurrencySymbol($spotOrder->currency_id);

                    // 提交撤单请求到撮合引擎
                    $messageId = $this->matchEngineService->cancelOrder(
                        MarketType::CRYPTO->value,
                        $symbol,
                        $userId,
                        $matchOrder->order_id
                    );

                    if ($messageId) {
                        $successCount++;
                        
                        // 更新现货订单状态为撤销中
                        $spotOrder->status = OrderStatus::CANCELED->value;
                        $spotOrder->save();
                    } else {
                        $errors[] = "订单 {$spotOrder->id} 提交撮合引擎失败";
                        $failedCount++;
                    }

                } catch (\Exception $e) {
                    $errors[] = "订单 {$spotOrder->id} 撤销失败: " . $e->getMessage();
                    $failedCount++;
                }
            }

            return [
                'total_count' => $totalCount,
                'success_count' => $successCount,
                'failed_count' => $failedCount,
                'errors' => $errors
            ];
        });
    }

    /**
     * 创建止盈止损委托订单
     */
    protected function createStopProfitLossOrders(int $userId, array $orderData, TradeSpotOrder $spotOrder): array
    {
        $commissionOrders = [];
        $takeProfitPrice = $orderData['take_profit_price'] ?? null;
        $stopLossPrice = $orderData['stop_loss_price'] ?? null;

        if (!$takeProfitPrice && !$stopLossPrice) {
            return $commissionOrders;
        }

        // 确定委托订单的方向（与原订单相反）
        $commissionSide = $orderData['side'] === 'buy' ? -1 : 1; // 买入后设置卖出委托，卖出后设置买入委托
        $quantity = (string)$orderData['quantity'];

        try {
            $commissionService = ApplicationContext::getContainer()->get(TradeSpotCommissionService::class);
            // 创建止盈委托订单
            if ($takeProfitPrice) {
                $takeProfitCondition = $orderData['side'] === 'buy' 
                    ? TriggerCondition::GREATER_THAN_OR_EQUAL // 买入后价格上涨触发卖出
                    : TriggerCondition::LESS_THAN_OR_EQUAL;   // 卖出后价格下跌触发买入

                $takeProfitOrder = $commissionService->placeOrder(
                    $userId,
                    $orderData['currency_id'],
                    $commissionSide,
                    CommissionOrderType::STOP_PROFIT_LOSS->value,
                    $takeProfitCondition->value,
                    $takeProfitPrice,
                    $quantity,
                    TriggerType::MARKET->value, // 默认市价单
                    null
                );

                $commissionOrders[] = [
                    'type' => 'take_profit',
                    'commission_id' => $takeProfitOrder->id,
                    'trigger_price' => $takeProfitPrice,
                    'quantity' => $quantity
                ];
            }

            // 创建止损委托订单
            if ($stopLossPrice) {
                $stopLossCondition = $orderData['side'] === 'buy' 
                    ? TriggerCondition::LESS_THAN_OR_EQUAL    // 买入后价格下跌触发卖出
                    : TriggerCondition::GREATER_THAN_OR_EQUAL; // 卖出后价格上涨触发买入

                $stopLossOrder = $commissionService->placeOrder(
                    $userId,
                    $orderData['currency_id'],
                    $commissionSide,
                    CommissionOrderType::STOP_PROFIT_LOSS->value,
                    $stopLossCondition->value,
                    $stopLossPrice,
                    $quantity,
                    TriggerType::MARKET->value, // 默认市价单
                    null
                );

                $commissionOrders[] = [
                    'type' => 'stop_loss',
                    'commission_id' => $stopLossOrder->id,
                    'trigger_price' => $stopLossPrice,
                    'quantity' => $quantity
                ];
            }

        } catch (\Exception $e) {
            // 记录日志但不影响主订单
            $this->logger->error('创建止盈止损委托订单失败', [
                'user_id' => $userId,
                'spot_order_id' => $spotOrder->id,
                'take_profit_price' => $takeProfitPrice,
                'stop_loss_price' => $stopLossPrice,
                'error' => $e->getMessage()
            ]);
        }

        return $commissionOrders;
    }

    /**
     * 获取用户成交明细
     */
    public function getTradeHistory(int $userId, ?int $currencyId = null, int $page = 1, int $pageSize = 20): array
    {
        $query = MatchTrade::query()
            ->where(function ($q) use ($userId) {
                $q->where('buy_user_id', $userId)
                  ->orWhere('sell_user_id', $userId);
            });

        // 可选的币种过滤
        if ($currencyId) {
            $query->where('currency_id', $currencyId);
        }

        // 预加载关联数据
        $query->with([
            'buyMatchOrder',
            'sellMatchOrder',
            'buySpotOrder' => function ($q) use ($userId) {
                $q->where('trade_spot_order.user_id', $userId);
            },
            'sellSpotOrder' => function ($q) use ($userId) {
                $q->where('trade_spot_order.user_id', $userId);
            }
        ]);

        // 分页查询
        $trades = $query->orderBy('created_at', 'desc')
                       ->paginate($pageSize, ['*'], 'page', $page);

        // 格式化数据
        $data = [];
        foreach ($trades->items() as $trade) {
            $isBuyer = $trade->buy_user_id == $userId;
            $spotOrder = $isBuyer ? $trade->buySpotOrder : $trade->sellSpotOrder;
            $matchOrder = $isBuyer ? $trade->buyMatchOrder : $trade->sellMatchOrder;

            $data[] = [
                'trade_id' => $trade->trade_id,
                'price' => (string)$trade->price,
                'quantity' => (string)$trade->quantity,
                'commission' => $spotOrder ? (string)$spotOrder->commission : '0',
                'order_type' => $matchOrder ? $matchOrder->order_type : null,
                'side' => $isBuyer ? 'buy' : 'sell',
                'currency_id' => $trade->currency_id,
                'created_at' => $trade->created_at->toDateTimeString()
            ];
        }

        return [
            'data' => $data,
            'pagination' => [
                'current_page' => $trades->currentPage(),
                'per_page' => $trades->perPage(),
                'total' => $trades->total(),
                'last_page' => $trades->lastPage(),
                'has_more' => $trades->hasMorePages()
            ]
        ];
    }

}