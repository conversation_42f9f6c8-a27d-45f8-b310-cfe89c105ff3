<?php

declare(strict_types=1);

namespace App\Http\Api\Service\V1\Asset;

use App\Enum\CurrencyConfigKey;
use App\Exception\BusinessException;
use App\Http\Api\Service\BaseService;
use App\Http\Common\ResultCode;
use App\Model\Enums\User\AccountType;
use App\Model\Enums\User\FlowsType;
use App\Model\User\UserAccountsAsset;
use App\Model\User\UserAccountsFlow;
use Hyperf\Di\Annotation\Inject;
use Hyperf\Redis\Redis;

class AssetService extends BaseService
{
    #[Inject]
    protected Redis $redis;

    /**
     * 获取用户全部资产(不计算价值)
     */
    public function getUserAllAssets(): array
    {
        $userId = $this->userId();
        if (!$userId) {
            throw new BusinessException(ResultCode::UNAUTHORIZED, '用户未登录');
        }

        // 使用join查询用户资产和币种信息，避免循环查询
        $assets = UserAccountsAsset::query()
            ->select([
                'user_accounts_assets.account_type',
                'user_accounts_assets.currency_id',
                'user_accounts_assets.available',
                'user_accounts_assets.frozen',
                'user_accounts_assets.margin_quote',
                'user_accounts_assets.locked',
                'currency.symbol',
                'currency_mate.logo'
            ])
            ->join('currency', 'user_accounts_assets.currency_id', '=', 'currency.id')
            ->leftJoin('currency_mate', 'currency.id', '=', 'currency_mate.currency_id')
            ->where('user_accounts_assets.user_id', $userId)
            ->where('user_accounts_assets.status', 1)
            ->get()
            ->toArray();

        // 按账户类型分组
        $accounts = [];
        foreach ($assets as $asset) {
            $accountType = $asset['account_type'];

            if (!isset($accounts[$accountType])) {
                $accounts[$accountType] = [
                    'account_type' => $accountType,
                    'account_name' => $this->getAccountTypeName($accountType),
                    'currencies' => [],
                ];
            }

            $currencyAssets = [
                'currency_id' => $asset['currency_id'],
                'symbol' => $asset['symbol'] ?: '',
                'logo' => $asset['logo'] ?: '',
                'available' => number_format($asset['available'], 8, '.', ''),
                'frozen' => number_format($asset['frozen'], 8, '.', '')
            ];

            if ($accountType === AccountType::ISOLATED->value) {
                $currencyAssets = array_merge($currencyAssets, [
                    'margin_quote' => number_format($asset['margin_quote'], 8, '.', ''),
                    'margin_frozen' => number_format($asset['locked'], 8, '.', '')
                ]);
            }

            $accounts[$accountType]['currencies'][] = $currencyAssets;
        }

        return ['accounts' => array_values($accounts)];
    }

    /**
     * 获取单币种资产详情
     */
    public function getCurrencyAssetDetail(int $currencyId): array
    {
        $userId = $this->userId();
        if (!$userId) {
            throw new BusinessException(ResultCode::UNAUTHORIZED, '用户未登录');
        }

        // 使用JOIN查询获取币种base_asset，避免Redis查询
        $assets = UserAccountsAsset::query()
            ->select([
                'user_accounts_assets.account_type',
                'user_accounts_assets.available',
                'user_accounts_assets.frozen',
                'user_accounts_assets.margin_quote',
                'user_accounts_assets.locked',
                'currency.base_asset as symbol'
            ])
            ->leftJoin('currency', 'user_accounts_assets.currency_id', '=', 'currency.id')
            ->where('user_accounts_assets.user_id', $userId)
            ->where('user_accounts_assets.currency_id', $currencyId)
            ->where('user_accounts_assets.status', 1)
            ->get();

        $accounts = [];
        $totalBalance = 0;
        $symbol = '';

        foreach ($assets as $asset) {
            $available = $asset['available'];
            $frozen = $asset['frozen'];
            $total = $available + $frozen;

            if (empty($symbol)) {
                $symbol = $asset['symbol'];
            }

            if ($total > 0) {
                $currencyAssets = [
                    'account_type' => $asset['account_type'],
                    'account_name' => $this->getAccountTypeName($asset['account_type']),
                    'available' => number_format($available, 8, '.', ''),
                    'frozen' => number_format($frozen, 8, '.', '')
                ];
                if ($asset['account_type'] === AccountType::ISOLATED->value) {
                    $currencyAssets = array_merge($currencyAssets, [
                        'margin_quote' => number_format($asset['margin_quote'], 8, '.', ''),
                        'margin_frozen' => number_format($asset['locked'], 8, '.', '')
                    ]);
                }

                $accounts[] = $currencyAssets;

                $totalBalance += $total;
            }
        }

        return [
            'currency_id' => $currencyId,
            'symbol' => $symbol,
            'total_balance' => number_format($totalBalance, 8, '.', ''),
            'accounts' => $accounts,
        ];
    }

    /**
     * 获取资产流水记录(不含类型名称)
     */
    public function getAssetFlows(array $filters = []): array
    {
        $userId = $this->userId();
        if (!$userId) {
            throw new BusinessException(ResultCode::UNAUTHORIZED, '用户未登录');
        }

        // 使用JOIN查询避免在循环中读取Redis币种信息
        $query = UserAccountsFlow::query()
            ->select([
                'user_accounts_flows.id',
                'user_accounts_flows.currency_id',
                'user_accounts_flows.type as flow_type',
                'user_accounts_flows.direction',
                'user_accounts_flows.amount',
                'user_accounts_flows.before as balance_before',
                'user_accounts_flows.after as balance_after',
                'user_accounts_flows.created_at',
                'currency.base_asset as symbol'
            ])
            ->join('currency', 'user_accounts_flows.currency_id', '=', 'currency.id')
            ->where('user_accounts_flows.user_id', $userId);

        // 应用筛选条件
        if (!empty($filters['currency_id'])) {
            $query->where('user_accounts_flows.currency_id', $filters['currency_id']);
        }

        if (!empty($filters['flow_type'])) {
            $query->where('user_accounts_flows.type', $filters['flow_type']);
        }

        if (!empty($filters['start_time'])) {
            $query->where('user_accounts_flows.created_at', '>=', date('Y-m-d H:i:s', $filters['start_time']));
        }

        if (!empty($filters['end_time'])) {
            $query->where('user_accounts_flows.created_at', '<=', date('Y-m-d H:i:s', $filters['end_time']));
        }

        // 分页参数
        $page = $filters['page'] ?? 1;
        $limit = min($filters['limit'] ?? 20, 100);
        $offset = ($page - 1) * $limit;

        // 获取总数
        $total = $query->count();

        // 获取数据
        $flows = $query->orderBy('user_accounts_flows.created_at', 'desc')
            ->offset($offset)
            ->limit($limit)
            ->get();

        $data = [];
        foreach ($flows as $flow) {
            $data[] = [
                'id' => $flow['id'],
                'currency_id' => $flow['currency_id'],
                'symbol' => $flow['symbol'],
                'flow_type' => $flow['flow_type'],
                'direction' => $flow['direction'],
                'amount' => number_format((float)$flow['amount'], 8, '.', ''),
                'balance_before' => number_format((float)$flow['balance_before'], 8, '.', ''),
                'balance_after' => number_format((float)$flow['balance_after'], 8, '.', ''),
                'created_at' => $flow['created_at']->format('Y-m-d H:i:s'),
            ];
        }

        return [
            'current_page' => $page,
            'per_page' => $limit,
            'total' => $total,
            'last_page' => ceil($total / $limit),
            'data' => $data,
        ];
    }

    /**
     * 获取流水类型解释(支持多语言)
     */
    public function getFlowTypes(string $lang = 'zh-CN'): array
    {
        $types = [];
        foreach (FlowsType::cases() as $flowType) {
            $types[$flowType->value] = $lang === 'en-US' ? $this->getFlowTypeEnglishName($flowType) : $this->getFlowTypeName($flowType);
        }

        return $types;
    }

    /**
     * 获取资产统计概览
     */
    public function getAssetSummary(): array
    {
        $userId = $this->userId();
        if (!$userId) {
            throw new BusinessException(ResultCode::UNAUTHORIZED, '用户未登录');
        }

        // 使用JOIN查询避免在循环中读取Redis币种信息
        $assets = UserAccountsAsset::query()
            ->select([
                'user_accounts_assets.account_type',
                'user_accounts_assets.currency_id',
                'user_accounts_assets.available',
                'user_accounts_assets.frozen',
                'currency.base_asset as symbol'
            ])
            ->join('currency', 'user_accounts_assets.currency_id', '=', 'currency.id')
            ->where('user_accounts_assets.user_id', $userId)
            ->where('user_accounts_assets.status', 1)
            ->where(function ($query) {
                $query->where('user_accounts_assets.available', '>', 0)
                    ->orWhere('user_accounts_assets.frozen', '>', 0);
            })
            ->get();

        // 按账户类型统计
        $accountDistribution = [];
        $currencyDistribution = [];

        foreach ($assets as $asset) {
            $accountType = $asset['account_type'];
            $currencyId = $asset['currency_id'];
            $total = $asset['available'] + $asset['frozen'];

            // 账户分布统计
            if (!isset($accountDistribution[$accountType])) {
                $accountDistribution[$accountType] = [
                    'account_type' => $accountType,
                    'account_name' => $this->getAccountTypeName($accountType),
                    'currency_count' => 0,
                ];
            }
            $accountDistribution[$accountType]['currency_count']++;

            // 币种分布统计
            if (!isset($currencyDistribution[$currencyId])) {
                $currencyDistribution[$currencyId] = [
                    'currency_id' => $currencyId,
                    'symbol' => $asset['symbol'],
                    'total_balance' => 0,
                    'account_count' => 0,
                ];
            }
            $currencyDistribution[$currencyId]['total_balance'] += $total;
            $currencyDistribution[$currencyId]['account_count']++;
        }

        // 格式化币种分布数据
        $topCurrencies = array_values($currencyDistribution);
        usort($topCurrencies, fn($a, $b) => $b['total_balance'] <=> $a['total_balance']);

        // 只返回前10个币种
        $topCurrencies = array_slice($topCurrencies, 0, 10);
        foreach ($topCurrencies as &$currency) {
            $currency['total_balance'] = number_format($currency['total_balance'], 8, '.', '');
        }

        return [
            'account_distribution' => array_values($accountDistribution),
            'top_currencies' => $topCurrencies,
            'total_currencies' => count($currencyDistribution),
            'total_accounts' => count($accountDistribution),
        ];
    }

    /**
     * 获取币种信息
     */
    private function getCurrencyInfo(int $currencyId): array
    {
        try {
            $currencyKey = CurrencyConfigKey::getCurrencyKey($currencyId);
            $symbol = $this->redis->hGet($currencyKey, 'symbol') ?: '';
            $logo = $this->redis->hGet($currencyKey, 'logo') ?: '';

            return [
                'symbol' => $symbol,
                'logo' => $logo,
            ];
        } catch (\Exception $e) {
            return [
                'symbol' => '',
                'logo' => '',
            ];
        }
    }

    /**
     * 获取账户类型名称
     */
    private function getAccountTypeName(int $accountType): string
    {
        return match ($accountType) {
            AccountType::WALLET->value => '资金钱包',
            AccountType::SPOT->value => '现货钱包',
            AccountType::FUTURES->value => '合约钱包',
            AccountType::MARGIN->value => '全仓杠杆钱包',
            AccountType::ISOLATED->value => '逐仓杠杆钱包',
            AccountType::CHAIN->value => '链上交易钱包',
            AccountType::COPY->value => '跟单钱包',
            AccountType::FUNDING->value => '理财钱包',
            default => '未知账户',
        };
    }

    /**
     * 获取流水类型名称
     */
    private function getFlowTypeName(FlowsType $flowType): string
    {
        return match ($flowType) {
            FlowsType::RECHARGE => '充值',
            FlowsType::WITHDRAW => '提币',
            FlowsType::TRANSFER => '划转',
            FlowsType::SPOT_TRADE => '现货交易',
            FlowsType::SPOT_TRADE_FEE => '现货交易手续费',
            FlowsType::SPOT_TRADE_REBATE => '现货交易手续费返佣',
            FlowsType::FUTURES_TRADE => '合约交易',
            FlowsType::FUTURES_TRADE_FEE => '合约交易手续费',
            FlowsType::FUTURES_TRADE_REBATE => '合约交易手续费返佣',
            FlowsType::PERPETUAL_MARGIN_FREEZE => '永续合约保证金冻结',
            FlowsType::PERPETUAL_MARGIN_UNFREEZE => '永续合约保证金解冻',
            FlowsType::PERPETUAL_TRADE => '永续合约交易',
            FlowsType::PERPETUAL_TRADE_FEE => '永续合约交易手续费',
            FlowsType::PERPETUAL_FUNDING_FEE => '永续合约资金费用',
            FlowsType::MARGIN_TRADE => '杠杆交易',
            FlowsType::MARGIN_TRADE_FEE => '杠杆交易手续费',
            FlowsType::MARGIN_BORROW => '杠杆借款',
            FlowsType::MARGIN_AUTO_BORROW => '杠杆自动借款',
            FlowsType::MARGIN_REPAY => '杠杆还款',
            FlowsType::MARGIN_INTEREST => '杠杆利息',
            FlowsType::INTERNAL_TRANSFER_OUT => '内部转账转出',
            FlowsType::INTERNAL_TRANSFER_IN => '内部转账转入',
            FlowsType::TRANSFER_OUT => '划转转出',
            FlowsType::TRANSFER_IN => '划转转入',
            FlowsType::COPY_SPOT_PROFIT_SHARING_OUT => '现货跟单分润支出',
            FlowsType::COPY_SPOT_PROFIT_SHARING_IN => '现货跟单分润收入',
            default => '未知类型',
        };
    }

    /**
     * 获取流水类型英文名称
     */
    private function getFlowTypeEnglishName(FlowsType $flowType): string
    {
        return match ($flowType) {
            FlowsType::RECHARGE => 'Deposit',
            FlowsType::WITHDRAW => 'Withdraw',
            FlowsType::TRANSFER => 'Transfer',
            FlowsType::SPOT_TRADE => 'Spot Trade',
            FlowsType::SPOT_TRADE_FEE => 'Spot Trade Fee',
            FlowsType::SPOT_TRADE_REBATE => 'Spot Trade Rebate',
            FlowsType::FUTURES_TRADE => 'Futures Trade',
            FlowsType::FUTURES_TRADE_FEE => 'Futures Trade Fee',
            FlowsType::FUTURES_TRADE_REBATE => 'Futures Trade Rebate',
            FlowsType::PERPETUAL_MARGIN_FREEZE => 'Perpetual Margin Freeze',
            FlowsType::PERPETUAL_MARGIN_UNFREEZE => 'Perpetual Margin Unfreeze',
            FlowsType::PERPETUAL_TRADE => 'Perpetual Trade',
            FlowsType::PERPETUAL_TRADE_FEE => 'Perpetual Trade Fee',
            FlowsType::PERPETUAL_FUNDING_FEE => 'Perpetual Funding Fee',
            FlowsType::MARGIN_TRADE => 'Margin Trade',
            FlowsType::MARGIN_TRADE_FEE => 'Margin Trade Fee',
            FlowsType::MARGIN_BORROW => 'Margin Borrow',
            FlowsType::MARGIN_AUTO_BORROW => 'Margin Auto Borrow',
            FlowsType::MARGIN_REPAY => 'Margin Repay',
            FlowsType::MARGIN_INTEREST => 'Margin Interest',
            FlowsType::INTERNAL_TRANSFER_OUT => 'Internal Transfer Out',
            FlowsType::INTERNAL_TRANSFER_IN => 'Internal Transfer In',
            FlowsType::TRANSFER_OUT => 'Transfer Out',
            FlowsType::TRANSFER_IN => 'Transfer In',
            FlowsType::COPY_SPOT_PROFIT_SHARING_OUT => 'Spot Copy Profit Sharing Out',
            FlowsType::COPY_SPOT_PROFIT_SHARING_IN => 'Spot Copy Profit Sharing In',
            default => 'Unknown Type',
        };
    }
}
