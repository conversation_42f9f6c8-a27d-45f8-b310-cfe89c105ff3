<?php

declare(strict_types=1);

namespace App\Http\Api\Service\V1\Copy;

use App\Enum\MarketType;
use App\Enum\OrderType;
use App\Model\Copy\CopySpotUserSetting;
use App\Model\Copy\CopySpotAdvancedSetting;
use App\Model\Copy\Enums\CopyType;
use App\Model\Currency\Currency;
use App\Model\Trade\TradeSpotOrder;
use App\Service\RedisFactory\CacheRedis;
use App\Service\RedisFactory\MarketRedis;
use App\Service\UserAccounts\UserAccountsAssetService;
use App\Model\Enums\User\AccountType;
use App\Trait\Config\CurrencyConfigTrait;
use Hyperf\Di\Annotation\Inject;

class CopySpotCalculationService
{
    use CurrencyConfigTrait;
    #[Inject]
    private UserAccountsAssetService $assetService;

    private const DEFAULT_PRECISION = 8;

    #[Inject]
    protected CacheRedis $cacheRedis;

    #[Inject]
    protected MarketRedis $marketRedis;

    public function calculateCopyAmount(
        TradeSpotOrder $traderOrder,
        CopySpotUserSetting $userSetting,
        ?CopySpotAdvancedSetting $advancedSetting = null
    ): array {
        $result = [
            'can_copy' => false,
            'copy_amount' => '0',
            'copy_quantity' => '0',
            'reason' => '',
            'original_amount' => (string)$traderOrder->getAmount(),
            'original_price' => (string)$traderOrder->getPrice()
        ];

        if ($advancedSetting) {
            return $this->calculateWithAdvancedSetting($traderOrder, $advancedSetting, $result);
        }

        return $this->calculateWithUserSetting($traderOrder, $userSetting, $result);
    }

    private function calculateWithUserSetting(
        TradeSpotOrder $traderOrder,
        CopySpotUserSetting $userSetting,
        array $result
    ): array {
        $copyType = $userSetting->getCopyType();
        if ($copyType === CopyType::FIXED_AMOUNT->value && $userSetting->fixed_amount) { // 固定额度模式
            return $this->calculateFixedAmount(
                $traderOrder,
                (string)$userSetting->fixed_amount,
                (string)($userSetting->max_follow_amount ?? '0'),
                $userSetting->getFollowerUserId(),
                $result
            );
        }

        if ($copyType === CopyType::RATE->value && $userSetting->rate) { // 比例百分比模式
            return $this->calculateRateAmount(
                $traderOrder,
                (string)$userSetting->rate,
                (string)($userSetting->max_follow_amount ?? '0'),
                $userSetting->getFollowerUserId(),
                $result
            );
        }

        $result['reason'] = 'no_copy_config';
        return $result;
    }

    private function calculateWithAdvancedSetting(
        TradeSpotOrder $traderOrder,
        CopySpotAdvancedSetting $advancedSetting,
        array $result
    ): array {
        if ($advancedSetting->copy_type === CopyType::FIXED_AMOUNT && $advancedSetting->fixed_amount) {
            return $this->calculateFixedAmount(
                $traderOrder,
                (string)$advancedSetting->fixed_amount,
                (string)($advancedSetting->max_follow_amount ?? '0'),
                $advancedSetting->follower_user_id,
                $result
            );
        }

        if ($advancedSetting->copy_type === CopyType::RATE && $advancedSetting->rate) {
            return $this->calculateRateAmount(
                $traderOrder,
                (string)$advancedSetting->rate,
                (string)($advancedSetting->max_follow_amount ?? '0'),
                $advancedSetting->follower_user_id,
                $result
            );
        }

        $result['reason'] = 'no_copy_config';
        return $result;
    }

    private function calculateFixedAmount(
        TradeSpotOrder $traderOrder,
        string $fixedAmount,
        string $maxFollowAmount,
        int $followerUserId,
        array $result
    ): array {
        if (bccomp($fixedAmount, '0', self::DEFAULT_PRECISION) <= 0) {
            $result['reason'] = 'invalid_fixed_amount';
            return $result;
        }

        $copyAmount = $fixedAmount;

        if (bccomp($maxFollowAmount, '0', self::DEFAULT_PRECISION) > 0) {
            if (bccomp($copyAmount, $maxFollowAmount, self::DEFAULT_PRECISION) > 0) {
                $copyAmount = $maxFollowAmount;
            }
        }

        $price = (string)$traderOrder->getPrice();
        if ($traderOrder->getOrderType() === intval(OrderType::MARKET_INT->value)) { // 市价单
            $price = $this->getEstimatedPrice($traderOrder->getCurrencyId());
        }

        if (bccomp($price, '0', self::DEFAULT_PRECISION) <= 0) {
            $result['reason'] = 'invalid_price';
            return $result;
        }

        $copyQuantity = bcdiv($copyAmount, $price, self::DEFAULT_PRECISION);

        $balanceCheck = $this->checkUserBalance($followerUserId, $traderOrder, $copyAmount);
        if (!$balanceCheck['sufficient']) {
            $result['reason'] = 'insufficient_balance';
            $result['required_amount'] = $copyAmount;
            $result['available_balance'] = $balanceCheck['available'];
            return $result;
        }

        $result['can_copy'] = true;
        $result['copy_amount'] = $copyAmount;
        $result['copy_quantity'] = $copyQuantity;
        $result['copy_type'] = 'fixed_amount';

        return $result;
    }

    private function calculateRateAmount(
        TradeSpotOrder $traderOrder,
        string $rate,
        string $maxFollowAmount,
        int $followerUserId,
        array $result
    ): array {
        if (bccomp($rate, '0', self::DEFAULT_PRECISION) <= 0) {
            $result['reason'] = 'invalid_rate';
            return $result;
        }

        $traderAmount = (string)$traderOrder->getAmount();
        $price = (string)$traderOrder->getPrice();
        
        if ($traderOrder->getOrderType() === intval(OrderType::MARKET_INT->value)) { // 市价单
            $price = $this->getEstimatedPrice($traderOrder->getCurrencyId());
        }

        if (bccomp($price, '0', self::DEFAULT_PRECISION) <= 0) {
            $result['reason'] = 'invalid_price';
            return $result;
        }

        $traderValue = bcmul($traderAmount, $price, self::DEFAULT_PRECISION);
        $rateDecimal = strval($rate);
        $copyAmount = bcmul($traderValue, $rateDecimal, self::DEFAULT_PRECISION);

        if (bccomp($maxFollowAmount, '0', self::DEFAULT_PRECISION) > 0) {
            if (bccomp($copyAmount, $maxFollowAmount, self::DEFAULT_PRECISION) > 0) {
                $copyAmount = $maxFollowAmount;
            }
        }

        $copyQuantity = bcdiv($copyAmount, $price, self::DEFAULT_PRECISION);

        $balanceCheck = $this->checkUserBalance($followerUserId, $traderOrder, $copyAmount);
        if (!$balanceCheck['sufficient']) {
            $result['reason'] = 'insufficient_balance';
            $result['required_amount'] = $copyAmount;
            $result['available_balance'] = $balanceCheck['available'];
            return $result;
        }

        $result['can_copy'] = true;
        $result['copy_amount'] = $copyAmount;
        $result['copy_quantity'] = $copyQuantity;
        $result['copy_type'] = 'rate';
        $result['rate'] = $rate;

        return $result;
    }

    private function checkUserBalance(int $userId, TradeSpotOrder $traderOrder, string $requiredAmount): array
    {
        $requiredCurrencyId = $this->getRequiredCurrencyId($traderOrder);
        
        $userAsset = $this->assetService->getUserAsset($userId, AccountType::SPOT->value, $requiredCurrencyId);
        $availableBalance = $userAsset ? (string)$userAsset->available : '0';

        $sufficient = bccomp($availableBalance, $requiredAmount, self::DEFAULT_PRECISION) >= 0;

        return [
            'sufficient' => $sufficient,
            'available' => $availableBalance,
            'required' => $requiredAmount,
            'currency_id' => $requiredCurrencyId
        ];
    }

    private function getRequiredCurrencyId(TradeSpotOrder $traderOrder): int
    {
        if ($traderOrder->getDirection() === 1) { // 买入
            return $this->getQuoteCurrencyId($traderOrder->getCurrencyId());
        } else { // 卖出
            return $traderOrder->getCurrencyId();
        }
    }

    private function getQuoteCurrencyId(int $currencyId): int
    {
        // 这里需要根据实际的币种配置获取计价币种ID
        return (int)$this->getCurrencyConfigByKey($currencyId, Currency::FIELD_QUOTE_ASSETS_ID,$this->cacheRedis);
    }

    private function getEstimatedPrice(int $currencyId): string
    {
        // 这里需要从Redis获取最新价格
        return (string)$this->getCurrencyPrice($currencyId,MarketType::CRYPTO,$this->marketRedis);
    }

    public function validateCopySettings(CopySpotUserSetting $userSetting, ?CopySpotAdvancedSetting $advancedSetting = null): array
    {
        $errors = [];

        if ($advancedSetting) {
            if (!$advancedSetting->copy_type) {
                $errors[] = 'copy_type_required';
            } elseif ($advancedSetting->copy_type === CopyType::FIXED_AMOUNT) {
                if (!$advancedSetting->fixed_amount || bccomp((string)$advancedSetting->fixed_amount, '0', self::DEFAULT_PRECISION) <= 0) {
                    $errors[] = 'fixed_amount_required';
                }
            } elseif ($advancedSetting->copy_type === CopyType::RATE) {
                if (!$advancedSetting->rate || bccomp((string)$advancedSetting->rate, '0', self::DEFAULT_PRECISION) <= 0) {
                    $errors[] = 'rate_required';
                }
            }
        } else {
            $copyType = $userSetting->getCopyType();

            if ($copyType === 0) { // 固定额度模式
                if (!$userSetting->fixed_amount || bccomp((string)$userSetting->fixed_amount, '0', self::DEFAULT_PRECISION) <= 0) {
                    $errors[] = 'fixed_amount_required';
                }
            } elseif ($copyType === 1) { // 比例百分比模式
                if (!$userSetting->rate || bccomp((string)$userSetting->rate, '0', self::DEFAULT_PRECISION) <= 0) {
                    $errors[] = 'rate_required';
                }
            } else {
                $errors[] = 'invalid_copy_type';
            }
        }

        return [
            'valid' => empty($errors),
            'errors' => $errors
        ];
    }
}
