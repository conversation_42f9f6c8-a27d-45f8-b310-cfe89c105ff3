<?php

declare(strict_types=1);

namespace App\Http\Api\Service\V1\Copy;

use App\Model\Copy\CopySpotExpert;
use App\Model\Copy\Enums\ExpertStatus;
use App\Service\RedisFactory\CacheRedis;
use Hyperf\Di\Annotation\Inject;
use App\Enum\Redis\CacheKey\CopySpotCacheKey;

class TraderDetectionService
{
    #[Inject]
    private CacheRedis $redis;

    private const CACHE_TTL = 3600;

    public function isActiveTrader(int $userId): bool
    {
        $cacheKey = CopySpotCacheKey::getTraderStatusKey($userId);

        $cached = $this->redis->exists($cacheKey);
        if ($cached) {
            return (bool) $cached;
        }

        $expert = CopySpotExpert::where(CopySpotExpert::FIELD_USER_ID, $userId)
            ->where(CopySpotExpert::FIELD_STATUS, ExpertStatus::APPROVED)
            ->where(CopySpotExpert::FIELD_IS_ACTIVE, true)
            ->first();

        $isTrader = $expert !== null;
        
        $this->redis->setex($cacheKey, self::CACHE_TTL, (int) $isTrader);
        
        return $isTrader;
    }

    public function getTraderExpert(int $userId): ?CopySpotExpert
    {
        return CopySpotExpert::where(CopySpotExpert::FIELD_USER_ID, $userId)
            ->where(CopySpotExpert::FIELD_STATUS, ExpertStatus::APPROVED)
            ->where(CopySpotExpert::FIELD_IS_ACTIVE, true)
            ->first();
    }

    public function isTraderForCurrency(int $userId, int $currencyId): bool
    {
        $expert = $this->getTraderExpert($userId);
        
        if (!$expert) {
            return false;
        }

        $currencyIds = $expert->currency_ids;
        
        if (empty($currencyIds)) {
            return $expert->new_currency_auto_copy;
        }

        return in_array($currencyId, $currencyIds, true);
    }

    public function clearTraderCache(int $userId): void
    {
        $cacheKey = CopySpotCacheKey::getTraderStatusKey($userId);
        $this->redis->del($cacheKey);
    }

    public function batchCheckTraders(array $userIds): array
    {
        if (empty($userIds)) {
            return [];
        }

        $cacheKeys = [];
        foreach ($userIds as $userId) {
            $cacheKeys[$userId] = CopySpotCacheKey::getTraderStatusKey($userId);
        }

        $cachedResults = $this->redis->mget(array_values($cacheKeys));
        $results = [];
        $uncachedUserIds = [];

        foreach ($userIds as $index => $userId) {
            if ($cachedResults[$index] !== null) {
                $results[$userId] = (bool) $cachedResults[$index];
            } else {
                $uncachedUserIds[] = $userId;
            }
        }

        if (!empty($uncachedUserIds)) {
            $experts = CopySpotExpert::whereIn(CopySpotExpert::FIELD_USER_ID, $uncachedUserIds)
                ->where(CopySpotExpert::FIELD_STATUS, ExpertStatus::APPROVED)
                ->where(CopySpotExpert::FIELD_IS_ACTIVE, true)
                ->pluck(CopySpotExpert::FIELD_USER_ID)
                ->toArray();

            $cacheData = [];
            foreach ($uncachedUserIds as $userId) {
                $isTrader = in_array($userId, $experts, true);
                $results[$userId] = $isTrader;
                $cacheData[CopySpotCacheKey::getTraderStatusKey($userId)] = (int) $isTrader;
            }

            if (!empty($cacheData)) {
                $this->redis->pipeline(function ($pipe) use ($cacheData) {
                    foreach ($cacheData as $key => $value) {
                        $pipe->setex($key, self::CACHE_TTL, $value);
                    }
                });
            }
        }

        return $results;
    }
}
