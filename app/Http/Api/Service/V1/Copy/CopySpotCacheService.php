<?php

declare(strict_types=1);

namespace App\Http\Api\Service\V1\Copy;

use App\Model\Copy\CopySpotUserSetting;
use App\Model\Copy\CopySpotAdvancedSetting;
use App\Model\Copy\CopySpotExpert;
use App\Model\Copy\Enums\CopyStatus;
use App\Model\Copy\Enums\ExpertStatus;
use Hyperf\Di\Annotation\Inject;
use Hyperf\Redis\Redis;
use Psr\Log\LoggerInterface;
use App\Enum\Redis\CacheKey\CopySpotCacheKey;

class CopySpotCacheService
{
    #[Inject]
    private Redis $redis;

    #[Inject]
    private LoggerInterface $logger;

    private const CACHE_TTL = 3600; // 1小时
    private const BATCH_SIZE = 100;

    public function warmupTraderFollowersCache(int $traderId): void
    {
        try {
            $followers = $this->loadFollowersFromDatabase($traderId);
            $cacheKey = CopySpotCacheKey::getFollowersKey($traderId);
            
            $this->redis->setex($cacheKey, self::CACHE_TTL, json_encode($followers));
            
            $this->logger->info('预热交易员跟单用户缓存', [
                'trader_id' => $traderId,
                'followers_count' => count($followers)
            ]);

        } catch (\Exception $e) {
            $this->logger->error('预热跟单用户缓存失败', [
                'trader_id' => $traderId,
                'error' => $e->getMessage()
            ]);
        }
    }

    public function batchWarmupTradersCache(array $traderIds): array
    {
        $results = [
            'success' => 0,
            'failed' => 0,
            'total' => count($traderIds)
        ];

        $batches = array_chunk($traderIds, self::BATCH_SIZE);

        foreach ($batches as $batch) {
            $pipeline = $this->redis->pipeline();

            foreach ($batch as $traderId) {
                try {
                    $followers = $this->loadFollowersFromDatabase($traderId);
                    $cacheKey = CopySpotCacheKey::getFollowersKey($traderId);
                    
                    $pipeline->setex($cacheKey, self::CACHE_TTL, json_encode($followers));
                    $results['success']++;

                } catch (\Exception $e) {
                    $this->logger->error('批量预热缓存失败', [
                        'trader_id' => $traderId,
                        'error' => $e->getMessage()
                    ]);
                    $results['failed']++;
                }
            }

            $pipeline->exec();
        }

        $this->logger->info('批量预热交易员缓存完成', $results);
        return $results;
    }

    public function cacheUserSettings(int $followerUserId, int $expertId, array $settings): void
    {
        $cacheKey = CopySpotCacheKey::getUserSettingKey($followerUserId, $expertId);
        $this->redis->setex($cacheKey, self::CACHE_TTL, json_encode($settings));
    }

    public function getCachedUserSettings(int $followerUserId, int $expertId): ?array
    {
        $cacheKey = CopySpotCacheKey::getUserSettingKey($followerUserId, $expertId);
        $cached = $this->redis->get($cacheKey);
        
        return $cached ? json_decode($cached, true) : null;
    }

    public function cacheAdvancedSettings(int $followerUserId, int $expertId, int $currencyId, array $settings): void
    {
        $cacheKey = CopySpotCacheKey::getAdvancedSettingKey($followerUserId, $expertId, $currencyId);
        $this->redis->setex($cacheKey, self::CACHE_TTL, json_encode($settings));
    }

    public function getCachedAdvancedSettings(int $followerUserId, int $expertId, int $currencyId): ?array
    {
        $cacheKey = CopySpotCacheKey::getAdvancedSettingKey($followerUserId, $expertId, $currencyId);
        $cached = $this->redis->get($cacheKey);
        
        return $cached ? json_decode($cached, true) : null;
    }

    public function cacheTraderConfig(int $traderId, array $config): void
    {
        $cacheKey = CopySpotCacheKey::getTraderConfigKey($traderId);
        $this->redis->setex($cacheKey, self::CACHE_TTL, json_encode($config));
    }

    public function getCachedTraderConfig(int $traderId): ?array
    {
        $cacheKey = CopySpotCacheKey::getTraderConfigKey($traderId);
        $cached = $this->redis->get($cacheKey);
        
        return $cached ? json_decode($cached, true) : null;
    }

    public function invalidateTraderCache(int $traderId): void
    {
        $keys = [
            CopySpotCacheKey::getFollowersKey($traderId),
            CopySpotCacheKey::getTraderConfigKey($traderId),
            CopySpotCacheKey::getTraderStatusKey($traderId)
        ];

        foreach ($keys as $key) {
            $this->redis->del($key);
        }

        $this->logger->info('清除交易员缓存', ['trader_id' => $traderId]);
    }

    public function invalidateUserCache(int $userId, int $expertId): void
    {
        $pattern = CopySpotCacheKey::getUserSettingKey($userId, $expertId);
        $this->redis->del($pattern);

        // 清除高级设置缓存（需要遍历所有币种）
        $advancedPattern = "copy_spot:advanced_setting:{$userId}:{$expertId}:*";
        $keys = $this->redis->keys($advancedPattern);
        
        if (!empty($keys)) {
            $this->redis->del(...$keys);
        }

        $this->logger->info('清除用户跟单缓存', [
            'user_id' => $userId,
            'expert_id' => $expertId
        ]);
    }

    public function preloadCopyOrdersCache(int $originalOrderId): void
    {
        try {
            $copyOrders = $this->loadCopyOrdersFromDatabase($originalOrderId);
            $cacheKey = CopySpotCacheKey::getCopyOrdersKey($originalOrderId);
            
            $this->redis->setex($cacheKey, self::CACHE_TTL, json_encode($copyOrders));

        } catch (\Exception $e) {
            $this->logger->error('预加载跟单订单缓存失败', [
                'original_order_id' => $originalOrderId,
                'error' => $e->getMessage()
            ]);
        }
    }

    public function getCachedCopyOrders(int $originalOrderId): ?array
    {
        $cacheKey = CopySpotCacheKey::getCopyOrdersKey($originalOrderId);
        $cached = $this->redis->get($cacheKey);
        
        return $cached ? json_decode($cached, true) : null;
    }

    public function batchPreloadUserAssets(array $userIds, array $currencyIds): array
    {
        $results = [];
        
        try {
            // 这里可以批量查询用户资产并缓存
            // 实际实现需要调用UserAccountsAssetService的批量查询方法
            
            $this->logger->info('批量预加载用户资产', [
                'user_count' => count($userIds),
                'currency_count' => count($currencyIds)
            ]);

        } catch (\Exception $e) {
            $this->logger->error('批量预加载用户资产失败', [
                'error' => $e->getMessage()
            ]);
        }

        return $results;
    }

    public function optimizeFollowerQuery(int $traderId): array
    {
        $cacheKey = CopySpotCacheKey::getFollowersKey($traderId);
        
        // 尝试从缓存获取
        $cached = $this->redis->get($cacheKey);
        if ($cached !== null) {
            return json_decode($cached, true) ?: [];
        }

        // 缓存未命中，从数据库加载并缓存
        $followers = $this->loadFollowersFromDatabase($traderId);
        $this->redis->setex($cacheKey, self::CACHE_TTL, json_encode($followers));
        
        return $followers;
    }

    private function loadFollowersFromDatabase(int $traderId): array
    {
        return CopySpotUserSetting::where(CopySpotUserSetting::FIELD_EXPERT_USER_ID, $traderId)
            ->where(CopySpotUserSetting::FIELD_STATUS, CopyStatus::FOLLOWING)
            ->select([
                CopySpotUserSetting::FIELD_FOLLOWER_USER_ID . ' as user_id',
                CopySpotUserSetting::FIELD_EXPERT_ID . ' as expert_id',
                CopySpotUserSetting::FIELD_COPY_TYPE . ' as copy_type',
                CopySpotUserSetting::FIELD_FIXED_AMOUNT . ' as fixed_amount',
                CopySpotUserSetting::FIELD_RATE . ' as rate',
                CopySpotUserSetting::FIELD_MAX_FOLLOW_AMOUNT . ' as max_follow_amount',
                CopySpotUserSetting::FIELD_AUTO_NEW_PAIRS . ' as auto_new_pairs',
                CopySpotUserSetting::FIELD_COPY_CURRENCIES . ' as copy_currencies'
            ])
            ->get()
            ->toArray();
    }

    private function loadCopyOrdersFromDatabase(int $originalOrderId): array
    {
        return \App\Model\Copy\CopySpotOrder::where('expert_order_id', $originalOrderId)
            ->select([
                'id',
                'follower_user_id',
                'expert_id',
                'expert_user_id',
                'follower_order_id',
                'copy_settings_snapshot'
            ])
            ->get()
            ->toArray();
    }

    public function getCacheStats(): array
    {
        try {
            $info = $this->redis->info('memory');
            $keyspace = $this->redis->info('keyspace');

            return [
                'memory_used' => $info['used_memory_human'] ?? 'unknown',
                'memory_peak' => $info['used_memory_peak_human'] ?? 'unknown',
                'keyspace_hits' => $info['keyspace_hits'] ?? 0,
                'keyspace_misses' => $info['keyspace_misses'] ?? 0,
                'hit_rate' => $this->calculateHitRate($info),
                'copy_spot_keys' => $this->countCopySpotKeys()
            ];

        } catch (\Exception $e) {
            $this->logger->error('获取缓存统计失败', ['error' => $e->getMessage()]);
            return [];
        }
    }

    private function calculateHitRate(array $info): string
    {
        $hits = $info['keyspace_hits'] ?? 0;
        $misses = $info['keyspace_misses'] ?? 0;
        $total = $hits + $misses;

        if ($total === 0) {
            return '0%';
        }

        $rate = ($hits / $total) * 100;
        return number_format($rate, 2) . '%';
    }

    private function countCopySpotKeys(): int
    {
        try {
            $keys = $this->redis->keys('copy_spot:*');
            return count($keys);
        } catch (\Exception $e) {
            return 0;
        }
    }

    public function cleanupExpiredCache(): int
    {
        try {
            $pattern = 'copy_spot:*';
            $keys = $this->redis->keys($pattern);
            $cleaned = 0;

            foreach ($keys as $key) {
                $ttl = $this->redis->ttl($key);
                if ($ttl === -1) { // 没有过期时间的key
                    $this->redis->expire($key, self::CACHE_TTL);
                    $cleaned++;
                }
            }

            $this->logger->info('清理过期缓存', ['cleaned_keys' => $cleaned]);
            return $cleaned;

        } catch (\Exception $e) {
            $this->logger->error('清理过期缓存失败', ['error' => $e->getMessage()]);
            return 0;
        }
    }
}
