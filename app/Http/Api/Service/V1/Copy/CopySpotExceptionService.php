<?php

declare(strict_types=1);

namespace App\Http\Api\Service\V1\Copy;

use App\Model\Copy\CopySpotOrder;
use App\Service\UserAccounts\UserAccountsAssetService;
use App\Model\Enums\User\AccountType;
use App\Model\Enums\User\FlowsType;
use Hyperf\Di\Annotation\Inject;
use Hyperf\DbConnection\Db;
use Hyperf\Redis\Redis;
use Psr\Log\LoggerInterface;

class CopySpotExceptionService
{
    #[Inject]
    private UserAccountsAssetService $assetService;

    #[Inject]
    private Redis $redis;

    #[Inject]
    private LoggerInterface $logger;

    private const RETRY_QUEUE_KEY = 'copy_spot:retry_queue';
    private const MAX_RETRY_COUNT = 3;
    private const RETRY_DELAY_SECONDS = [60, 300, 900]; // 1分钟、5分钟、15分钟

    public function handleCopyOrderException(
        array $orderData,
        \Exception $exception,
        int $retryCount = 0
    ): array {
        $this->logger->error('跟单订单异常', [
            'follower_id' => $orderData['follower_user_id'],
            'expert_id' => $orderData['expert_id'],
            'currency_id' => $orderData['currency_id'],
            'copy_amount' => $orderData['copy_amount'],
            'retry_count' => $retryCount,
            'error' => $exception->getMessage()
        ]);

        $exceptionType = $this->classifyException($exception);

        switch ($exceptionType) {
            case 'insufficient_balance':
                return $this->handleInsufficientBalance($orderData, $retryCount);
            
            case 'network_error':
                return $this->handleNetworkError($orderData, $retryCount);
            
            case 'system_error':
                return $this->handleSystemError($orderData, $retryCount);
            
            case 'validation_error':
                return $this->handleValidationError($orderData);
            
            default:
                return $this->handleUnknownError($orderData, $exception, $retryCount);
        }
    }

    private function classifyException(\Exception $exception): string
    {
        $message = $exception->getMessage();

        if (strpos($message, 'insufficient') !== false || strpos($message, '余额不足') !== false) {
            return 'insufficient_balance';
        }

        if (strpos($message, 'network') !== false || strpos($message, 'timeout') !== false) {
            return 'network_error';
        }

        if (strpos($message, 'validation') !== false || strpos($message, '验证') !== false) {
            return 'validation_error';
        }

        if (strpos($message, 'system') !== false || strpos($message, 'internal') !== false) {
            return 'system_error';
        }

        return 'unknown_error';
    }

    private function handleInsufficientBalance(array $orderData, int $retryCount): array
    {
        $followerUserId = $orderData['follower_user_id'];
        $currencyId = $this->getRequiredCurrencyId($orderData);

        $userAsset = $this->assetService->getUserAsset($followerUserId, AccountType::SPOT->value, $currencyId);
        $availableBalance = $userAsset ? (string)$userAsset->available : '0';

        if (bccomp($availableBalance, '0', 8) <= 0) {
            $this->logger->warning('用户余额为零，跳过跟单', [
                'follower_id' => $followerUserId,
                'currency_id' => $currencyId
            ]);

            return [
                'success' => false,
                'action' => 'skip',
                'reason' => 'zero_balance'
            ];
        }

        $reducedAmount = bcmul($availableBalance, '0.95', 8); // 使用95%的可用余额

        if (bccomp($reducedAmount, $orderData['copy_amount'], 8) < 0) {
            $orderData['copy_amount'] = $reducedAmount;
            $orderData['quantity'] = bcdiv($reducedAmount, $orderData['price'], 8);

            $this->logger->info('降级跟单金额', [
                'follower_id' => $followerUserId,
                'original_amount' => $orderData['copy_amount'],
                'reduced_amount' => $reducedAmount
            ]);

            return [
                'success' => true,
                'action' => 'retry_with_reduced_amount',
                'order_data' => $orderData
            ];
        }

        return [
            'success' => false,
            'action' => 'skip',
            'reason' => 'insufficient_balance_after_reduction'
        ];
    }

    private function handleNetworkError(array $orderData, int $retryCount): array
    {
        if ($retryCount >= self::MAX_RETRY_COUNT) {
            $this->logger->error('网络异常重试次数超限', [
                'follower_id' => $orderData['follower_user_id'],
                'retry_count' => $retryCount
            ]);

            return [
                'success' => false,
                'action' => 'abandon',
                'reason' => 'max_retry_exceeded'
            ];
        }

        $delaySeconds = self::RETRY_DELAY_SECONDS[$retryCount] ?? 900;
        $retryData = [
            'order_data' => $orderData,
            'retry_count' => $retryCount + 1,
            'retry_time' => time() + $delaySeconds,
            'exception_type' => 'network_error'
        ];

        $this->addToRetryQueue($retryData);

        $this->logger->info('网络异常，加入重试队列', [
            'follower_id' => $orderData['follower_user_id'],
            'retry_count' => $retryCount + 1,
            'delay_seconds' => $delaySeconds
        ]);

        return [
            'success' => true,
            'action' => 'retry_later',
            'retry_time' => $retryData['retry_time']
        ];
    }

    private function handleSystemError(array $orderData, int $retryCount): array
    {
        if ($retryCount >= 1) { // 系统错误只重试一次
            return [
                'success' => false,
                'action' => 'abandon',
                'reason' => 'system_error_retry_exceeded'
            ];
        }

        $retryData = [
            'order_data' => $orderData,
            'retry_count' => $retryCount + 1,
            'retry_time' => time() + 300, // 5分钟后重试
            'exception_type' => 'system_error'
        ];

        $this->addToRetryQueue($retryData);

        return [
            'success' => true,
            'action' => 'retry_later',
            'retry_time' => $retryData['retry_time']
        ];
    }

    private function handleValidationError(array $orderData): array
    {
        $this->logger->warning('跟单参数验证失败，跳过处理', [
            'follower_id' => $orderData['follower_user_id'],
            'order_data' => $orderData
        ]);

        return [
            'success' => false,
            'action' => 'skip',
            'reason' => 'validation_error'
        ];
    }

    private function handleUnknownError(array $orderData, \Exception $exception, int $retryCount): array
    {
        if ($retryCount >= 1) {
            return [
                'success' => false,
                'action' => 'abandon',
                'reason' => 'unknown_error_retry_exceeded'
            ];
        }

        $retryData = [
            'order_data' => $orderData,
            'retry_count' => $retryCount + 1,
            'retry_time' => time() + 600, // 10分钟后重试
            'exception_type' => 'unknown_error',
            'exception_message' => $exception->getMessage()
        ];

        $this->addToRetryQueue($retryData);

        return [
            'success' => true,
            'action' => 'retry_later',
            'retry_time' => $retryData['retry_time']
        ];
    }

    private function addToRetryQueue(array $retryData): void
    {
        $score = $retryData['retry_time'];
        $member = json_encode($retryData);
        
        $this->redis->zadd(self::RETRY_QUEUE_KEY, $score, $member);
    }

    public function processRetryQueue(): array
    {
        $currentTime = time();
        $retryItems = $this->redis->zrangebyscore(self::RETRY_QUEUE_KEY, (string)0, (string)$currentTime, ['limit' => [0, 50]]);

        $processed = 0;
        $success = 0;
        $failed = 0;

        foreach ($retryItems as $item) {
            try {
                $retryData = json_decode($item, true);
                
                // 处理重试逻辑
                $result = $this->processRetryItem($retryData);
                
                if ($result['success']) {
                    $success++;
                } else {
                    $failed++;
                }

                // 从队列中移除已处理的项目
                $this->redis->zrem(self::RETRY_QUEUE_KEY, $item);
                $processed++;

            } catch (\Exception $e) {
                $this->logger->error('处理重试队列项目失败', [
                    'item' => $item,
                    'error' => $e->getMessage()
                ]);
                $failed++;
            }
        }

        return [
            'processed' => $processed,
            'success' => $success,
            'failed' => $failed
        ];
    }

    private function processRetryItem(array $retryData): array
    {
        // 这里应该调用实际的跟单创建逻辑
        // 为了简化，这里只是记录日志
        $this->logger->info('处理重试项目', [
            'retry_data' => $retryData
        ]);

        return ['success' => true];
    }

    private function getRequiredCurrencyId(array $orderData): int
    {
        if ($orderData['direction'] === 1) { // 买入
            return 1; // USDT，实际应该从配置获取
        } else { // 卖出
            return $orderData['currency_id'];
        }
    }

    public function rollbackFailedCopyOrder(int $followerUserId, array $orderData): bool
    {
        return Db::transaction(function () use ($followerUserId, $orderData) {
            try {
                // 如果有冻结资金，需要解冻
                if (isset($orderData['frozen_amount']) && bccomp($orderData['frozen_amount'], '0', 8) > 0) {
                    $currencyId = $this->getRequiredCurrencyId($orderData);
                    
                    $this->assetService->unfreezeAsset(
                        $followerUserId,
                        AccountType::SPOT->value,
                        $currencyId,
                        $orderData['frozen_amount'],
                        FlowsType::SPOT_TRADE->value
                    );

                    $this->logger->info('回滚跟单订单，解冻资金', [
                        'follower_id' => $followerUserId,
                        'currency_id' => $currencyId,
                        'amount' => $orderData['frozen_amount']
                    ]);
                }

                return true;

            } catch (\Exception $e) {
                $this->logger->error('回滚跟单订单失败', [
                    'follower_id' => $followerUserId,
                    'order_data' => $orderData,
                    'error' => $e->getMessage()
                ]);

                throw $e;
            }
        });
    }
}
