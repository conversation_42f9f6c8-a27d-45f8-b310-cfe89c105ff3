<?php

declare(strict_types=1);

namespace App\Http\Api\Service\V1\Copy;

use App\Enum\AsyncExecutorKey;
use App\Enum\CurrencyConfigKey;
use App\Job\Copy\CopySpotProfitSharingJob;
use App\Model\Copy\CopySpotPosition;
use App\Model\Copy\CopySpotOrder;
use App\Model\Copy\Enums\CopySpotPositionStatus;
use App\Model\Currency\Currency;
use App\Service\RedisFactory\CacheRedis;
use Hyperf\Di\Annotation\Inject;
use Hyperf\DbConnection\Db;
use Psr\Log\LoggerInterface;

class CopySpotPositionService
{
    private LoggerInterface $logger;

    private const DEFAULT_PRECISION = 8;

    #[Inject]
    protected CacheRedis $cacheRedis;

    public function __construct()
    {
        $this->logger = logger('现货持仓维护','copyTrade/spot_copy_trade.log');
    }

    public function getOrCreatePosition(int $followerUserId, int $expertId, int $expertUserId, int $currencyId): CopySpotPosition
    {
        $position = CopySpotPosition::where(CopySpotPosition::FIELD_FOLLOWER_USER_ID, $followerUserId)
            ->where(CopySpotPosition::FIELD_EXPERT_ID, $expertId)
            ->where(CopySpotPosition::FIELD_CURRENCY_ID, $currencyId)
            ->first();

        if (!$position) {
            $position = new CopySpotPosition();
            $position->follower_user_id = $followerUserId;
            $position->expert_id = $expertId;
            $position->expert_user_id = $expertUserId;
            $position->currency_id = $currencyId;
            $position->total_buy_quantity = 0;
            $position->total_sell_quantity = 0;
            $position->available_quantity = 0;
            $position->frozen_quantity = 0;
            $position->avg_buy_price = 0;
            $position->total_buy_amount = 0;
            $position->avg_sell_price = 0;
            $position->total_sell_amount = 0;
            $position->realized_pnl = 0;
            $position->status = CopySpotPositionStatus::HOLDING->value;
            $position->save();

            $this->logger->info('创建新的跟单持仓记录', [
                'follower_user_id' => $followerUserId,
                'expert_id' => $expertId,
                'currency_id' => $currencyId
            ]);
        }

        return $position;
    }

    public function updatePositionForBuy(int $followerUserId, int $expertId, int $expertUserId, int $currencyId, string $price, string $quantity): void
    {
        Db::transaction(function () use ($followerUserId, $expertId, $expertUserId, $currencyId, $price, $quantity) {
            // 查询是否有未平仓的持仓记录
            $position = CopySpotPosition::where(CopySpotPosition::FIELD_FOLLOWER_USER_ID, $followerUserId)
                ->where(CopySpotPosition::FIELD_EXPERT_ID, $expertId)
                ->where(CopySpotPosition::FIELD_CURRENCY_ID, $currencyId)
                ->where(CopySpotPosition::FIELD_STATUS, CopySpotPositionStatus::HOLDING->value)
                ->where(CopySpotPosition::FIELD_AVAILABLE_QUANTITY, '>', 0)
                ->first();

            if ($position) {
                // 有未平仓记录，累计到现有记录
                $this->accumulateBuyPosition($position, $price, $quantity);
                $this->logger->info('累计买入到现有持仓', [
                    'position_id' => $position->id,
                    'follower_user_id' => $followerUserId,
                    'expert_id' => $expertId,
                    'currency_id' => $currencyId,
                    'add_quantity' => $quantity,
                    'add_price' => $price
                ]);
            } else {
                // 没有未平仓记录，创建新记录
                $position = $this->createNewBuyPosition($followerUserId, $expertId, $expertUserId, $currencyId, $price, $quantity);
                $this->logger->info('创建新的买入持仓', [
                    'position_id' => $position->id,
                    'follower_user_id' => $followerUserId,
                    'expert_id' => $expertId,
                    'currency_id' => $currencyId,
                    'quantity' => $quantity,
                    'price' => $price
                ]);
            }
        });
    }

    private function accumulateBuyPosition(CopySpotPosition $position, string $price, string $quantity): void
    {
        // 计算新买入金额
        $newBuyAmount = bcmul($price, $quantity, self::DEFAULT_PRECISION);

        // 更新总买入数量和金额
        $newTotalBuyQuantity = bcadd((string)$position->total_buy_quantity, $quantity, self::DEFAULT_PRECISION);
        $newTotalBuyAmount = bcadd((string)$position->total_buy_amount, $newBuyAmount, self::DEFAULT_PRECISION);

        // 重新计算平均买入价格
        $newAvgBuyPrice = bcdiv($newTotalBuyAmount, $newTotalBuyQuantity, self::DEFAULT_PRECISION);

        // 更新持仓记录
        $position->total_buy_quantity = (float)$newTotalBuyQuantity;
        $position->total_buy_amount = (float)$newTotalBuyAmount;
        $position->avg_buy_price = (float)$newAvgBuyPrice;
        $position->available_quantity = (float)bcadd((string)$position->available_quantity, $quantity, self::DEFAULT_PRECISION);

        $position->save();
    }

    private function createNewBuyPosition(int $followerUserId, int $expertId, int $expertUserId, int $currencyId, string $price, string $quantity): CopySpotPosition
    {
        $buyAmount = bcmul($price, $quantity, self::DEFAULT_PRECISION);

        $position = new CopySpotPosition();
        $position->follower_user_id = $followerUserId;
        $position->expert_id = $expertId;
        $position->expert_user_id = $expertUserId;
        $position->currency_id = $currencyId;
        $position->total_buy_quantity = (float)$quantity;
        $position->total_sell_quantity = 0;
        $position->available_quantity = (float)$quantity;
        $position->frozen_quantity = 0;
        $position->avg_buy_price = (float)$price;
        $position->total_buy_amount = (float)$buyAmount;
        $position->avg_sell_price = 0;
        $position->total_sell_amount = 0;
        $position->realized_pnl = 0;
        $position->status = CopySpotPositionStatus::HOLDING->value;

        $position->save();
        return $position;
    }

    public function updatePositionForSell(int $followerUserId, int $expertId, int $currencyId, string $price, string $quantity): void
    {
        Db::transaction(function () use ($followerUserId, $expertId, $currencyId, $price, $quantity) {
            // 查询未平仓的持仓记录
            $position = CopySpotPosition::where(CopySpotPosition::FIELD_FOLLOWER_USER_ID, $followerUserId)
                ->where(CopySpotPosition::FIELD_EXPERT_ID, $expertId)
                ->where(CopySpotPosition::FIELD_CURRENCY_ID, $currencyId)
                ->where(CopySpotPosition::FIELD_STATUS, CopySpotPositionStatus::HOLDING->value)
                ->where(CopySpotPosition::FIELD_AVAILABLE_QUANTITY, '>', 0)
                ->first();

            if (!$position) {
                $this->logger->warning('卖出时未找到未平仓记录', [
                    'follower_user_id' => $followerUserId,
                    'expert_id' => $expertId,
                    'currency_id' => $currencyId
                ]);
                return;
            }

            $availableQuantity = (string)$position->available_quantity;

            // 检查卖出数量是否超过可用持仓
            if (bccomp($quantity, $availableQuantity, self::DEFAULT_PRECISION) > 0) {
                $this->logger->error('卖出数量超过可用持仓', [
                    'follower_user_id' => $followerUserId,
                    'expert_id' => $expertId,
                    'currency_id' => $currencyId,
                    'available_quantity' => $availableQuantity,
                    'sell_quantity' => $quantity
                ]);
                $quantity = $availableQuantity; // 最多只能卖出可用数量
            }

            // 更新卖出相关数据
            $this->updateSellData($position, $price, $quantity);

            // 减少可用持仓数量
            $newAvailableQuantity = bcsub($availableQuantity, $quantity, self::DEFAULT_PRECISION);
            $position->available_quantity = (float)$newAvailableQuantity;

            // 判断是否完全平仓
            if (bccomp($newAvailableQuantity, '0', self::DEFAULT_PRECISION) <= 0) {
                $position->status = CopySpotPositionStatus::CLOSED->value;

                $this->logger->info('持仓完全平仓', [
                    'position_id' => $position->id,
                    'follower_user_id' => $followerUserId,
                    'expert_id' => $expertId,
                    'currency_id' => $currencyId,
                    'realized_pnl' => $position->realized_pnl
                ]);

                // 如果有盈利，投递分润任务
                if (bccomp((string)$position->realized_pnl, '0', self::DEFAULT_PRECISION) > 0) {

                    $this->dispatchProfitSharingJob($position);
                }
            }

            $position->save();

            $this->logger->info('更新卖出持仓', [
                'position_id' => $position->id,
                'follower_user_id' => $followerUserId,
                'expert_id' => $expertId,
                'currency_id' => $currencyId,
                'sell_price' => $price,
                'sell_quantity' => $quantity,
                'remaining_quantity' => $newAvailableQuantity,
                'status' => $position->status
            ]);
        });
    }

    private function updateSellData(CopySpotPosition $position, string $price, string $quantity): void
    {
        // 计算新卖出金额
        $newSellAmount = bcmul($price, $quantity, self::DEFAULT_PRECISION);

        // 更新总卖出数量和金额
        $newTotalSellQuantity = bcadd((string)$position->total_sell_quantity, $quantity, self::DEFAULT_PRECISION);
        $newTotalSellAmount = bcadd((string)$position->total_sell_amount, $newSellAmount, self::DEFAULT_PRECISION);

        // 重新计算平均卖出价格
        $newAvgSellPrice = bcdiv($newTotalSellAmount, $newTotalSellQuantity, self::DEFAULT_PRECISION);

        // 计算已实现盈亏（这次卖出的盈亏）
        $costPrice = (string)$position->avg_buy_price;
        $realizedPnlForThisTrade = bcmul(
            bcsub($price, $costPrice, self::DEFAULT_PRECISION),
            $quantity,
            self::DEFAULT_PRECISION
        );
        $newRealizedPnl = bcadd((string)$position->realized_pnl, $realizedPnlForThisTrade, self::DEFAULT_PRECISION);

        // 更新持仓记录
        $position->total_sell_quantity = (float)$newTotalSellQuantity;
        $position->total_sell_amount = (float)$newTotalSellAmount;
        $position->avg_sell_price = (float)$newAvgSellPrice;
        $position->realized_pnl = (float)$newRealizedPnl;
    }

    public function getPositionsByExpertAndCurrency(int $expertId, int $currencyId): array
    {
        return CopySpotPosition::where(CopySpotPosition::FIELD_EXPERT_ID, $expertId)
            ->where(CopySpotPosition::FIELD_CURRENCY_ID, $currencyId)
            ->where(CopySpotPosition::FIELD_STATUS, CopySpotPositionStatus::HOLDING->value)
            ->where(CopySpotPosition::FIELD_AVAILABLE_QUANTITY, '>', 0)
            ->whereColumn(CopySpotPosition::FIELD_FOLLOWER_USER_ID, '!=', CopySpotPosition::FIELD_EXPERT_USER_ID) // 排除交易员自己的持仓
            ->get()
            ->toArray();
    }

    public function getAllPositionsByExpertAndCurrency(int $expertId, int $currencyId): array
    {
        return CopySpotPosition::where(CopySpotPosition::FIELD_EXPERT_ID, $expertId)
            ->where(CopySpotPosition::FIELD_CURRENCY_ID, $currencyId)
            ->select([
                CopySpotPosition::FIELD_FOLLOWER_USER_ID,
                CopySpotPosition::FIELD_EXPERT_ID,
                CopySpotPosition::FIELD_EXPERT_USER_ID,
                CopySpotPosition::FIELD_AVAILABLE_QUANTITY,
                CopySpotPosition::FIELD_STATUS
            ])
            ->get()
            ->toArray();
    }

    public function getPosition(int $followerUserId, int $expertId, int $currencyId): ?CopySpotPosition
    {
        return CopySpotPosition::where(CopySpotPosition::FIELD_FOLLOWER_USER_ID, $followerUserId)
            ->where(CopySpotPosition::FIELD_EXPERT_ID, $expertId)
            ->where(CopySpotPosition::FIELD_CURRENCY_ID, $currencyId)
            ->first();
    }

    public function getAvailableQuantity(int $followerUserId, int $expertId, int $currencyId): string
    {
        $position = $this->getPosition($followerUserId, $expertId, $currencyId);
        return $position ? (string)$position->available_quantity : '0';
    }

    public function freezeQuantity(int $followerUserId, int $expertId, int $currencyId, string $quantity): bool
    {
        return Db::transaction(function () use ($followerUserId, $expertId, $currencyId, $quantity) {
            $position = $this->getPosition($followerUserId, $expertId, $currencyId);
            if (!$position) {
                return false;
            }

            $availableQuantity = (string)$position->available_quantity;
            if (bccomp($availableQuantity, $quantity, self::DEFAULT_PRECISION) < 0) {
                return false;
            }

            $newAvailableQuantity = bcsub($availableQuantity, $quantity, self::DEFAULT_PRECISION);
            $newFrozenQuantity = bcadd((string)$position->frozen_quantity, $quantity, self::DEFAULT_PRECISION);

            $position->available_quantity = (float)$newAvailableQuantity;
            $position->frozen_quantity = (float)$newFrozenQuantity;
            $position->save();

            return true;
        });
    }

    public function unfreezeQuantity(int $followerUserId, int $expertId, int $currencyId, string $quantity): bool
    {
        return Db::transaction(function () use ($followerUserId, $expertId, $currencyId, $quantity) {
            $position = $this->getPosition($followerUserId, $expertId, $currencyId);
            if (!$position) {
                return false;
            }

            $frozenQuantity = (string)$position->frozen_quantity;
            if (bccomp($frozenQuantity, $quantity, self::DEFAULT_PRECISION) < 0) {
                return false;
            }

            $newFrozenQuantity = bcsub($frozenQuantity, $quantity, self::DEFAULT_PRECISION);
            $newAvailableQuantity = bcadd((string)$position->available_quantity, $quantity, self::DEFAULT_PRECISION);

            $position->frozen_quantity = (float)$newFrozenQuantity;
            $position->available_quantity = (float)$newAvailableQuantity;
            $position->save();

            return true;
        });
    }

    private function dispatchProfitSharingJob(CopySpotPosition $position): void
    {
        try {
            $quote_currency_id = $this->cacheRedis->hGet(CurrencyConfigKey::getCurrencyKey(intval($position->currency_id)),Currency::FIELD_QUOTE_ASSETS_ID) ?? 1273;
            $job = new CopySpotProfitSharingJob([
                'follower_user_id' => $position->follower_user_id,
                'expert_user_id' => $position->expert_user_id,
                'currency_id' => $quote_currency_id,
                'realized_pnl' => (string)$position->realized_pnl,
                'position_id' => $position->id
            ]);

            pushAsyncJob(AsyncExecutorKey::MATCH_ORDER_QUEUE->value, $job);

            $this->logger->info('投递现货分润任务', [
                'position_id' => $position->id,
                'follower_user_id' => $position->follower_user_id,
                'expert_user_id' => $position->expert_user_id,
                'currency_id' => $quote_currency_id,
                'realized_pnl' => $position->realized_pnl
            ]);

        } catch (\Exception $e) {
            $this->logger->error('投递现货分润任务失败', [
                'position_id' => $position->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
        }
    }
}
