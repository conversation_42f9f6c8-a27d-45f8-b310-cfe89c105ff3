<?php

declare(strict_types=1);

namespace App\Http\Api\Service\V1\Copy;

use App\Enum\OrderStatus;
use App\Enum\OrderType;
use App\Enum\TradeSide;
use App\Http\Api\Service\V1\TradeSpotService;
use App\Model\Copy\CopySpotOrder;
use App\Model\Copy\CopySpotExpert;
use App\Model\Copy\CopyExclusiveMember;
use App\Model\Copy\Enums\CopyMode;
use App\Model\Trade\TradeSpotOrder;
use Hyperf\Di\Annotation\Inject;
use Hyperf\DbConnection\Db;
use Psr\Log\LoggerInterface;

class CopySpotTradeService
{
    #[Inject]
    private TradeSpotService $tradeSpotService;

    #[Inject]
    private LoggerInterface $logger;

    public function batchCreateCopyOrders(array $copyOrdersData, TradeSpotOrder $originalOrder): array
    {
        $results = [];
        $successCount = 0;
        $failedCount = 0;

        foreach ($copyOrdersData as $orderData) {
            try {
                $result = $this->createSingleCopyOrder($orderData, $originalOrder);
                
                if ($result['success']) {
                    $successCount++;
                } else {
                    $failedCount++;
                }
                
                $results[] = $result;
                
            } catch (\Exception $e) {
                $this->logger->error('创建跟单订单失败', [
                    'follower_id' => $orderData['follower_user_id'],
                    'original_order_id' => $originalOrder->getId(),
                    'error' => $e->getMessage()
                ]);
                
                $failedCount++;
                $results[] = [
                    'success' => false,
                    'follower_id' => $orderData['follower_user_id'],
                    'error' => $e->getMessage()
                ];
            }
        }

        $this->logger->info('批量创建跟单订单结果', [
            'original_order_id' => $originalOrder->getId(),
            'total' => count($copyOrdersData),
            'success' => $successCount,
            'failed' => $failedCount
        ]);

        return [
            'total' => count($copyOrdersData),
            'success_count' => $successCount,
            'failed_count' => $failedCount,
            'results' => $results
        ];
    }

    public function createSingleCopyOrder(array $orderData, TradeSpotOrder $originalOrder): array
    {
        return Db::transaction(function () use ($orderData, $originalOrder) {
            try {
                // 获取专家配置信息
                $expert = CopySpotExpert::query()->where('user_id',$orderData['expert_id'])->first();
                if (!$expert) {
                    throw new \RuntimeException('专家信息不存在');
                }

                // 检查尊享模式权限
                if ($expert->getExclusiveMode() && !$this->checkExclusivePermission($orderData['follower_user_id'], $orderData['expert_id'])) {
                    throw new \RuntimeException('用户无尊享模式跟单权限');
                }

                // 直接使用监听器准备好的订单数据格式
                $copyOrderData = [
                    'currency_id' => $orderData['currency_id'],
                    'side' => $orderData['side'],
                    'type' => $orderData['type'],
                    'price' => $orderData['price'],
                    'quantity' => $orderData['quantity'],
                    'copy_order_id' => $orderData['copy_order_id']
                ];

                $spotOrderResult = $this->tradeSpotService->placeOrder(
                    $orderData['follower_user_id'],
                    $copyOrderData
                );

                $copySpotOrder = new CopySpotOrder();
                $copySpotOrder->follower_user_id = $orderData['follower_user_id'];
                $copySpotOrder->expert_id = $orderData['expert_id'];
                $copySpotOrder->expert_user_id = $originalOrder->getUserId();
                $copySpotOrder->expert_order_id = $originalOrder->getId();
                $copySpotOrder->follower_order_id = $spotOrderResult['order_id'];
                $copySpotOrder->mode = CopyMode::MULTI_EXPLORE;
                $copySpotOrder->profit_sharing_rate = $expert->getProfitSharingRate();
                $copySpotOrder->is_exclusive = $expert->getExclusiveMode();
                $copySpotOrder->copy_settings_snapshot = [
                    'copy_amount' => $orderData['copy_amount'] ?? '0',
                    'copy_type' => $orderData['copy_type'] ?? 'unknown',
                    'currency_id' => $orderData['currency_id'],
                    'original_price' => $orderData['price'],
                    'original_quantity' => $orderData['quantity'],
                    'expert_profit_sharing_rate' => $expert->getProfitSharingRate(),
                    'expert_exclusive_mode' => $expert->getExclusiveMode(),
                    'expert_min_follow_amount' => $expert->getMinFollowAmount()
                ];
                
                if (!$copySpotOrder->save()) {
                    throw new \RuntimeException('保存跟单记录失败');
                }

                $this->logger->info('创建跟单订单成功', [
                    'follower_id' => $orderData['follower_user_id'],
                    'original_order_id' => $originalOrder->getId(),
                    'copy_order_id' => $spotOrderResult['order_id'],
                    'copy_amount' => $orderData['copy_amount']
                ]);

                return [
                    'success' => true,
                    'follower_id' => $orderData['follower_user_id'],
                    'copy_order_id' => $spotOrderResult['order_id'],
                    'copy_spot_order_id' => $copySpotOrder->id,
                    'copy_amount' => $orderData['copy_amount']
                ];

            } catch (\Exception $e) {
                $this->logger->error('创建跟单订单事务失败', [
                    'follower_id' => $orderData['follower_user_id'],
                    'original_order_id' => $originalOrder->getId(),
                    'error' => $e->getMessage()
                ]);

                throw $e;
            }
        });
    }

    private function getOrderTypeString(int $orderType): string
    {
        return match ($orderType) {
            1 => 'limit',
            2 => 'market',
            default => 'limit'
        };
    }

    public function createCopyCloseOrder(
        int $followerUserId,
        int $expertId,
        TradeSpotOrder $originalOrder,
        string $closeQuantity,
        string $closePrice
    ): array {
        return Db::transaction(function () use ($followerUserId, $expertId, $originalOrder, $closeQuantity, $closePrice) {
            try {
                // 获取专家配置信息
                $expert = CopySpotExpert::query()->where('user_id',$expertId)->first();
                if (!$expert) {
                    throw new \RuntimeException('专家信息不存在');
                }

                // 检查尊享模式权限
                if ($expert->getExclusiveMode() && !$this->checkExclusivePermission($followerUserId, $expertId)) {
                    throw new \RuntimeException('用户无尊享模式跟单权限');
                }

                $copyOrderData = [
                    'currency_id' => $originalOrder->getCurrencyId(),
                    'side' => $originalOrder->getDirection() === intval(TradeSide::SELL_INT->value) ? 'sell' : 'buy',
                    'type' => $originalOrder->getOrderType() === intval(OrderType::MARKET_INT->value) ? 'market' : 'limit',
                    'price' => $closePrice,
                    'quantity' => $closeQuantity,
                    'copy_order_id' => $originalOrder->getId()
                ];

                $spotOrderResult = $this->tradeSpotService->placeOrder($followerUserId, $copyOrderData);

                $copySpotOrder = new CopySpotOrder();
                $copySpotOrder->follower_user_id = $followerUserId;
                $copySpotOrder->expert_id = $expertId;
                $copySpotOrder->expert_user_id = $originalOrder->getUserId();
                $copySpotOrder->expert_order_id = $originalOrder->getId();
                $copySpotOrder->follower_order_id = $spotOrderResult['order_id'];
                $copySpotOrder->mode = CopyMode::MULTI_EXPLORE;
                $copySpotOrder->profit_sharing_rate = $expert->getProfitSharingRate();
                $copySpotOrder->is_exclusive = $expert->getExclusiveMode();
                $copySpotOrder->copy_settings_snapshot = [
                    'copy_amount' => bcmul($closeQuantity, $closePrice, 8),
                    'copy_type' => 'close',
                    'currency_id' => $originalOrder->getCurrencyId(),
                    'close_quantity' => $closeQuantity,
                    'close_price' => $closePrice,
                    'expert_profit_sharing_rate' => $expert->getProfitSharingRate(),
                    'expert_exclusive_mode' => $expert->getExclusiveMode(),
                    'expert_min_follow_amount' => $expert->getMinFollowAmount()
                ];
                
                if (!$copySpotOrder->save()) {
                    throw new \RuntimeException('保存平仓跟单记录失败');
                }

                $this->logger->info('创建平仓跟单订单成功', [
                    'follower_id' => $followerUserId,
                    'original_order_id' => $originalOrder->getId(),
                    'close_order_id' => $spotOrderResult['order_id'],
                    'close_quantity' => $closeQuantity
                ]);

                return [
                    'success' => true,
                    'follower_id' => $followerUserId,
                    'close_order_id' => $spotOrderResult['order_id'],
                    'copy_spot_order_id' => $copySpotOrder->id,
                    'close_quantity' => $closeQuantity
                ];

            } catch (\Exception $e) {
                $this->logger->error('创建平仓跟单订单失败', [
                    'follower_id' => $followerUserId,
                    'original_order_id' => $originalOrder->getId(),
                    'error' => $e->getMessage()
                ]);

                throw $e;
            }
        });
    }

    public function getCopyOrdersByOriginalOrder(int $traderId): array
    {
        return CopySpotOrder::where([
            'expert_id' => $traderId
        ])->leftJoin(
            'trade_spot_order',
            'trade_spot_order.id',
            '=',
            'copy_spot_order.follower_order_id'
        )->select(
            [
                'copy_spot_order.id',
                'copy_spot_order.follower_user_id',
                'copy_spot_order.expert_id',
                'copy_spot_order.expert_user_id',
                'copy_spot_order.expert_order_id',
                'copy_spot_order.follower_order_id',
                'copy_spot_order.profit_sharing_rate',
                'copy_spot_order.is_exclusive',
                'copy_spot_order.copy_settings_snapshot',
                'trade_spot_order.is_trader',
                'trade_spot_order.status as follower_order_status',
                'trade_spot_order.status as follower_order_status',
                'trade_spot_order.status as follower_order_status'
            ]
        )
            ->get()
            ->toArray();
    }

    public function updateCopyOrderStatus(int $copySpotOrderId, int $status): bool
    {
        return CopySpotOrder::where('id', $copySpotOrderId)
            ->update(['status' => $status]) > 0;
    }

    private function checkExclusivePermission(int $followerUserId, int $expertId): bool
    {
        return CopyExclusiveMember::where('follower_user_id', $followerUserId)
            ->where('expert_id', $expertId)
            ->exists();
    }
}
