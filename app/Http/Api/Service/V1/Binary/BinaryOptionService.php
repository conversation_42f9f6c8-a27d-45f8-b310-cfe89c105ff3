<?php

declare(strict_types=1);

namespace App\Http\Api\Service\V1\Binary;

use App\Enum\AsyncExecutorKey;
use App\Enum\BinaryOptionCacheKey;
use App\Enum\MarketType;
use App\Exception\BusinessException;
use App\Http\Common\ResultCode;
use App\Job\BinaryOption\BinaryOptionOrderJob;
use App\Model\Binary\BinaryOptionConfig;
use App\Model\Binary\BinaryOptionOrder;
use App\Model\Currency\Currency;
use App\Model\Enums\Binary\BinaryOptionEnum;
use App\Model\Enums\Binary\BinaryOptionStatus;
use App\Model\Enums\Binary\BinaryOptionPriceSource;
use App\Model\Enums\Binary\BinaryOptionRateType;
use App\Model\Enums\Binary\BinaryOptionResult;
use App\Service\RedisFactory\CacheRedis;
use App\Service\RedisFactory\MarketRedis;
use App\Service\UserAccounts\UserAccountsAssetService;
use App\Model\Enums\User\AccountType;
use App\Model\Enums\User\FlowsType;
use App\Trait\Config\CurrencyConfigTrait;
use Carbon\Carbon;
use Hyperf\Di\Annotation\Inject;
use Hyperf\DbConnection\Db;
use Hyperf\Snowflake\IdGeneratorInterface;
use Psr\Log\LoggerInterface;

class BinaryOptionService
{
    use CurrencyConfigTrait;

    #[Inject]
    protected BinaryOptionConfig $binaryOptionConfig;

    #[Inject]
    protected BinaryOptionOrder $binaryOptionOrder;

    #[Inject]
    protected UserAccountsAssetService $userAccountsAssetService;

    #[Inject]
    protected CacheRedis $redis;

    #[Inject]
    protected MarketRedis $marketRedis;

    #[Inject]
    protected IdGeneratorInterface $idGenerator;

    #[Inject]
    protected LoggerInterface $logger;

    /**
     * 获取可用配置列表
     */
    public function getAvailableConfigs(?int $currencyId = null): array
    {
        $cacheKey = BinaryOptionCacheKey::getAllConfigsKey();

        $configs = $this->redis->get($cacheKey);
        if (!$configs) {
            $query = $this->binaryOptionConfig->where('status', 1);
            if ($currencyId) {
                $query->where('currency_id', $currencyId);
            }

            $configs = $query->orderBy('currency_id')
                ->orderBy('time_period')
                ->get();
            if ($configs->isEmpty()) {
                $configs = [
                    [
                        'currency_id' => $currencyId,
                        'time_period' => "60",
                        'time_period_name' => '1m',
                        'price_source' => BinaryOptionPriceSource::CONTRACT->value,
                        'win_rate_type' => BinaryOptionRateType::FIXED->value,
                        'lose_rate_type' => BinaryOptionRateType::FIXED->value,
                        'fixed_win_rate' => 0.8,
                        'fixed_lose_rate' => 0.95,
                        'dynamic_win_formula' => '',
                        'dynamic_lose_formula' => '',
                        'min_amount' => 10,
                        'max_amount' => 500,
                        'status' => 1
                    ], [
                        'currency_id' => $currencyId,
                        'time_period' => "300",
                        'time_period_name' => '5m',
                        'price_source' => BinaryOptionPriceSource::CONTRACT->value,
                        'win_rate_type' => BinaryOptionRateType::FIXED->value,
                        'lose_rate_type' => BinaryOptionRateType::FIXED->value,
                        'fixed_win_rate' => 0.8,
                        'fixed_lose_rate' => 0.95,
                        'dynamic_win_formula' => '',
                        'dynamic_lose_formula' => '',
                        'min_amount' => 10,
                        'max_amount' => 500,
                        'status' => 1
                    ], [
                        'currency_id' => $currencyId,
                        'time_period' => "600",
                        'time_period_name' => '10m',
                        'price_source' => BinaryOptionPriceSource::CONTRACT->value,
                        'win_rate_type' => BinaryOptionRateType::FIXED->value,
                        'lose_rate_type' => BinaryOptionRateType::FIXED->value,
                        'fixed_win_rate' => 0.8,
                        'fixed_lose_rate' => 0.95,
                        'dynamic_win_formula' => '',
                        'dynamic_lose_formula' => '',
                        'min_amount' => 10,
                        'max_amount' => 500,
                        'status' => 1
                    ], [
                        'currency_id' => $currencyId,
                        'time_period' => "3600",
                        'time_period_name' => '1H',
                        'price_source' => BinaryOptionPriceSource::CONTRACT->value,
                        'win_rate_type' => BinaryOptionRateType::FIXED->value,
                        'lose_rate_type' => BinaryOptionRateType::FIXED->value,
                        'fixed_win_rate' => 0.8,
                        'fixed_lose_rate' => 0.95,
                        'dynamic_win_formula' => '',
                        'dynamic_lose_formula' => '',
                        'min_amount' => 10,
                        'max_amount' => 500,
                        'status' => 1
                    ]
                ];
            } else {
                $configs = $configs->toArray();
            }

        } else {
            $configs = json_decode($configs, true);
            if ($currencyId) {
                $configs = array_filter($configs, fn($config) => $config['currency_id'] == $currencyId);
            }
        }

        return array_values($configs);
    }

    /**
     * 获取特定配置
     */
    public function getConfig(int $currencyId, int $timePeriod): ?array
    {
        $cacheKey = BinaryOptionCacheKey::getConfigKey($currencyId, $timePeriod);

        $config = $this->redis->get($cacheKey);
        if (!$config) {
            $configModel = $this->binaryOptionConfig
                ->where('currency_id', $currencyId)
                ->where('time_period', $timePeriod)
                ->where('status', 1)
                ->first();

            if (!$configModel) {
                $configModel = $this->binaryOptionConfig->fill([
                    'currency_id' => $currencyId,
                    'time_period' => $timePeriod,
                    'time_period_name' => 'm',
                    'price_source' => BinaryOptionPriceSource::CONTRACT->value,
                    'win_rate_type' => BinaryOptionRateType::FIXED->value,
                    'lose_rate_type' => BinaryOptionRateType::FIXED->value,
                    'fixed_win_rate' => 0.8,
                    'fixed_lose_rate' => 0.95,
                    'dynamic_win_formula' => '',
                    'dynamic_lose_formula' => '',
                    'min_amount' => 10,
                    'max_amount' => 500,
                    'status' => 1
                ]);
            }

            $config = $configModel->toArray();
            $this->redis->setex($cacheKey, 3600, json_encode($config));
        } else {
            $config = json_decode($config, true);
        }

        return $config;
    }

    /**
     * 下单
     */
    public function placeOrder(int $userId, array $orderData): array
    {
        $currencyId = $orderData['currency_id'];
        $timePeriod = $orderData['time_period'];
        $direction = $orderData['direction'];
        $investAmount = $orderData['invest_amount'];

        // 获取配置
        $config = $this->getConfig((int)$currencyId, (int)$timePeriod);
        if (!$config) {
            throw new BusinessException(ResultCode::FAIL, '该币种和时间周期暂不支持交易');
        }

        // 验证投资金额
        if (bccomp($investAmount, strval($config['min_amount']), 8) < 0) {
            throw new BusinessException(ResultCode::FAIL, '投资金额不能小于最小限额');
        }

        if (bccomp($investAmount, strval($config['max_amount']), 8) > 0) {
            throw new BusinessException(ResultCode::FAIL, '投资金额不能大于最大限额');
        }

        $quote_currency_id = (int)$this->getCurrencyConfigByKey((int)$config['currency_id'], Currency::FIELD_QUOTE_ASSETS_ID, $this->redis);

        return Db::transaction(function () use ($userId, $config, $direction, $investAmount, $quote_currency_id) {
            // 冻结用户资金
            $this->userAccountsAssetService->freezeAsset(
                $userId,
                AccountType::SPOT->value,
                $quote_currency_id,
                (float)$investAmount,
                FlowsType::BINARY_OPTION_FREEZE->value,
                0
            );

            // 获取当前价格
            $currentPrice = $this->getCurrentPrice($config['currency_id'], $config['price_source']);

            // 计算盈亏比例
            $rates = $this->calculateRates($config, $currentPrice);

            // 生成订单号
            $orderNo = $this->generateOrderNo();

            // 创建订单
            $orderData = [
                'order_no' => $orderNo,
                'user_id' => $userId,
                'currency_id' => $config['currency_id'],
                'time_period' => $config['time_period'],
                'direction' => $direction,
                'invest_amount' => $investAmount,
                'price_source' => $config['price_source'],
                'open_price' => $currentPrice,
                'win_rate' => $rates['win_rate'],
                'lose_rate' => $rates['lose_rate'],
                'status' => BinaryOptionStatus::PENDING->value,
                'open_time' => Carbon::now(),
                'expire_time' => Carbon::now()->addSeconds($config['time_period']),
            ];

            $order = $this->binaryOptionOrder->create($orderData);

            // 添加到待结算队列
            $this->addToSettlementQueue($order->id, (int)$config['time_period']);

            return [
                'order_id' => $order->id,
                'order_no' => $orderNo,
                'open_price' => $currentPrice,
                'expire_time' => $orderData['expire_time'],
                'win_rate' => $rates['win_rate'],
                'lose_rate' => $rates['lose_rate'],
            ];
        });
    }

    /**
     * 获取当前价格
     */
    protected function getCurrentPrice(int $currencyId, int $priceSource): string
    {
        return (string)$this->getCurrencyPrice($currencyId, MarketType::MARGIN, $this->marketRedis);
    }

    /**
     * 获取现货价格
     */
    protected function getSpotPrice(int $currencyId): ?string
    {
        return (string)$this->getCurrencyPrice($currencyId, MarketType::CRYPTO, $this->marketRedis);
    }

    /**
     * 获取合约价格
     */
    protected function getContractPrice(int $currencyId): ?string
    {
        return (string)$this->getCurrencyPrice($currencyId, MarketType::MARGIN, $this->marketRedis);
    }

    /**
     * 计算盈亏比例
     */
    protected function calculateRates(array $config, string $currentPrice): array
    {
        $winRate = $config['fixed_win_rate'] ?? '0.8000';
        $loseRate = $config['fixed_lose_rate'] ?? '1.0000';

        // 如果是动态比例，需要计算
        if ($config['win_rate_type'] == BinaryOptionRateType::DYNAMIC->value && $config['dynamic_win_formula']) {
            $winRate = $this->calculateDynamicRate($config['dynamic_win_formula'], $currentPrice, $config['currency_id']);
        }

        if ($config['lose_rate_type'] == BinaryOptionRateType::DYNAMIC->value && $config['dynamic_lose_formula']) {
            $loseRate = $this->calculateDynamicRate($config['dynamic_lose_formula'], $currentPrice, $config['currency_id']);
        }

        return [
            'win_rate' => $winRate,
            'lose_rate' => $loseRate,
        ];
    }

    /**
     * 计算动态比例
     */
    protected function calculateDynamicRate(string $formula, string $currentPrice, int $currencyId): string
    {
        // 获取现货价格和合约价格用于公式计算
        $spotPrice = $this->getSpotPrice($currencyId);
        $contractPrice = $this->getContractPrice($currencyId);

        $priceDiff = bcsub($spotPrice, $contractPrice, 8);
        $priceDiff = bccomp($priceDiff, '0', 8) >= 0 ? $priceDiff : bcmul($priceDiff, '-1', 8);
        $priceDiffRate = bcdiv($priceDiff, $spotPrice, 8);

        // 替换公式中的变量
        $variables = [
            '{spot_price}' => $spotPrice,
            '{contract_price}' => $contractPrice,
            '{price_diff}' => $priceDiff,
            '{price_diff_rate}' => $priceDiffRate,
        ];

        $processedFormula = str_replace(array_keys($variables), array_values($variables), $formula);

        // 安全计算公式结果
        return $this->safeEvalFormula($processedFormula);
    }

    /**
     * 安全计算公式
     */
    protected function safeEvalFormula(string $formula): string
    {
        // 简单的公式解析，只支持基本运算
        // 实际项目中建议使用专门的表达式解析库
        try {
            // 移除空格
            $formula = str_replace(' ', '', $formula);

            // 基本安全检查
            if (!preg_match('/^[0-9+\-*\/\.\(\)]+$/', $formula)) {
                throw new \Exception('Invalid formula');
            }

            $result = eval("return $formula;");
            return number_format($result, 4, '.', '');
        } catch (\Exception $e) {
            $this->logger->error('Formula calculation error', ['formula' => $formula, 'error' => $e->getMessage()]);
            return '0.8000'; // 默认值
        }
    }

    /**
     * 生成订单号
     */
    protected function generateOrderNo(): int
    {
        return $this->idGenerator->generate();
    }

    /**
     * 添加到Redis延时队列 (使用现有的异步队列系统)
     */
    protected function addToSettlementQueue(int $order_id, int $delay = 0): void
    {
        pushAsyncJob(AsyncExecutorKey::BINARY_ORDER_QYEUE->value, new BinaryOptionOrderJob($order_id), $delay);
    }

    /**
     * 获取用户订单列表
     */
    public function getUserOrders(int $userId, array $params = []): array
    {
        $query = $this->binaryOptionOrder->where('user_id', $userId);

        if (!empty($params['currency_id'])) {
            $query->where('currency_id', $params['currency_id']);
        }

        if (!empty($params['status'])) {
            $query->where('status', $params['status']);
        }

        if (!empty($params['start_time'])) {
            $query->where('open_time', '>=', $params['start_time']);
        }

        if (!empty($params['end_time'])) {
            $query->where('open_time', '<=', $params['end_time']);
        }

        $page = $params['page'] ?? 1;
        $limit = $params['limit'] ?? 20;

        return $query->orderBy('id', 'desc')
            ->paginate($limit, ['*'], 'page', $page)
            ->toArray();
    }

    /**
     * 获取订单详情
     */
    public function getOrderDetail(int $userId, int $orderId): array
    {
        $order = $this->binaryOptionOrder
            ->where('id', $orderId)
            ->where('user_id', $userId)
            ->first();

        if (!$order) {
            throw new BusinessException(ResultCode::FAIL, '订单不存在');
        }

        return $order->toArray();
    }

    /**
     * 取消订单
     */
    public function cancelOrder(int $userId, int $orderId): bool
    {
        return Db::transaction(function () use ($userId, $orderId) {
            $order = $this->binaryOptionOrder
                ->where('id', $orderId)
                ->where('user_id', $userId)
                ->where('status', BinaryOptionStatus::PENDING->value)
                ->lockForUpdate()
                ->first();

            if (!$order) {
                throw new BusinessException(ResultCode::FAIL, '订单不存在或无法取消');
            }

            // 检查是否已过期
            if (strtotime($order->expire_time) <= time()) {
                throw new BusinessException(ResultCode::FAIL, '订单已过期，无法取消');
            }

            // 更新订单状态
            $order->status = BinaryOptionStatus::CANCELLED->value;
            $order->save();

            // 解冻资金
            $this->userAccountsAssetService->unfreezeAsset(
                $userId,
                AccountType::SPOT->value,
                $order->currency_id,
                (float)$order->invest_amount,
                FlowsType::BINARY_OPTION_UNFREEZE->value,
                0
            );

            // 从延时队列移除 (如果使用Redis ZSET)
            $queueKey = BinaryOptionCacheKey::getPendingOrdersKey();
            $this->redis->zrem($queueKey, $orderId);

            return true;
        });
    }

    /**
     * 结算订单 (通过Redis延时队列触发)
     */
    public function settleOrder(int $orderId): bool
    {
        $lockKey = BinaryOptionCacheKey::getSettlementLockKey($orderId);

        // 使用Redis锁防止重复结算
        $lock = $this->redis->set($lockKey, 1, ['NX', 'EX' => 30]);
        if (!$lock) {
            return false;
        }

        try {
            return Db::transaction(function () use ($orderId) {
                $order = $this->binaryOptionOrder
                    ->where('id', $orderId)
                    ->where('status', BinaryOptionStatus::PENDING->value)
                    ->lockForUpdate()
                    ->first();

                if (!$order) {
                    return false;
                }

                // 获取结算价格
                $closePrice = $this->getCurrentPrice($order->currency_id, $order->price_source);

                // 判断盈亏
                $result = $this->judgeResult($order->direction, $order->open_price, $closePrice);

                // 计算结算金额
                $payoutAmount = $this->calculatePayout($order, $result);

                // 更新订单
                $order->close_price = $closePrice;
                $order->payout_amount = $payoutAmount;
                $order->status = $this->getStatusByResult($result);
                $order->settle_time =Carbon::now();
                $order->save();

                // 处理资金
                $this->processSettlement($order, $result, $payoutAmount);

                return true;
            });
        } finally {
            $this->redis->del($lockKey);
        }
    }

    /**
     * 判断结果
     */
    protected function judgeResult(int $direction, string $openPrice, string $closePrice): int
    {
        $priceChange = bccomp($closePrice, $openPrice, 8);

        if ($priceChange == 0) {
            return BinaryOptionResult::DRAW->value; // 平局
        }

        $actualDirection = $priceChange > 0 ? BinaryOptionEnum::UP->value : BinaryOptionEnum::DOWN->value;

        return $direction == $actualDirection ? BinaryOptionResult::WIN->value : BinaryOptionResult::LOSE->value;
    }

    /**
     * 计算结算金额
     */
    protected function calculatePayout(BinaryOptionOrder $order, int $result): string
    {
        $investAmount = (string)$order->invest_amount;

        if ($result == BinaryOptionResult::WIN->value) {
            // 盈利：本金 + 盈利
            $profit = bcmul($investAmount, (string)$order->win_rate, 8);
            return bcadd($investAmount, $profit, 8);
        } elseif ($result == BinaryOptionResult::LOSE->value) {
            // 亏损：本金 * (1 - 亏损比例)
            $loss = bcmul($investAmount, (string)$order->lose_rate, 8);
            return bcsub($investAmount, $loss, 8);
        } else {
            // 平局：返还本金
            return $investAmount;
        }
    }

    /**
     * 根据结果获取状态
     */
    protected function getStatusByResult(int $result): int
    {
        return match ($result) {
            BinaryOptionResult::WIN->value => BinaryOptionStatus::WIN->value,
            BinaryOptionResult::LOSE->value => BinaryOptionStatus::LOSE->value,
            BinaryOptionResult::DRAW->value => BinaryOptionStatus::DRAW->value,
            default => BinaryOptionStatus::PENDING->value,
        };
    }

    /**
     * 处理结算
     */
    protected function processSettlement(BinaryOptionOrder $order, int $result, string $payoutAmount): void
    {
        $quote_currency_id = (int)$this->getCurrencyConfigByKey((int)$order->currency_id, Currency::FIELD_QUOTE_ASSETS_ID, $this->redis);

        // 根据结果处理资金
        if ($result == BinaryOptionResult::WIN->value) {
            // 盈利：解冻资金到可用余额，然后增加盈利部分
            $this->userAccountsAssetService->unfreezeAsset(
                $order->user_id,
                AccountType::SPOT->value,
                intval($quote_currency_id),
                (float)$order->invest_amount,
                FlowsType::BINARY_OPTION_UNFREEZE->value,
                0
            );

            $profit = bcsub($payoutAmount, (string)$order->invest_amount, 8);
            if (bccomp($profit, '0', 8) > 0) {
                $this->userAccountsAssetService->addAvailableAsset(
                    $order->user_id,
                    AccountType::SPOT->value,
                    intval($quote_currency_id),
                    (float)$profit,
                    FlowsType::BINARY_OPTION_WIN->value,
                    0
                );
            }
        } elseif ($result == BinaryOptionResult::LOSE->value) {
            // 亏损：从冻结资金中扣除亏损，剩余部分解冻
            $loss = bcsub((string)$order->invest_amount, $payoutAmount, 8);
            if (bccomp($loss, '0', 8) > 0) {
                $this->userAccountsAssetService->deductFrozenAsset(
                    $order->user_id,
                    AccountType::SPOT->value,
                    intval($quote_currency_id),
                    (float)$loss,
                    FlowsType::BINARY_OPTION_LOSE->value,
                    0
                );
            }

            // 解冻剩余部分
            if (bccomp($payoutAmount, '0', 8) > 0) {
                $this->userAccountsAssetService->unfreezeAsset(
                    $order->user_id,
                    AccountType::SPOT->value,
                    intval($quote_currency_id),
                    (float)$payoutAmount,
                    FlowsType::BINARY_OPTION_UNFREEZE->value,
                    0
                );
            }
        } else {
            // 平局：直接解冻原始金额
            $this->userAccountsAssetService->unfreezeAsset(
                $order->user_id,
                AccountType::SPOT->value,
                intval($quote_currency_id),
                (float)$order->invest_amount,
                FlowsType::BINARY_OPTION_UNFREEZE->value,
                0
            );
        }
    }


    /**
     * 获取用户统计数据
     */
    public function getUserStatistics(int $userId, array $params = []): array
    {
        $query = $this->binaryOptionOrder->where('user_id', $userId);

        if (!empty($params['start_date'])) {
            $query->where('open_time', '>=', $params['start_date']);
        }

        if (!empty($params['end_date'])) {
            $query->where('open_time', '<=', $params['end_date']);
        }

        if (!empty($params['currency_id'])) {
            $query->where('currency_id', $params['currency_id']);
        }

        $orders = $query->get();

        $totalOrders = $orders->count();
        $winOrders = $orders->where('status', BinaryOptionStatus::WIN->value)->count();
        $loseOrders = $orders->where('status', BinaryOptionStatus::LOSE->value)->count();
        $drawOrders = $orders->where('status', BinaryOptionStatus::DRAW->value)->count();

        $totalInvest = $orders->sum('invest_amount');
        $totalPayout = $orders->whereIn('status', [
            BinaryOptionStatus::WIN->value,
            BinaryOptionStatus::LOSE->value,
            BinaryOptionStatus::DRAW->value
        ])->sum('payout_amount');

        $winRate = $totalOrders > 0 ? round(($winOrders / $totalOrders) * 100, 2) : 0;
        $profit = bcsub($totalPayout, $totalInvest, 8);

        return [
            'total_orders' => $totalOrders,
            'win_orders' => $winOrders,
            'lose_orders' => $loseOrders,
            'draw_orders' => $drawOrders,
            'win_rate' => $winRate,
            'total_invest' => $totalInvest,
            'total_payout' => $totalPayout,
            'profit' => $profit,
        ];
    }
}
