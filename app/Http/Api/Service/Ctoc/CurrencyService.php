<?php

declare(strict_types=1);
/**
 * CurrencyService
 * Author:XTeam
 * Contact:tg:@chenmaq
 * Version:1.0
 * Date:2025-07-18
 * Website:xxx
 */

namespace App\Http\Api\Service\Ctoc;

use App\Http\Api\Service\BaseService;
use App\Http\Api\Request\Ctoc\CurrencyRequest;
use App\Model\Currency\Currency;
use App\QueryBuilder\QueryBuilder;

class CurrencyService extends BaseService
{
    public function list(CurrencyRequest $request)
    {
       return QueryBuilder::for(Currency::class, $request)
            ->filters(['symbol', 'name'])
            ->defaultSort('id')
            ->allowedSorts(['id','created_at'])
            ->pagex();
    }
}
