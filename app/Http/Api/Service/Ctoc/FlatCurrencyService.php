<?php

declare(strict_types=1);
/**
 * FlatCurrencyService
 * Author:XTeam
 * Contact:tg:@chenmaq
 * Version:1.0
 * Date:2025-07-17
 * Website:xxx
 */

namespace App\Http\Api\Service\Ctoc;

use App\Http\Api\Service\BaseService;
use App\Http\Api\Request\Ctoc\FlatCurrencyRequest;
use App\Model\Ctoc\FlatCurrency;
use App\QueryBuilder\QueryBuilder;
use Plugin\West\SysSettings\Model\Config;

class FlatCurrencyService extends BaseService
{
    public function list(FlatCurrencyRequest $request)
    {
        // $query = Model::query();
        // return QueryBuilder::for($query, $request)
        //    ->filters(['name'])
        //    ->defaultSort('sort')
        //    ->allowedSorts(['id', 'sort', 'created_at'])
        //    ->page();
        $list = FlatCurrency::query()->get([
                    FlatCurrency::FIELD_ID,
                    FlatCurrency::FIELD_PAYMENT_TYPE,
                    FlatCurrency::FIELD_TITLE,
                    FlatCurrency::FIELD_ICON,
                ])->toArray();

        //取出 payment_type 并去重转换成数组 payment_type是一个数组，然后从config表中取出对应的名称，再把他赋值到payment_type_name字段
         foreach ($list as &$item) {
            $paymentTypes = $item['payment_type'] ?? [];

            if (!empty($paymentTypes) && is_array($paymentTypes)) {
                // 去重
                $uniqueTypes = array_unique($paymentTypes);

                // 查询 config 表
                $configItems = Config::query()
                    ->whereIn('value', $uniqueTypes)
                    ->get(['value', 'name'])
                    ->keyBy('value')
                    ->toArray();

                // 构造新的 payment_type 结构
                $item['payment_type_name'] = array_map(function ($value) use ($configItems) {
                    return [
                        'value' => $value,
                        'name' => $configItems[$value]['name'] ?? null,
                    ];
                }, $uniqueTypes);
            } else {
                $item['payment_type_name'] = [];
            }
        }

        return $list;
    }
}
