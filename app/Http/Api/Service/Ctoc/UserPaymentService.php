<?php

declare(strict_types=1);
/**
 * UserPaymentService
 * Author:XTeam
 * Contact:tg:@chenmaq
 * Version:1.0
 * Date:2025-07-18
 * Website:xxx
 */

namespace App\Http\Api\Service\Ctoc;

use App\Http\Api\Service\BaseService;
use App\Http\Api\Request\Ctoc\UserPaymentRequest;
use App\QueryBuilder\QueryBuilder;
use App\Repository\Ctoc\UserPaymentRepository;
use Hyperf\Di\Annotation\Inject;

class UserPaymentService extends BaseService
{
    #[Inject]
    protected UserPaymentRepository $userPaymentRepository;

    /**
     * 查询用户支付账号
     * Summary of list
     * @param \App\Http\Api\Request\Ctoc\UserPaymentRequest $request
     * @return \Hyperf\Collection\Collection
     */
    public function list(UserPaymentRequest $request)
    {
        return $this->userPaymentRepository->list($request->all());
    }

    public function create(UserPaymentRequest $request){
        return $this->userPaymentRepository->create(array_merge($request->all(), ['user_id' => $request->userId()]));
    }

    public function update(UserPaymentRequest $request){
        $id = $request->input('id');
        return $this->userPaymentRepository->updateById($id,$request->all());
    }

    public function delete(UserPaymentRequest $request){
        $id = $request->input('id');
        return $this->userPaymentRepository->deleteById($id);
    }
}
