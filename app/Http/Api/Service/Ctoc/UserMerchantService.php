<?php

declare(strict_types=1);
/**
 * UserMerchantService
 * Author:XTeam
 * Contact:tg:@chenmaq
 * Version:1.0
 * Date:2025-07-20
 * Website:xxx
 */

namespace App\Http\Api\Service\Ctoc;

use App\Http\Api\Service\BaseService;
use App\Http\Api\Request\Ctoc\UserMerchantRequest;
use App\Model\Ctoc\Enums\UserMerchantEnums;
use App\Model\Ctoc\UserMerchant;
use App\QueryBuilder\QueryBuilder;
use App\Repository\Ctoc\UserMerchantRepository;
use Hyperf\Di\Annotation\Inject;
use RuntimeException;

class UserMerchantService extends BaseService
{
    

    #[Inject]
    protected UserMerchantRepository $userMerchantRepository;
    
    /**
     * 申请商家
     * @param UserMerchantRequest $request
     * @return mixed
     */
    public function create(UserMerchantRequest $request)
    {
        $userId = $request->userId();

        // 1. 检查是否已经申请过
        $existingApplication = UserMerchant::query()->where(UserMerchant::FIELD_USER_ID, $userId)->first();
        if ($existingApplication && in_array($existingApplication->status, [UserMerchantEnums::APPROVED->value, UserMerchantEnums::STAY->value])) {
            throw new RuntimeException('您已经申请过商家，请勿重复申请');
        }
        $changeData = [
                UserMerchant::FIELD_USER_ID => $userId,
                UserMerchant::FIELD_USER_NAME => $request->input(UserMerchant::FIELD_USER_NAME),
                UserMerchant::FIELD_ID_CARD => $request->input(UserMerchant::FIELD_ID_CARD),
                UserMerchant::FIELD_ID_CARD_FRONT => $request->input(UserMerchant::FIELD_ID_CARD_FRONT),
                UserMerchant::FIELD_ID_CARD_BACK => $request->input(UserMerchant::FIELD_ID_CARD_BACK),
                UserMerchant::FIELD_STORE_NAME => $request->input(UserMerchant::FIELD_STORE_NAME),
                UserMerchant::FIELD_STORE_LOGO => $request->input(UserMerchant::FIELD_STORE_LOGO),
                UserMerchant::FIELD_CONTACT_NAME => $request->input(UserMerchant::FIELD_CONTACT_NAME),
                UserMerchant::FIELD_CONTACT_PHONE => $request->input(UserMerchant::FIELD_CONTACT_PHONE),
                UserMerchant::FIELD_CONTACT_EMAIL => $request->input(UserMerchant::FIELD_CONTACT_EMAIL),
                UserMerchant::FIELD_STATUS => UserMerchantEnums::STAY->value, // 默认待审核
        ];
        // 2. 创建商家申请
        if($existingApplication){
            $this->userMerchantRepository->updateById($existingApplication->id,$changeData);
            $merchant = UserMerchant::query()->where(UserMerchant::FIELD_USER_ID, $userId)->first();
        }else{
            $merchant = UserMerchant::create($changeData);
        }
        return $merchant;
    }

    public function detailByUserId(UserMerchantRequest $request){
        return UserMerchant::query()->where(UserMerchant::FIELD_USER_ID, $request->userId())->first();
    }
}
