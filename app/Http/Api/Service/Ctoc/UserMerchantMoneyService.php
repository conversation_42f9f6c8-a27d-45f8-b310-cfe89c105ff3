<?php

declare(strict_types=1);
/**
 * UserMerchantMoneyService
 * Author:XTeam
 * Contact:tg:@chenmaq
 * Version:1.0
 * Date:2025-07-20
 * Website:xxx
 */

namespace App\Http\Api\Service\Ctoc;

use App\Http\Api\Service\BaseService;
use App\Http\Api\Request\Ctoc\UserMerchantMoneyRequest;
use App\QueryBuilder\QueryBuilder;
use App\Repository\Ctoc\UserMerchantMoneyRepository;
use Hyperf\Di\Annotation\Inject;
use Ramsey\Uuid\Nonstandard\Uuid;

class UserMerchantMoneyService extends BaseService
{
    #[Inject]
    protected UserMerchantMoneyRepository $userMerchantMoneyRepository;

    /**
     * 缴纳保证金
     * Summary of cautionMoney
     * @param \App\Http\Api\Request\Ctoc\UserMerchantMoneyRequest $request
     * @return void
     */
    public function cautionMoney(UserMerchantMoneyRequest $request)
    {
        $order_sn = Uuid::uuid4()->toString();
        //创建保证金订单

        //返回保证金收款码
    }
}
