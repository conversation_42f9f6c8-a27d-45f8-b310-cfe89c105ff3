<?php

declare(strict_types=1);
/**
 * PaymentService
 * Author:XTeam
 * Contact:tg:@chenmaq
 * Version:1.0
 * Date:2025-07-17
 * Website:xxx
 */

namespace App\Http\Api\Service\Ctoc;

use App\Http\Api\Service\BaseService;
use App\Http\Api\Request\Ctoc\PaymentRequest;
use App\Model\Ctoc\Payment;
use App\QueryBuilder\QueryBuilder;
use Plugin\West\SysSettings\Model\Config;

class PaymentService extends BaseService
{
    public function list(PaymentRequest $request)
    {
        // $query = Model::query();
        // return QueryBuilder::for($query, $request)
        //    ->filters(['name'])
        //    ->defaultSort('sort')
        //    ->allowedSorts(['id', 'sort', 'created_at'])
        //    ->page();
        // 查询支付方式
        $payment_type = $request->input("payment_type");

        $configs = Config::query()
                        ->whereIn('value', $payment_type)
                        ->get(['value', 'name'])
                        // ->keyBy('value')
                        ->toArray();
        //通过value字段查询 Payment(通过payment_type关联) 表 并赋值到 config的 field 字段里 一个value 在payment中会有多个field
         $payments = Payment::query()
                        ->whereIn('payment_type', $payment_type)
                        ->get([
                            Payment::FIELD_ID,
                            Payment::FIELD_NAME,
                            Payment::FIELD_TYPE,
                            Payment::FIELD_PAYMENT_TYPE,
                            Payment::FIELD_FIELD,
                        ])
                        ->toArray();
        $typeToFieldsMap = [];
        foreach ($payments as $payment) {
            $type = $payment['payment_type'];
            if (!isset($typeToFieldsMap[$type])) {
                $typeToFieldsMap[$type] = [];
            }
            $typeToFieldsMap[$type][] = $payment;
        }

        // 把 fields 赋值给每个 config
        foreach ($configs as $value => &$config) {
            $config['fields'] = $typeToFieldsMap[$config['value']] ?? [];
        }
        unset( $config);
        return $configs;
    }
}
