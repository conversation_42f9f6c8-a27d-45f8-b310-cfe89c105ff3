<?php

declare(strict_types=1);
/**
 * BankService
 * Author:XTeam
 * Contact:tg:@chenmaq
 * Version:1.0
 * Date:2025-07-18
 * Website:xxx
 */

namespace App\Http\Api\Service\Ctoc;

use App\Http\Api\Service\BaseService;
use App\Http\Api\Request\Ctoc\BankRequest;
use App\Model\Ctoc\Bank;
use App\QueryBuilder\QueryBuilder;
use App\Repository\Ctoc\BankRepository;
use Hyperf\Di\Annotation\Inject;

class BankService extends BaseService
{

    #[Inject]
    protected BankRepository $bankRepository;

    public function list(BankRequest $request)
    {
        return $this->bankRepository->page($request->all());
    }
}
