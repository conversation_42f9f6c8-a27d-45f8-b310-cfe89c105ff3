<?php

declare(strict_types=1);
/**
 * 策略平台API
 * 合约专家统计服务类
 */

namespace App\Http\Api\Service\Copy\Expert\Contract;

use App\Http\Api\Service\BaseService;
use App\Model\Copy\CopyContractExpert;
use App\Model\Copy\CopyContractExpertStatistics;

/**
 * 合约专家统计服务类
 */
class ContractStatisticsService extends BaseService
{
    /**
     * 获取合约专家统计数据
     *
     * @param array $params 查询参数
     * @return array 统计数据
     */
    public function statistics(array $params): array
    {
        $days = $params['days'] ?? 30;

        // 获取当前用户的合约专家信息
        $expert = $this->request->getAttribute('contract_expert');

        $field_suffix = $this->getDaysSuffix($days);
        $selectFields = [
            'profit_rate' . $field_suffix,
            'total_profit' . $field_suffix,
            'max_drawdown' . $field_suffix,
            'follower_profit' . $field_suffix,
            'trade_frequency' . $field_suffix,
            'win_rate' . $field_suffix,
            'profit_order_count' . $field_suffix,
            'loss_order_count' . $field_suffix,
        ];

        // 查询专家统计数据
        $statistics = CopyContractExpertStatistics::query()
            ->select($selectFields)
            ->where('expert_id', $expert->id)
            ->first();

        if ($statistics) {
            $data = [
                'profit_rate' => $statistics->profit_rate . $field_suffix,
                'total_profit' => $statistics->total_profit . $field_suffix,
                'max_drawdown' => $statistics->max_drawdown . $field_suffix,
                'follower_profit' => $statistics->follower_profit . $field_suffix,
                'trade_frequency' => $statistics->trade_frequency . $field_suffix,
                'win_rate' => $statistics->win_rate . $field_suffix,
                'profit_order_count' => $statistics->profit_order_count . $field_suffix,
                'loss_order_count' => $statistics->loss_order_count . $field_suffix,
            ];
        } else {
            $data = [
                'profit_rate' => 0,
                'total_profit' => 0,
                'max_drawdown' => 0,
                'follower_profit' => 0,
                'trade_frequency' => 0,
                'win_rate' => 0,
                'profit_order_count' => 0,
                'loss_order_count' => 0,
            ];
        }

        return $data;
    }

    /**
     * 根据天数获取字段后缀
     *
     * @param int $days 天数
     * @return string 字段后缀
     */
    private function getDaysSuffix(int $days): string
    {
        return match ($days) {
            7 => '_7d',
            30 => '_30d',
            90 => '_90d',
            180 => '_180d',
            default => '', // 0表示全部，使用无后缀字段
        };
    }
}
