<?php

declare(strict_types=1);
/**
 * 策略平台API
 * 合约专家综合数据服务类
 */

namespace App\Http\Api\Service\Copy\Expert\Contract;

use App\Http\Api\Service\BaseService;
use App\Model\Copy\CopyContractExpert;
use App\Model\Copy\CopyContractExpertStatistics;
use App\Model\Copy\CopyContractOrder;
use App\Model\Copy\CopyContractUserSetting;
use App\Model\Copy\CopyFollow;
use App\Model\Copy\Enums\CopyStatus;
use App\Model\Copy\Enums\ExpertType;
use App\Model\Enums\User\AccountType;
use App\Service\UserAccounts\UserAccountsAssetService;
use Hyperf\Di\Annotation\Inject;

/**
 * 合约专家综合数据服务类
 */
class ContractSyntheticalService extends BaseService
{
    #[Inject]
    private readonly UserAccountsAssetService $userAccountsAssetService;

    /**
     * 获取合约专家综合数据
     *
     * @return array 综合数据
     */
    public function getSyntheticalData(): array
    {
        // 获取当前用户的合约专家信息
        $expert = $this->request->getAttribute('contract_expert');

        // 1. 查询当前跟单人数
        $currentFollowerCount = $this->getCurrentFollowerCount($expert->id);

        // 2. 获取最大跟单人数
        $maxFollowerCount = $this->getMaxFollowerCount($expert);

        // 3. 获取累计跟单人数
        $totalFollowerCount = $this->getTotalFollowerCount($expert->id);

        // 4. 计算资产管理规模（AUM）
        $aum = $this->getAum($expert->id);

        // 5. 获取总资产（合约跟单账户资金）
        $totalAssets = $this->getTotalAssets($expert->user_id);

        // 6. 查询最近交易时间
        $lastTradeTime = $this->getLastTradeTime($expert->id);

        // 7. 获取分润比例
        $profitSharingRate = $expert->profit_sharing_rate;

        // 8. 统计粉丝数量
        $fanCount = $this->getFanCount($expert->id);

        return [
            'current_follower_count' => $currentFollowerCount,
            'max_follower_count' => $maxFollowerCount,
            'total_follower_count' => $totalFollowerCount,
            'aum' => number_format($aum, 8, '.', ''),
            'total_assets' => number_format($totalAssets, 8, '.', ''),
            'last_trade_time' => $lastTradeTime,
            'profit_sharing_rate' => number_format($profitSharingRate, 2, '.', ''),
            'fan_count' => $fanCount,
        ];
    }

    /**
     * 获取当前跟单人数
     *
     * @param int $expertId 专家ID
     * @return int 当前跟单人数
     */
    private function getCurrentFollowerCount(int $expertId): int
    {
        return CopyContractUserSetting::query()
            ->where('expert_id', $expertId)
            ->where('status', CopyStatus::FOLLOWING)
            ->count();
    }

    /**
     * 获取最大跟单人数
     *
     * @param CopyContractExpert $expert 专家信息
     * @return int 最大跟单人数
     */
    private function getMaxFollowerCount(CopyContractExpert $expert): int
    {
        // 从专家等级配置中获取最大跟单人数
        $level = $expert->level;
        return $level ? $level->max_follow_count : 0;
    }

    /**
     * 获取累计跟单人数
     *
     * @param int $expertId 专家ID
     * @return int 累计跟单人数
     */
    private function getTotalFollowerCount(int $expertId): int
    {
        $statistics = CopyContractExpertStatistics::query()
            ->where('expert_id', $expertId)
            ->first();

        return $statistics ? $statistics->total_follower_count : 0;
    }

    /**
     * 计算资产管理规模（AUM）
     *
     * @param int $expertId 专家ID
     * @return float 资产管理规模
     */
    private function getAum(int $expertId): float
    {
        $statistics = CopyContractExpertStatistics::query()
            ->where('expert_id', $expertId)
            ->first();

        return $statistics ? $statistics->aum : 0.0;
    }

    /**
     * 获取总资产（专家合约跟单账户当前资金）
     *
     * @param int $userId 用户ID
     * @return float 总资产
     */
    private function getTotalAssets(int $userId): float
    {
        // 获取用户合约跟单账户的所有资产
        $assets = $this->userAccountsAssetService->getUserAssets($userId, AccountType::COPY->value);
        
        $totalAssets = 0.0;
        foreach ($assets as $asset) {
            // 计算可用资产和冻结资产的总和
            $totalAssets += ($asset['available'] ?? 0) + ($asset['frozen'] ?? 0);
        }

        return $totalAssets;
    }

    /**
     * 查询最近交易时间
     *
     * @param int $expertId 专家ID
     * @return string|null 最近交易时间
     */
    private function getLastTradeTime(int $expertId): ?string
    {
        $lastOrder = CopyContractOrder::query()
            ->where('expert_id', $expertId)
            ->orderBy('created_at', 'desc')
            ->first();

        return $lastOrder ? $lastOrder->created_at->format('Y-m-d H:i:s') : null;
    }

    /**
     * 统计粉丝数量
     *
     * @param int $expertId 专家ID
     * @return int 粉丝数量
     */
    private function getFanCount(int $expertId): int
    {
        return CopyFollow::query()
            ->where('expert_id', $expertId)
            ->where('type', ExpertType::CONTRACT)
            ->count();
    }
}
