<?php

declare(strict_types=1);
/**
 * 策略平台API
 * 交易专家申请服务类
 */

namespace App\Http\Api\Service\Copy\Expert;

use App\Exception\BusinessException;
use App\Http\Common\ResultCode;
use App\Http\Api\Service\BaseService;
use App\Model\Copy\CopyContractExpert;
use App\Model\Copy\CopyExpertLevel;
use App\Model\Copy\CopyFollow;
use App\Model\Copy\CopySpotExpert;
use App\Model\Copy\Enums\ExpertStatus;
use App\Model\Copy\Enums\ExpertType;
use App\Model\Currency\Currency;
use App\Model\Enums\User\AccountType;
use App\Model\User\Enums\KycStatus;
use App\Model\User\Enums\Status as UserStatus;
use App\Model\User\User;
use App\Model\User\UserKycVerification;
use App\Http\Api\Service\V1\Asset\TransferService;
use App\Model\Copy\CopyContractExpertStatistics;
use App\Model\Copy\CopySpotExpertStatistics;
use App\Model\Copy\CopyContractUserSetting;
use App\Model\Copy\CopySpotUserSetting;
use App\Model\Copy\Enums\CopyStatus;
use App\Model\User\UserAccountsAsset;
use App\Service\UserAccounts\UserAccountsAssetService;
use Hyperf\DbConnection\Db;
use Hyperf\Di\Annotation\Inject;

/**
 * 交易专家申请服务类
 */
class ExpertService extends BaseService
{
    #[Inject]
    protected UserAccountsAssetService $userAccountsAssetService;

    #[Inject]
    protected TransferService $transferService;

    protected int $usdtCurrencyId;

    public function __construct()
    {
        // 获取USDT币种ID
        $usdtCurrency = Currency::where(Currency::FIELD_BASE_ASSET, 'USDT')
            ->where('status', 1)
            ->first();

        if (!$usdtCurrency) {
            throw new BusinessException(ResultCode::FAIL, 'USDT币种配置不存在');
        }

        $this->usdtCurrencyId = $usdtCurrency->id;
    }

    /**
     * 申请成为交易专家
     */
    public function apply(array $data): array
    {
        $userId = $this->userId();
        $expertType = ExpertType::from($data['type']);

        // 通用验证
        $this->validateUserStatus($userId);
        $this->validateKycStatus($userId);
        $this->validateExpertStatus($userId, $expertType);

        return Db::transaction(function () use ($data, $userId, $expertType) {
            if ($expertType === ExpertType::CONTRACT) {
                return $this->applyContractExpert($data, $userId);
            } else {
                return $this->applySpotExpert($data, $userId);
            }
        });
    }

    /**
     * 获取交易专家信息
     */
    public function getExpertInfo(int $type): array
    {
        $userId = $this->userId();
        $expertType = ExpertType::from($type);

        // 获取专家基础信息
        if ($expertType === ExpertType::CONTRACT) {
            $expert = CopyContractExpert::with(['user', 'level'])
                ->where('user_id', $userId)
                ->first();
        } else {
            $expert = CopySpotExpert::with(['user', 'level'])
                ->where('user_id', $userId)
                ->first();
        }

        if (!$expert) {
            throw new BusinessException(ResultCode::NOT_FOUND, '未找到专家信息');
        }

        $expertData = $expert->toArray();

        // 获取关注信息
        $expertData['follow'] = $this->getFollowInfo($userId, $expert->id, $expertType);

        // 获取统计信息
        $expertData['statistics'] = $this->getExpertStatistics($userId, $expert->id, $expertType);

        return $expertData;
    }

    /**
     * 获取关注信息
     */
    private function getFollowInfo(int $userId, int $expertId, ExpertType $expertType): array
    {
        // 获取专家关注的其他专家数量
        $followingCount = CopyFollow::where(CopyFollow::FIELD_USER_ID, $userId)
            ->where(CopyFollow::FIELD_TYPE, $expertType)
            ->count();

        // 获取关注当前专家的粉丝数量
        $followerCount = CopyFollow::where(CopyFollow::FIELD_EXPERT_ID, $expertId)
            ->where(CopyFollow::FIELD_TYPE, $expertType)
            ->count();

        return [
            'follow_count' => $followingCount, // 关注的专家数量
            'fans_count' => $followerCount,   // 粉丝数量
        ];
    }

    /**
     * 获取专家统计信息
     */
    private function getExpertStatistics(int $userId, int $expertId, ExpertType $expertType): array
    {
        // 获取跟单账户余额
        $accountBalance = $this->getAccountBalanceByUserId($userId, $expertType);

        if ($expertType === ExpertType::CONTRACT) {
            $statistics = CopyContractExpertStatistics::where('expert_id', $expertId)->first() ?? new CopyContractExpertStatistics();
        } else {
            $statistics = CopySpotExpertStatistics::where('expert_id', $expertId)->first() ?? new CopySpotExpertStatistics();
        }

        $statistics = array_merge($statistics->toArray(), [
            'account_balance' => $accountBalance,
        ]);

        return $statistics;
    }

    /**
     * 验证用户状态
     */
    private function validateUserStatus(int $userId): void
    {
        $user = User::find($userId);
        if (!$user || $user->status !== UserStatus::NORMAL) {
            throw new BusinessException(ResultCode::FORBIDDEN, '用户状态异常，无法申请');
        }
    }

    /**
     * 验证KYC状态
     */
    private function validateKycStatus(int $userId): void
    {
        $kycVerification = UserKycVerification::where(UserKycVerification::FIELD_USER_ID, $userId)
            ->where(UserKycVerification::FIELD_STATUS, KycStatus::APPROVED)
            ->first();

        if (!$kycVerification) {
            throw new BusinessException(ResultCode::FORBIDDEN, '请先完成 KYC 认证');
        }
    }

    /**
     * 验证专家身份
     */
    private function validateExpertStatus(int $userId, ExpertType $expertType): void
    {
        if ($expertType === ExpertType::CONTRACT) {
            $expert = CopyContractExpert::where('user_id', $userId)->first();
            if ($expert) {
                if ($expert->status === ExpertStatus::APPROVED) {
                    throw new BusinessException(ResultCode::FORBIDDEN, '您已经是合约交易专家');
                } elseif ($expert->status === ExpertStatus::PENDING) {
                    throw new BusinessException(ResultCode::FORBIDDEN, '您有待审核的申请，请耐心等待');
                }
            }
        } else {
            $expert = CopySpotExpert::where('user_id', $userId)->first();
            if ($expert) {
                if ($expert->status === ExpertStatus::APPROVED) {
                    throw new BusinessException(ResultCode::FORBIDDEN, '您已经是现货交易专家');
                } elseif ($expert->status === ExpertStatus::PENDING) {
                    throw new BusinessException(ResultCode::FORBIDDEN, '您有待审核的申请，请耐心等待');
                }
            }
        }
    }

    /**
     * 申请合约交易专家
     */
    private function applyContractExpert(array $data, int $userId): array
    {
        // 验证划转金额
        $minTransferAmountSetting = \Plugin\West\SysSettings\Helper\Helper::getSysSettingByTypeCode('contract_expert_min_transfer');
        $minTransferAmount = $minTransferAmountSetting ? $minTransferAmountSetting->value : '100';
        if (bccomp((string) $data['transfer_amount'], (string) $minTransferAmount, 8) < 0) {
            throw new BusinessException(ResultCode::UNPROCESSABLE_ENTITY, "划转金额不能少于 {$minTransferAmount} USDT");
        }

        // 验证账户余额 - 使用统一的划转服务进行验证和划转
        $transferData = [
            'currency_id' => $this->usdtCurrencyId,
            'from_account_type' => $data['transfer_from_account'],
            'to_account_type' => AccountType::COPY->value,
            'amount' => $data['transfer_amount'],
            'remark' => '申请合约交易专家，添加合约带单资金'
        ];

        // 执行资金划转 - 使用统一的划转服务
        $transferResult = $this->transferService->transfer($transferData);

        if (!$transferResult || empty($transferResult['transfer_id'])) {
            throw new BusinessException(ResultCode::FAIL, '资金划转失败，请稍后重试');
        }

        // 创建专家记录
        $expertData = $this->buildContractExpertData($data, $userId);
        $expert = CopyContractExpert::create($expertData);

        // 更新用户展示名称
        if (!empty($data['display_name'])) {
            User::where('id', $userId)->update(['display_name' => $data['display_name']]);
        }

        // 加载关联数据
        $expert->load(['user', 'level']);

        return $expert->toArray();
    }

    /**
     * 申请现货交易专家
     */
    private function applySpotExpert(array $data, int $userId): array
    {
        // 创建专家记录
        $expertData = $this->buildSpotExpertData($data, $userId);
        $expert = CopySpotExpert::create($expertData);

        // 更新用户展示名称
        if (!empty($data['display_name'])) {
            User::where('id', $userId)->update(['display_name' => $data['display_name']]);
        }

        // 加载关联数据
        $expert->load(['user', 'level']);

        return $expert->toArray();
    }

    /**
     * 构建合约专家数据
     */
    private function buildContractExpertData(array $data, int $userId): array
    {
        $needReviewSetting = \Plugin\West\SysSettings\Helper\Helper::getSysSettingByTypeCode('contract_expert_need_review');
        $needReview = $needReviewSetting ? $needReviewSetting->value : '1';

        $minFollowAmountSetting = \Plugin\West\SysSettings\Helper\Helper::getSysSettingByTypeCode('contract_min_follow_amount');
        $minFollowAmount = $minFollowAmountSetting ? $minFollowAmountSetting->value : '100';

        // 匹配专家等级
        $levelId = null;
        $profitSharingRate = 0;
        $level = $this->matchExpertLevel($userId, ExpertType::CONTRACT, (string) $data['transfer_amount']);
        if ($level) {
            $levelId = $level->id;
            $profitSharingRate = $level->getMaxProfitRate();
        }

        $expertData = [
            'user_id' => $userId,
            'introduction' => $data['introduction'],
            'transfer_from_account' => $data['transfer_from_account'],
            'transfer_amount' => $data['transfer_amount'],
            'status' => $needReview == '1' ? ExpertStatus::PENDING : ExpertStatus::APPROVED,
            'min_follow_amount' => $minFollowAmount,
            'currency_ids' => $this->getAllCurrencyIds(),
            'profit_sharing_rate' => $profitSharingRate,
            'level_id' => $levelId,
        ];

        return $expertData;
    }

    /**
     * 构建现货专家数据
     */
    private function buildSpotExpertData(array $data, int $userId): array
    {
        $needReviewSetting = \Plugin\West\SysSettings\Helper\Helper::getSysSettingByTypeCode('spot_expert_need_review');
        $needReview = $needReviewSetting ? $needReviewSetting->value : '1';

        $minFollowAmountSetting = \Plugin\West\SysSettings\Helper\Helper::getSysSettingByTypeCode('spot_min_follow_amount');
        $minFollowAmount = $minFollowAmountSetting ? $minFollowAmountSetting->value : '100';

        // 匹配专家等级（现货专家初始金额为0）
        $levelId = null;
        $profitSharingRate = 0;
        $level = $this->matchExpertLevel($userId, ExpertType::SPOT);
        if ($level) {
            $levelId = $level->id;
            $profitSharingRate = $level->getMaxProfitRate();
        }

        return [
            'user_id' => $userId,
            'introduction' => $data['introduction'],
            'status' => $needReview == '1' ? ExpertStatus::PENDING : ExpertStatus::APPROVED,
            'min_follow_amount' => $minFollowAmount,
            'currency_ids' => $this->getAllCurrencyIds(),
            'profit_sharing_rate' => $profitSharingRate,
            'level_id' => $levelId,
        ];
    }

    /**
     * 获取所有币种ID（不包含USDT）
     */
    private function getAllCurrencyIds(): array
    {
        return Currency::where('status', 1)->where(Currency::FIELD_BASE_ASSET, '!=', 'USDT')->pluck('id')->toArray();
    }

    /**
     * 匹配专家等级
     * 同时满足条件一和条件二，则返回该等级
     * @param int $userId 用户ID
     * @param ExpertType $expertType 专家类型
     * @param string|null $amount 对应账户资金（合约专家时为合约带单账户资金，现货专家时为现货账户资金）
     * @return CopyExpertLevel|null 返回等级
     */
    public function matchExpertLevel(int $userId, ExpertType $expertType, ?string $amount = null): ?CopyExpertLevel
    {
        $levels = CopyExpertLevel::where('type', $expertType)
            ->orderBy('level', 'desc')
            ->get();

        if ($levels->isEmpty()) {
            return null;
        }

        // 获取专家记录ID（用于查询跟单相关数据）
        if ($expertType === ExpertType::CONTRACT) {
            $expert = CopyContractExpert::where('user_id', $userId)->first();
            $expertId = $expert?->id;

            if (!$amount) {
                $assets = UserAccountsAsset::query()->where(UserAccountsAsset::FIELD_USER_ID, $userId)
                    ->where(UserAccountsAsset::FIELD_CURRENCY_ID, $this->usdtCurrencyId)
                    ->where(UserAccountsAsset::FIELD_ACCOUNT_TYPE, AccountType::COPY)
                    ->first();

                if (!$assets) {
                    $amount = '0';
                } else {
                    $amount = $assets->available;
                }
            }
        } else {
            $expert = CopySpotExpert::where('user_id', $userId)->first();
            $expertId = $expert?->id;

            if (!$amount) {
                $assets = UserAccountsAsset::query()->where(UserAccountsAsset::FIELD_USER_ID, $userId)
                    ->where(UserAccountsAsset::FIELD_CURRENCY_ID, $this->usdtCurrencyId)
                    ->where(UserAccountsAsset::FIELD_ACCOUNT_TYPE, AccountType::SPOT)
                    ->first();

                if (!$assets) {
                    $amount = '0';
                } else {
                    $amount = $assets->available;
                }
            }
        }

        foreach ($levels as $level) {
            // 条件一：带单金额检查
            $conditionOnePass = bccomp((string) $amount, (string) $level->condition_amount, 8) >= 0;

            // 条件二：跟单者相关条件检查（新用户跟单数据为0，正常执行判断逻辑）
            if ($expertType === ExpertType::CONTRACT) {
                $conditionTwoPass = $this->checkContractExpertConditionTwo($expertId, $level);
            } else {
                $conditionTwoPass = $this->checkSpotExpertConditionTwo($expertId, $level);
            }

            // 如果条件一和条件二都满足，返回该等级
            if ($conditionOnePass && $conditionTwoPass) {
                return $level;
            }
        }

        // 如果没有匹配的等级，返回 null
        return null;
    }

    /**
     * 检查合约专家条件二
     */
    private function checkContractExpertConditionTwo(?int $expertId, CopyExpertLevel $level): bool
    {
        // 获取专家统计数据（新用户expertId为null，统计数据为0）
        $followAmount = '0';
        $followCount = 0;

        if ($expertId) {
            $statistics = CopyContractExpertStatistics::where('expert_id', $expertId)->first();
            $followAmount = $statistics?->aum ?? '0';

            // 获取跟单人数
            $followCount = CopyContractUserSetting::where('expert_id', $expertId)
                ->where('status', CopyStatus::FOLLOWING)
                ->count();
        }

        // 条件二：满足任一子条件即可
        $followAmountPass = bccomp((string) $followAmount, (string) $level->condition_follow_amount, 8) >= 0;
        $followCountPass = $followCount >= $level->condition_follow_count;

        return $followAmountPass || $followCountPass;
    }

    /**
     * 检查现货专家条件二
     */
    private function checkSpotExpertConditionTwo(?int $expertId, CopyExpertLevel $level): bool
    {
        // 获取专家统计数据（新用户expertId为null，统计数据为0）
        $followAmount = '0';
        $followCount = 0;

        if ($expertId) {
            $statistics = CopySpotExpertStatistics::where('expert_id', $expertId)->first();
            $followAmount = $statistics?->aum ?? '0';

            // 获取跟单人数
            $followCount = CopySpotUserSetting::where('expert_id', $expertId)
                ->where('status', CopyStatus::FOLLOWING)
                ->count();
        }

        // 条件二：满足任一子条件即可
        $followAmountPass = bccomp((string) $followAmount, (string) $level->condition_follow_amount, 8) >= 0;
        $followCountPass = $followCount >= $level->condition_follow_count;

        return $followAmountPass || $followCountPass;
    }

    /**
     * 获取跟单账户余额（通过专家用户ID、专家类型获取）
     */
    public function getAccountBalanceByUserId(int $userId, ExpertType $expertType): string
    {
        if ($expertType === ExpertType::CONTRACT) {
            $accountType = AccountType::COPY->value;
        } else {
            $accountType = AccountType::SPOT->value;
        }
        $asset = $this->userAccountsAssetService->getUserAsset($userId, $accountType, $this->usdtCurrencyId);

        return $asset ? (string)$asset->available : '0';
    }

    /**
     * 获取跟单账户余额（通过专家实例）
     */
    public function getAccountBalanceByExpert(CopyContractExpert | CopySpotExpert $expert): string
    {
        return $this->getAccountBalanceByUserId($expert->user_id, $expert->type);
    }
}
