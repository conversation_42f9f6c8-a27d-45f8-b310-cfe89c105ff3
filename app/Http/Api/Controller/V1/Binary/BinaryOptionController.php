<?php

declare(strict_types=1);

namespace App\Http\Api\Controller\V1\Binary;

use App\Http\Common\Controller\AbstractController;
use App\Http\Api\Request\V1\Binary\BinaryOptionRequest;
use App\Http\Api\Service\V1\Binary\BinaryOptionService;
use App\Http\Api\Middleware\TokenMiddleware;
use Hyperf\HttpServer\Annotation\Controller;
use Hyperf\HttpServer\Annotation\PostMapping;
use Hyperf\HttpServer\Annotation\GetMapping;
use Hyperf\HttpServer\Annotation\Middleware;

#[Controller(prefix: 'api/v1/binary-option')]
class BinaryOptionController extends AbstractController
{
    /**
     * @var BinaryOptionService $service
     */
    public mixed $service;

    /**
     * @var BinaryOptionRequest $rules
     */
    public mixed $rules;

    /**
     * 获取可用配置列表
     */
    #[GetMapping('configs')]
    #[Middleware(middleware: TokenMiddleware::class)]
    public function getConfigs(): \App\Http\Common\Result
    {
        $this->requestValidate($this->rules->getConfigRules());

        $params = $this->request->all();
        $currencyId = $params['currency_id'] ?? null;

        $result = $this->service->getAvailableConfigs((int)$currencyId);

        return $this->success($result, '获取配置成功');
    }

    /**
     * 下单
     */
    #[PostMapping('place-order')]
    #[Middleware(middleware: TokenMiddleware::class)]
    public function placeOrder(): \App\Http\Common\Result
    {
        $this->requestValidate($this->rules->placeOrderRules());

        $userId = (int)$this->request->getAttribute('user_id');
        $orderData = $this->request->all();

        $result = $this->service->placeOrder($userId, $orderData);

        return $this->success($result, '下单成功');
    }

    /**
     * 获取订单列表
     */
    #[GetMapping('orders')]
    #[Middleware(middleware: TokenMiddleware::class)]
    public function getOrders(): \App\Http\Common\Result
    {
        $this->requestValidate($this->rules->orderListRules());

        $userId = (int)$this->request->getAttribute('user_id');
        $params = $this->request->all();

        $result = $this->service->getUserOrders($userId, $params);

        return $this->success($result, '获取订单列表成功');
    }

    /**
     * 获取订单详情
     */
    #[GetMapping('order/{id}')]
    #[Middleware(middleware: TokenMiddleware::class)]
    public function getOrderDetail(): \App\Http\Common\Result
    {
        $userId = (int)$this->request->getAttribute('user_id');
        $orderId = (int)$this->request->route('id');

        $result = $this->service->getOrderDetail($userId, $orderId);

        return $this->success($result, '获取订单详情成功');
    }

    /**
     * 取消订单
     */
    #[PostMapping('cancel-order')]
    #[Middleware(middleware: TokenMiddleware::class)]
    public function cancelOrder(): \App\Http\Common\Result
    {
        $this->requestValidate($this->rules->cancelOrderRules());

        $userId = (int)$this->request->getAttribute('user_id');
        $orderId = (int)$this->request->input('order_id');

        $result = $this->service->cancelOrder($userId, $orderId);

        return $this->success($result, '取消订单成功');
    }

    /**
     * 获取统计数据
     */
    #[GetMapping('statistics')]
    #[Middleware(middleware: TokenMiddleware::class)]
    public function getStatistics(): \App\Http\Common\Result
    {
        $this->requestValidate($this->rules->statisticsRules());

        $userId = (int)$this->request->getAttribute('user_id');
        $params = $this->request->all();

        $result = $this->service->getUserStatistics($userId, $params);

        return $this->success($result, '获取统计数据成功');
    }
}
