<?php

declare(strict_types=1);
/**
 * 策略平台API
 * 交易专家控制器
 */

namespace App\Http\Api\Controller\Copy\Expert;

use App\Http\Api\Middleware\TokenMiddleware;
use App\Http\Api\Request\Copy\Expert\ExpertApplicationRequest;
use App\Http\Api\Service\Copy\Expert\ExpertService;
use App\Http\Common\Controller\AbstractController;
use App\Http\Common\Result;
use Hyperf\Di\Annotation\Inject;
use Hyperf\HttpServer\Annotation\Controller;
use Hyperf\HttpServer\Annotation\GetMapping;
use Hyperf\HttpServer\Annotation\Middleware;
use Hyperf\HttpServer\Annotation\PostMapping;

/**
 * 交易专家控制器
 */
#[Controller(prefix: "api/copy/expert")]
#[Middleware(TokenMiddleware::class)]
class ExpertController extends AbstractController
{
    #[Inject]
    protected ExpertService $expertService;

    /**
     * 申请成为交易专家
     */
    #[PostMapping("apply")]
    public function apply(ExpertApplicationRequest $request): Result
    {
        $result = $this->expertService->apply($request->all());
        return $this->success($result);
    }

    /**
     * 获取交易专家信息
     */
    #[GetMapping("info/{type}")]
    public function info(int $type): Result
    {
        $result = $this->expertService->getExpertInfo($type);
        return $this->success($result);
    }
}
