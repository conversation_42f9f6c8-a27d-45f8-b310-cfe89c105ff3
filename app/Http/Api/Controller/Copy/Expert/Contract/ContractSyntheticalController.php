<?php

declare(strict_types=1);
/**
 * 策略平台API
 * 合约专家综合数据控制器
 */

namespace App\Http\Api\Controller\Copy\Expert\Contract;

use App\Http\Api\Middleware\ContractExpertMiddleware;
use App\Http\Api\Middleware\TokenMiddleware;
use App\Http\Api\Service\Copy\Expert\Contract\ContractSyntheticalService;
use App\Http\Common\Controller\AbstractController;
use App\Http\Common\Result;
use Hyperf\Di\Annotation\Inject;
use Hyperf\HttpServer\Annotation\Controller;
use Hyperf\HttpServer\Annotation\GetMapping;
use Hyperf\HttpServer\Annotation\Middleware;

/**
 * 合约专家综合数据控制器
 */
#[Controller(prefix: 'api/copy/expert/contract')]
#[Middleware(middleware: TokenMiddleware::class, priority: 100)]
#[Middleware(middleware: ContractExpertMiddleware::class, priority: 99)]
class ContractSyntheticalController extends AbstractController
{
    #[Inject]
    private readonly ContractSyntheticalService $contractSyntheticalService;

    /**
     * 合约-综合数据接口
     */
    #[GetMapping('synthetical')]
    public function synthetical(): Result
    {
        return $this->success(
            $this->contractSyntheticalService->getSyntheticalData()
        );
    }
}
