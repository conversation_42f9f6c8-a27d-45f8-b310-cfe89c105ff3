<?php

declare(strict_types=1);
/**
 * 策略平台API
 * 合约专家统计控制器
 */

namespace App\Http\Api\Controller\Copy\Expert\Contract;

use App\Http\Api\Middleware\ContractExpertMiddleware;
use App\Http\Api\Middleware\TokenMiddleware;
use App\Http\Api\Request\Copy\Expert\Contract\ContractStatisticsRequest;
use App\Http\Api\Service\Copy\Expert\Contract\ContractStatisticsService;
use App\Http\Common\Controller\AbstractController;
use App\Http\Common\Result;
use Hyperf\Di\Annotation\Inject;
use Hyperf\HttpServer\Annotation\Controller;
use Hyperf\HttpServer\Annotation\GetMapping;
use Hyperf\HttpServer\Annotation\Middleware;

/**
 * 合约专家统计控制器
 */
#[Controller(prefix: 'api/copy/expert/contract')]
#[Middleware(middleware: TokenMiddleware::class, priority: 100)]
#[Middleware(middleware: ContractExpertMiddleware::class, priority: 99)]
class ContractStatisticsController extends AbstractController
{
    #[Inject]
    private readonly ContractStatisticsService $contractStatisticsService;

    /**
     * 合约-带单数据统计接口
     */
    #[GetMapping('statistics')]
    public function statistics(ContractStatisticsRequest $request): Result
    {
        return $this->success(
            $this->contractStatisticsService->statistics($request->all())
        );
    }
}
