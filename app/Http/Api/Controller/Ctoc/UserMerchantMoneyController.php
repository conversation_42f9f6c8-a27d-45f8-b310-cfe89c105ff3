<?php

declare(strict_types=1);

/**
 * UserMerchantMoneyController
 * Author:XTeam
 * Contact:tg:@chenmaq
 * Version:1.0
 * Date:2025-07-20
 * Website:xxx
 */

namespace App\Http\Api\Controller\Ctoc;

use App\Http\Common\Result;
use Hyperf\HttpServer\Annotation\Middleware;
use App\Http\Api\Middleware\TokenMiddleware;
use App\Http\Common\Controller\AbstractController;
use Hyperf\HttpServer\Annotation\Controller;
use Hyperf\HttpServer\Annotation\GetMapping;
// use Hyperf\HttpServer\Annotation\PostMapping;
// use Hyperf\HttpServer\Annotation\PutMapping;
// use Hyperf\HttpServer\Annotation\DeleteMapping;
use Hyperf\Di\Annotation\Inject;
use App\Http\Api\Request\Ctoc\UserMerchantMoneyRequest;
use \App\Http\Api\Service\Ctoc\UserMerchantMoneyService;

/**
 * c2c用户商户保证金
 */
#[Controller(prefix: "api/ctoc/userMerchantMoney")]
#[Middleware(TokenMiddleware::class)]
class UserMerchantMoneyController extends AbstractController
{
    #[Inject]
    protected UserMerchantMoneyService $userMerchantMoneyService;

    /**
     * 说明：缴纳保证金
     * @return Result
     * @throws \Psr\Container\ContainerExceptionInterface
     * @throws \Psr\Container\NotFoundExceptionInterface
     */
    #[GetMapping("cautionMoney")]
    public function cautionMoney(UserMerchantMoneyRequest $request): Result
    {
        $result = $this->userMerchantMoneyService->cautionMoney($request);
        return $this->success($result);
    }
}
