<?php

declare(strict_types=1);

/**
 * UserPaymentController
 * Author:XTeam
 * Contact:tg:@chenmaq
 * Version:1.0
 * Date:2025-07-18
 * Website:xxx
 */

namespace App\Http\Api\Controller\Ctoc;

use App\Http\Common\Result;
use Hyperf\HttpServer\Annotation\Middleware;
use App\Http\Api\Middleware\TokenMiddleware;
use App\Http\Common\Controller\AbstractController;
use Hyperf\HttpServer\Annotation\Controller;
use Hyperf\HttpServer\Annotation\GetMapping;
// use Hyperf\HttpServer\Annotation\PostMapping;
// use Hyperf\HttpServer\Annotation\PutMapping;
// use Hyperf\HttpServer\Annotation\DeleteMapping;
use Hyperf\Di\Annotation\Inject;
use App\Http\Api\Request\Ctoc\UserPaymentRequest;
use \App\Http\Api\Service\Ctoc\UserPaymentService;
use Hyperf\HttpServer\Annotation\PostMapping;

/**
 * 用户支付账号管理
 */
#[Controller(prefix: "api/ctoc/userPayment")]
#[Middleware(TokenMiddleware::class)]
class UserPaymentController extends AbstractController
{
    #[Inject]
    protected UserPaymentService $userPaymentService;

    /**
     * 说明：通过支付方式 查询用户的支付账号
     * @return Result
     * @throws \Psr\Container\ContainerExceptionInterface
     * @throws \Psr\Container\NotFoundExceptionInterface
     */
    #[GetMapping("list")]
    public function list(UserPaymentRequest $request): Result
    {
        $result = $this->userPaymentService->list($request);
        return $this->success($result);
    }

    /**
     * 说明：创建用户的支付账号
     * @return Result
     * @throws \Psr\Container\ContainerExceptionInterface
     * @throws \Psr\Container\NotFoundExceptionInterface
     */
    #[PostMapping("create")]
    public function create(UserPaymentRequest $request): Result
    {
        $result = $this->userPaymentService->create($request);
        return $this->success($result);
    }

    /**
     * 说明：修改用户的支付账号
     * @return Result
     * @throws \Psr\Container\ContainerExceptionInterface
     * @throws \Psr\Container\NotFoundExceptionInterface
     */
    #[PostMapping("update")]
    public function update(UserPaymentRequest $request): Result
    {
        $result = $this->userPaymentService->update($request);
        return $this->success($result);
    }

    /**
     * 说明：修改用户的支付账号
     * @return Result
     * @throws \Psr\Container\ContainerExceptionInterface
     * @throws \Psr\Container\NotFoundExceptionInterface
     */
    #[PostMapping("delete")]
    public function delete(UserPaymentRequest $request): Result
    {
        $result = $this->userPaymentService->delete($request);
        return $this->success($result);
    }


}
