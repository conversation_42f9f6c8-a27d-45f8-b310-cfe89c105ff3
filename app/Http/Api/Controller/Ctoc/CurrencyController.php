<?php

declare(strict_types=1);

/**
 * CurrencyController
 * Author:XTeam
 * Contact:tg:@chenmaq
 * Version:1.0
 * Date:2025-07-18
 * Website:xxx
 */

namespace App\Http\Api\Controller\Ctoc;

use App\Http\Common\Result;
use Hyperf\HttpServer\Annotation\Middleware;
use App\Http\Api\Middleware\TokenMiddleware;
use App\Http\Common\Controller\AbstractController;
use Hyperf\HttpServer\Annotation\Controller;
use Hyperf\HttpServer\Annotation\GetMapping;
// use Hyperf\HttpServer\Annotation\PostMapping;
// use Hyperf\HttpServer\Annotation\PutMapping;
// use Hyperf\HttpServer\Annotation\DeleteMapping;
use Hyperf\Di\Annotation\Inject;
use App\Http\Api\Request\Ctoc\CurrencyRequest;
use \App\Http\Api\Service\Ctoc\CurrencyService;

/** 
 * 币种管理
 */
#[Controller(prefix: "api/ctoc/currency")]
class CurrencyController extends AbstractController
{
    #[Inject]
    protected CurrencyService $currencyService;

    /**
     * 说明：
     * @return Result
     * @throws \Psr\Container\ContainerExceptionInterface
     * @throws \Psr\Container\NotFoundExceptionInterface
     */
    #[GetMapping("list")]
    public function list(CurrencyRequest $request): Result
    {
        $result = $this->currencyService->list($request);
        return $this->success($result);
    }
}
