<?php

declare(strict_types=1);

/**
 * BankController
 * Author:XTeam
 * Contact:tg:@chenmaq
 * Version:1.0
 * Date:2025-07-18
 * Website:xxx
 */

namespace App\Http\Api\Controller\Ctoc;

use App\Http\Common\Result;
use Hyperf\HttpServer\Annotation\Middleware;
use App\Http\Api\Middleware\TokenMiddleware;
use App\Http\Common\Controller\AbstractController;
use Hyperf\HttpServer\Annotation\Controller;
use Hyperf\HttpServer\Annotation\GetMapping;
// use Hyperf\HttpServer\Annotation\PostMapping;
// use Hyperf\HttpServer\Annotation\PutMapping;
// use Hyperf\HttpServer\Annotation\DeleteMapping;
use Hyperf\Di\Annotation\Inject;
use App\Http\Api\Request\Ctoc\BankRequest;
use \App\Http\Api\Service\Ctoc\BankService;

/** 
 * 银行管理
 */
#[Controller(prefix: "api/ctoc/bank")]
class BankController extends AbstractController
{
    #[Inject]
    protected BankService $bankService;

    /**
     * 说明：
     * @return Result
     * @throws \Psr\Container\ContainerExceptionInterface
     * @throws \Psr\Container\NotFoundExceptionInterface
     */
    #[GetMapping("list")]
    public function list(BankRequest $request): Result
    {
        $result = $this->bankService->list($request);
        return $this->success($result);
    }
}
