<?php

declare(strict_types=1);

/**
 * FlatCurrencyController
 * Author:XTeam
 * Contact:tg:@chenmaq
 * Version:1.0
 * Date:2025-07-17
 * Website:xxx
 */

namespace App\Http\Api\Controller\Ctoc;

use App\Http\Common\Result;
use Hyperf\HttpServer\Annotation\Middleware;
use App\Http\Api\Middleware\TokenMiddleware;
use App\Http\Common\Controller\AbstractController;
use Hyperf\HttpServer\Annotation\Controller;
use Hyperf\HttpServer\Annotation\GetMapping;
// use Hyperf\HttpServer\Annotation\PostMapping;
// use Hyperf\HttpServer\Annotation\PutMapping;
// use Hyperf\HttpServer\Annotation\DeleteMapping;
use Hyperf\Di\Annotation\Inject;
use App\Http\Api\Request\Ctoc\FlatCurrencyRequest;
use \App\Http\Api\Service\Ctoc\FlatCurrencyService;

/**
 * 法币接口.
 */
#[Controller(prefix: "api/ctoc/flatCurrency")]
class FlatCurrencyController extends AbstractController
{
    #[Inject]
    protected FlatCurrencyService $flatCurrencyService;

    /**
     * 说明：
     * @return Result
     * @throws \Psr\Container\ContainerExceptionInterface
     * @throws \Psr\Container\NotFoundExceptionInterface
     */
    #[GetMapping("list")]
    public function list(FlatCurrencyRequest $request): Result
    {
        $result = $this->flatCurrencyService->list($request);
        return $this->success($result);
    }
}
