<?php

declare(strict_types=1);

/**
 * UserMerchantController
 * Author:XTeam
 * Contact:tg:@chenmaq
 * Version:1.0
 * Date:2025-07-20
 * Website:xxx
 */

namespace App\Http\Api\Controller\Ctoc;

use App\Http\Common\Result;
use Hyperf\HttpServer\Annotation\Middleware;
use App\Http\Api\Middleware\TokenMiddleware;
use App\Http\Common\Controller\AbstractController;
use Hyperf\HttpServer\Annotation\Controller;
use Hyperf\HttpServer\Annotation\GetMapping;
use Hyperf\Di\Annotation\Inject;
use App\Http\Api\Request\Ctoc\UserMerchantRequest;
use \App\Http\Api\Service\Ctoc\UserMerchantService;
use Hyperf\HttpServer\Annotation\PostMapping;

/**
 * c2c用户商户申请
 */
#[Controller(prefix: "api/ctoc/userMerchant")]
#[Middleware(TokenMiddleware::class)]
class UserMerchantController extends AbstractController
{
    #[Inject]
    protected UserMerchantService $userMerchantService;

    /**
     * 说明：申请商家
     * @return Result
     * @throws \Psr\Container\ContainerExceptionInterface
     * @throws \Psr\Container\NotFoundExceptionInterface
     */
    #[PostMapping("create")]
    public function create(UserMerchantRequest $request): Result
    {
        $result = $this->userMerchantService->create($request);
        return $this->success($result);
    }

    #[GetMapping("detailByUserId")]
    public function detailByUserId(UserMerchantRequest $request): Result
    {
        $result = $this->userMerchantService->detailByUserId($request);
        return $this->success($result);
    }
}
