<?php

declare(strict_types=1);
/**
 * 策略平台API
 * 现货交易专家中间件
 */

namespace App\Http\Api\Middleware;

use App\Exception\BusinessException;
use App\Http\Common\ResultCode;
use App\Model\Copy\CopySpotExpert;
use App\Model\Copy\Enums\ExpertStatus;
use Hyperf\HttpServer\Contract\RequestInterface;
use Psr\Http\Message\ResponseInterface;
use Psr\Http\Message\ServerRequestInterface;
use Psr\Http\Server\MiddlewareInterface;
use Psr\Http\Server\RequestHandlerInterface;

/**
 * 现货交易专家中间件
 * 验证当前用户是否为现货交易专家
 */
class SpotExpertMiddleware implements MiddlewareInterface
{
    public function __construct(
        protected RequestInterface $request
    ) {}

    public function process(ServerRequestInterface $request, RequestHandlerInterface $handler): ResponseInterface
    {
        $userId = $request->getAttribute('user_id');

        if (!$userId) {
            throw new BusinessException(ResultCode::UNAUTHORIZED, '用户未登录');
        }

        // 查询用户是否为现货交易专家
        $expert = CopySpotExpert::query()
            ->where('user_id', $userId)
            ->where('status', ExpertStatus::APPROVED)
            ->first();

        if (!$expert) {
            throw new BusinessException(ResultCode::FORBIDDEN, '需要现货交易专家身份');
        }

        // 将专家信息添加到请求属性中
        $request = $request->withAttribute('spot_expert', $expert);

        return $handler->handle($request);
    }
}
