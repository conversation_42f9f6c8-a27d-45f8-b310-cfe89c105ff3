<?php

declare(strict_types=1);
namespace App\Http\Admin\Request\Ctoc;

use App\Http\Common\Request\Traits\ActionRulesTrait;
use Hyperf\Validation\Request\FormRequest;

class UserMerchantMoneyRequest extends FormRequest
{
    use ActionRulesTrait;

    public function authorize(): bool
    {
        return true;
    }

    // 自动匹配create方法验证
    public function createRules(): array
    {
        return [
            'user_id' => ['integer'],
            'order_sn' => ['sometimes'],
            'money' => ['numeric', 'min:0'],
            'status' => ['integer'],
            'created_by' => ['integer'],
            'updated_by' => ['integer'],
        ];
    }

    // 自动匹配save方法验证
    public function saveRules(): array
    {
        return [
            'user_id' => ['integer'],
            'order_sn' => ['sometimes'],
            'money' => ['numeric', 'min:0'],
            'status' => ['integer'],
            'created_by' => ['integer'],
            'updated_by' => ['integer'],
        ];
    }

    public function attributes(): array
    {
        return [
            'user_id' => trans('user_merchant_money.user_id') ?: '用户ID（关联cpx_user用户表）',
            'order_sn' => trans('user_merchant_money.order_sn') ?: '订单号',
            'money' => trans('user_merchant_money.money') ?: '保证金',
            'status' => trans('user_merchant_money.status') ?: '审核状态',
            'created_by' => trans('user_merchant_money.created_by') ?: '创建者',
            'updated_by' => trans('user_merchant_money.updated_by') ?: '更新者',
        ];
    }

    /**
     * 获取验证错误的自定义消息.
     */
    public function messages(): array
    {
        return [
            // 可以在这里添加自定义的错误消息
        ];
    }
}