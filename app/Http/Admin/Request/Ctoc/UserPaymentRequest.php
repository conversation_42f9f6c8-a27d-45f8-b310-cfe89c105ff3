<?php

declare(strict_types=1);
namespace App\Http\Admin\Request\Ctoc;

use App\Http\Common\Request\Traits\ActionRulesTrait;
use Hyperf\Validation\Request\FormRequest;

class UserPaymentRequest extends FormRequest
{
    use ActionRulesTrait;

    public function authorize(): bool
    {
        return true;
    }

    // 自动匹配create方法验证
    public function createRules(): array
    {
        return [
            'user_id' => ['integer'],
            'flat_currency_id' => ['integer'],
            'payment_type' => ['sometimes'],
            'account_info' => ['array'],
            'created_by' => ['integer'],
            'updated_by' => ['integer'],
        ];
    }

    // 自动匹配save方法验证
    public function saveRules(): array
    {
        return [
            'user_id' => ['integer'],
            'flat_currency_id' => ['integer'],
            'payment_type' => ['sometimes'],
            'account_info' => ['array'],
            'created_by' => ['integer'],
            'updated_by' => ['integer'],
        ];
    }

    public function attributes(): array
    {
        return [
            'user_id' => trans('user_payment.user_id') ?: '用户ID',
            'flat_currency_id' => trans('user_payment.flat_currency_id') ?: '法币ID',
            'payment_type' => trans('user_payment.payment_type') ?: '支付方式 如 wechat',
            'account_info' => trans('user_payment.account_info') ?: '账号信息json',
            'created_by' => trans('user_payment.created_by') ?: '创建者',
            'updated_by' => trans('user_payment.updated_by') ?: '更新者',
        ];
    }

    /**
     * 获取验证错误的自定义消息.
     */
    public function messages(): array
    {
        return [
            // 可以在这里添加自定义的错误消息
        ];
    }
}