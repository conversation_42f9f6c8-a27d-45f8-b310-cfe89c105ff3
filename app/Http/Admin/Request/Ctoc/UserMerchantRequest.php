<?php

declare(strict_types=1);
namespace App\Http\Admin\Request\Ctoc;

use App\Http\Common\Request\Traits\ActionRulesTrait;
use Hyperf\Validation\Request\FormRequest;

class UserMerchantRequest extends FormRequest
{
    use ActionRulesTrait;

    public function authorize(): bool
    {
        return true;
    }

    // 自动匹配create方法验证
    public function createRules(): array
    {
        return [
            'user_id' => ['integer'],
            'user_name' => ['sometimes'],
            'id_card' => ['sometimes'],
            'id_card_front' => ['sometimes'],
            'id_card_back' => ['sometimes'],
            'store_name' => ['sometimes'],
            'store_logo' => ['sometimes'],
            'contact_name' => ['sometimes'],
            'contact_phone' => ['regex:/^1[3456789]\d{9}$/'],
            'contact_email' => ['email'],
            'status' => ['integer'],
            'remark' => ['sometimes'],
            'created_by' => ['integer'],
            'updated_by' => ['integer'],
        ];
    }

    // 自动匹配save方法验证
    public function saveRules(): array
    {
        return [
            'user_id' => ['integer'],
            'user_name' => ['sometimes'],
            'id_card' => ['sometimes'],
            'id_card_front' => ['sometimes'],
            'id_card_back' => ['sometimes'],
            'store_name' => ['sometimes'],
            'store_logo' => ['sometimes'],
            'contact_name' => ['sometimes'],
            'contact_phone' => ['regex:/^1[3456789]\d{9}$/'],
            'contact_email' => ['email'],
            'status' => ['integer'],
            'remark' => ['sometimes'],
            'created_by' => ['integer'],
            'updated_by' => ['integer'],
        ];
    }

    public function attributes(): array
    {
        return [
            'user_id' => trans('user_merchant.user_id') ?: '用户ID（关联cpx_user用户表）',
            'user_name' => trans('user_merchant.user_name') ?: '姓名',
            'id_card' => trans('user_merchant.id_card') ?: '身份证号',
            'id_card_front' => trans('user_merchant.id_card_front') ?: '身份证正面图片',
            'id_card_back' => trans('user_merchant.id_card_back') ?: '身份证反面图片',
            'store_name' => trans('user_merchant.store_name') ?: '店铺名称',
            'store_logo' => trans('user_merchant.store_logo') ?: '店铺Logo图片',
            'contact_name' => trans('user_merchant.contact_name') ?: '联系人姓名',
            'contact_phone' => trans('user_merchant.contact_phone') ?: '联系电话',
            'contact_email' => trans('user_merchant.contact_email') ?: '联系邮箱',
            'status' => trans('user_merchant.status') ?: '审核状态',
            'remark' => trans('user_merchant.remark') ?: '审核备注',
            'created_by' => trans('user_merchant.created_by') ?: '创建者',
            'updated_by' => trans('user_merchant.updated_by') ?: '更新者',
        ];
    }

    /**
     * 获取验证错误的自定义消息.
     */
    public function messages(): array
    {
        return [
            // 可以在这里添加自定义的错误消息
        ];
    }
}