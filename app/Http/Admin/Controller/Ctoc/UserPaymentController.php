<?php

declare(strict_types=1);
/**
 * This file is part of MineAdmin.
 *
 * @link     https://www.mineadmin.com
 * @document https://doc.mineadmin.com
 * @contact  <EMAIL>
 * @license  https://github.com/mineadmin/MineAdmin/blob/master/LICENSE
 */

namespace App\Http\Admin\Controller\Ctoc;

use App\Http\Admin\Controller\AbstractController;
use App\Http\Admin\Middleware\PermissionMiddleware;
use App\Http\Admin\Request\Ctoc\UserPaymentRequest as Request;
use App\Http\Common\Middleware\AccessTokenMiddleware;
use App\Http\Common\Middleware\OperationMiddleware;
use App\Http\Common\Result;
use App\Http\CurrentUser;
use App\Http\Admin\Service\Ctoc\UserPaymentService;
use Hyperf\Di\Annotation\Inject;
use Hyperf\HttpServer\Annotation\Middleware;
use Hyperf\Swagger\Annotation as OA;
use Hyperf\Swagger\Annotation\Delete;
use Hyperf\Swagger\Annotation\Get;
use Hyperf\Swagger\Annotation\Post;
use Hyperf\Swagger\Annotation\Put;
use Mine\Access\Attribute\Permission;
use Mine\Swagger\Attributes\ResultResponse;


#[OA\Tag('用户支付账号')]
#[OA\HyperfServer('http')]
#[Middleware(middleware: AccessTokenMiddleware::class, priority: 100)]
#[Middleware(middleware: PermissionMiddleware::class, priority: 99)]
#[Middleware(middleware: OperationMiddleware::class, priority: 98)]
class UserPaymentController extends AbstractController
{
    public function __construct(
        private readonly CurrentUser $currentUser
    ) {}

    #[Inject]
    protected UserPaymentService $userPaymentService;

    #[Get(
        path: '/admin/ctoc/user_payment/list',
        operationId: 'ctoc:user_payment:list',
        summary: '用户支付账号列表',
        security: [['Bearer' => [], 'ApiKey' => []]],
        tags: ['用户支付账号'],
    )]
    #[Permission(code: 'ctoc:user_payment:list')]
    #[ResultResponse(instance: new Result())]
    public function pageList(): Result
    {
        return $this->success(
            $this->userPaymentService->page(
                $this->getRequestData(),
                $this->getCurrentPage(),
                $this->getPageSize()
            )
        );
    }

    #[Post(
        path: '/admin/ctoc/user_payment/create',
        operationId: 'ctoc:user_payment:create',
        summary: '用户支付账号新增',
        security: [['Bearer' => [], 'ApiKey' => []]],
        tags: ['用户支付账号'],
    )]
    #[Permission(code: 'ctoc:user_payment:create')]
    #[ResultResponse(instance: new Result())]
    public function create(Request $request): Result
    {
        $this->userPaymentService->create(array_merge($request->all(), [
            'created_by' => $this->currentUser->id(),
        ]));
        return $this->success();
    }

    #[Put(
        path: '/admin/ctoc/user_payment/save/{id}',
        operationId: 'ctoc:user_payment:save',
        summary: '用户支付账号保存',
        security: [['Bearer' => [], 'ApiKey' => []]],
        tags: ['用户支付账号'],
    )]
    #[Permission(code: 'ctoc:user_payment:save')]
    #[ResultResponse(instance: new Result())]
    public function save(int $id, Request $request): Result
    {
        $this->userPaymentService->updateById($id, array_merge($request->all(), [
            'updated_by' => $this->currentUser->id(),
        ]));
        return $this->success();
    }

    #[Delete(
        path: '/admin/ctoc/user_payment/delete',
        operationId: 'ctoc:user_payment:delete',
        summary: '用户支付账号删除',
        security: [['Bearer' => [], 'ApiKey' => []]],
        tags: ['用户支付账号'],
    )]
    #[ResultResponse(instance: new Result())]
    #[Permission(code: 'ctoc:user_payment:delete')]
    public function delete(): Result
    {
        $this->userPaymentService->deleteById($this->getRequestData());
        return $this->success();
    }

}
