<?php

declare(strict_types=1);
/**
 * This file is part of MineAdmin.
 *
 * @link     https://www.mineadmin.com
 * @document https://doc.mineadmin.com
 * @contact  <EMAIL>
 * @license  https://github.com/mineadmin/MineAdmin/blob/master/LICENSE
 */

namespace App\Http\Admin\Controller\Ctoc;

use App\Http\Admin\Controller\AbstractController;
use App\Http\Admin\Middleware\PermissionMiddleware;
use App\Http\Admin\Request\Ctoc\UserMerchantMoneyRequest as Request;
use App\Http\Admin\Service\Ctoc\UserMerchantMoneyService;
use App\Http\Common\Middleware\AccessTokenMiddleware;
use App\Http\Common\Middleware\OperationMiddleware;
use App\Http\Common\Result;
use App\Http\CurrentUser;
use Hyperf\Di\Annotation\Inject;
use Hyperf\HttpServer\Annotation\Middleware;
use Hyperf\Swagger\Annotation as OA;
use Hyperf\Swagger\Annotation\Delete;
use Hyperf\Swagger\Annotation\Get;
use Hyperf\Swagger\Annotation\Post;
use Hyperf\Swagger\Annotation\Put;
use Mine\Access\Attribute\Permission;
use Mine\Swagger\Attributes\ResultResponse;


#[OA\Tag('c2c保证金')]
#[OA\HyperfServer('http')]
#[Middleware(middleware: AccessTokenMiddleware::class, priority: 100)]
#[Middleware(middleware: PermissionMiddleware::class, priority: 99)]
#[Middleware(middleware: OperationMiddleware::class, priority: 98)]
class UserMerchantMoneyController extends AbstractController
{
    public function __construct(
        private readonly CurrentUser $currentUser
    ) {}

    #[Inject]
    protected UserMerchantMoneyService $userMerchantMoneyService;

    #[Get(
        path: '/admin/ctoc/user_merchant_money/list',
        operationId: 'ctoc:user_merchant_money:list',
        summary: 'c2c保证金列表',
        security: [['Bearer' => [], 'ApiKey' => []]],
        tags: ['c2c保证金'],
    )]
    #[Permission(code: 'ctoc:user_merchant_money:list')]
    #[ResultResponse(instance: new Result())]
    public function pageList(): Result
    {
        return $this->success(
            $this->userMerchantMoneyService->page(
                $this->getRequestData(),
                $this->getCurrentPage(),
                $this->getPageSize()
            )
        );
    }

    #[Post(
        path: '/admin/ctoc/user_merchant_money/create',
        operationId: 'ctoc:user_merchant_money:create',
        summary: 'c2c保证金新增',
        security: [['Bearer' => [], 'ApiKey' => []]],
        tags: ['c2c保证金'],
    )]
    #[Permission(code: 'ctoc:user_merchant_money:create')]
    #[ResultResponse(instance: new Result())]
    public function create(Request $request): Result
    {
        $this->userMerchantMoneyService->create(array_merge($request->all(), [
            'created_by' => $this->currentUser->id(),
        ]));
        return $this->success();
    }

    #[Put(
        path: '/admin/ctoc/user_merchant_money/save/{id}',
        operationId: 'ctoc:user_merchant_money:save',
        summary: 'c2c保证金保存',
        security: [['Bearer' => [], 'ApiKey' => []]],
        tags: ['c2c保证金'],
    )]
    #[Permission(code: 'ctoc:user_merchant_money:save')]
    #[ResultResponse(instance: new Result())]
    public function save(int $id, Request $request): Result
    {
        $this->userMerchantMoneyService->updateById($id, array_merge($request->all(), [
            'updated_by' => $this->currentUser->id(),
        ]));
        return $this->success();
    }

    #[Delete(
        path: '/admin/ctoc/user_merchant_money/delete',
        operationId: 'ctoc:user_merchant_money:delete',
        summary: 'c2c保证金删除',
        security: [['Bearer' => [], 'ApiKey' => []]],
        tags: ['c2c保证金'],
    )]
    #[ResultResponse(instance: new Result())]
    #[Permission(code: 'ctoc:user_merchant_money:delete')]
    public function delete(): Result
    {
        $this->userMerchantMoneyService->deleteById($this->getRequestData());
        return $this->success();
    }

}
