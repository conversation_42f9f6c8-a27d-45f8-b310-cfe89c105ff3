<?php

declare(strict_types=1);
/**
 * This file is part of MineAdmin.
 *
 * @link     https://www.mineadmin.com
 * @document https://doc.mineadmin.com
 * @contact  <EMAIL>
 * @license  https://github.com/mineadmin/MineAdmin/blob/master/LICENSE
 */

namespace App\Http\Admin\Controller\Ctoc;

use App\Http\Admin\Controller\AbstractController;
use App\Http\Admin\Middleware\PermissionMiddleware;
use App\Http\Admin\Request\Ctoc\UserMerchantRequest as Request;
use App\Http\Common\Middleware\AccessTokenMiddleware;
use App\Http\Common\Middleware\OperationMiddleware;
use App\Http\Common\Result;
use App\Http\CurrentUser;
use App\Http\Admin\Service\Ctoc\UserMerchantService;
use Hyperf\Di\Annotation\Inject;
use Hyperf\HttpServer\Annotation\Middleware;
use Hyperf\Swagger\Annotation as OA;
use Hyperf\Swagger\Annotation\Delete;
use Hyperf\Swagger\Annotation\Get;
use Hyperf\Swagger\Annotation\Post;
use Hyperf\Swagger\Annotation\Put;
use Mine\Access\Attribute\Permission;
use Mine\Swagger\Attributes\ResultResponse;


#[OA\Tag('c2c商家申请')]
#[OA\HyperfServer('http')]
#[Middleware(middleware: AccessTokenMiddleware::class, priority: 100)]
#[Middleware(middleware: PermissionMiddleware::class, priority: 99)]
#[Middleware(middleware: OperationMiddleware::class, priority: 98)]
class UserMerchantController extends AbstractController
{
    public function __construct(
        private readonly CurrentUser $currentUser
    ) {}

    #[Inject]
    protected UserMerchantService $userMerchantService;

    #[Get(
        path: '/admin/ctoc/user_merchant/list',
        operationId: 'ctoc:user_merchant:list',
        summary: 'c2c商家申请列表',
        security: [['Bearer' => [], 'ApiKey' => []]],
        tags: ['c2c商家申请'],
    )]
    #[Permission(code: 'ctoc:user_merchant:list')]
    #[ResultResponse(instance: new Result())]
    public function pageList(): Result
    {
        return $this->success(
            $this->userMerchantService->page(
                $this->getRequestData(),
                $this->getCurrentPage(),
                $this->getPageSize()
            )
        );
    }

    #[Post(
        path: '/admin/ctoc/user_merchant/create',
        operationId: 'ctoc:user_merchant:create',
        summary: 'c2c商家申请新增',
        security: [['Bearer' => [], 'ApiKey' => []]],
        tags: ['c2c商家申请'],
    )]
    #[Permission(code: 'ctoc:user_merchant:create')]
    #[ResultResponse(instance: new Result())]
    public function create(Request $request): Result
    {
        $this->userMerchantService->create(array_merge($request->all(), [
            'created_by' => $this->currentUser->id(),
        ]));
        return $this->success();
    }

    #[Put(
        path: '/admin/ctoc/user_merchant/save/{id}',
        operationId: 'ctoc:user_merchant:save',
        summary: 'c2c商家申请保存',
        security: [['Bearer' => [], 'ApiKey' => []]],
        tags: ['c2c商家申请'],
    )]
    #[Permission(code: 'ctoc:user_merchant:save')]
    #[ResultResponse(instance: new Result())]
    public function save(int $id, Request $request): Result
    {
        $this->userMerchantService->updateById($id, array_merge($request->all(), [
            'updated_by' => $this->currentUser->id(),
        ]));
        return $this->success();
    }

    #[Delete(
        path: '/admin/ctoc/user_merchant/delete',
        operationId: 'ctoc:user_merchant:delete',
        summary: 'c2c商家申请删除',
        security: [['Bearer' => [], 'ApiKey' => []]],
        tags: ['c2c商家申请'],
    )]
    #[ResultResponse(instance: new Result())]
    #[Permission(code: 'ctoc:user_merchant:delete')]
    public function delete(): Result
    {
        $this->userMerchantService->deleteById($this->getRequestData());
        return $this->success();
    }

}
