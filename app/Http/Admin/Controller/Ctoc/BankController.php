<?php

declare(strict_types=1);
/**
 * This file is part of MineAdmin.
 *
 * @link     https://www.mineadmin.com
 * @document https://doc.mineadmin.com
 * @contact  <EMAIL>
 * @license  https://github.com/mineadmin/MineAdmin/blob/master/LICENSE
 */

namespace App\Http\Admin\Controller\Ctoc;

use App\Http\Admin\Controller\AbstractController;
use App\Http\Admin\Middleware\PermissionMiddleware;
use App\Http\Admin\Request\Ctoc\BankRequest as Request;
use App\Http\Admin\Service\Ctoc\BankService;
use App\Http\Common\Middleware\AccessTokenMiddleware;
use App\Http\Common\Middleware\OperationMiddleware;
use App\Http\Common\Result;
use App\Http\CurrentUser;
use Hyperf\Di\Annotation\Inject;
use Hyperf\HttpServer\Annotation\Middleware;
use Hyperf\Swagger\Annotation as OA;
use Hyperf\Swagger\Annotation\Delete;
use Hyperf\Swagger\Annotation\Get;
use Hyperf\Swagger\Annotation\Post;
use Hyperf\Swagger\Annotation\Put;
use Mine\Access\Attribute\Permission;
use Mine\Swagger\Attributes\ResultResponse;


#[OA\Tag('银行')]
#[OA\HyperfServer('http')]
#[Middleware(middleware: AccessTokenMiddleware::class, priority: 100)]
#[Middleware(middleware: PermissionMiddleware::class, priority: 99)]
#[Middleware(middleware: OperationMiddleware::class, priority: 98)]
class BankController extends AbstractController
{
    public function __construct(
        private readonly CurrentUser $currentUser
    ) {}



    #[Inject]
    protected BankService $bankService;

    #[Get(
        path: '/admin/ctoc/bank/list',
        operationId: 'ctoc:bank:list',
        summary: '银行列表',
        security: [['Bearer' => [], 'ApiKey' => []]],
        tags: ['银行'],
    )]
    #[Permission(code: 'ctoc:bank:list')]
    #[ResultResponse(instance: new Result())]
    public function pageList(): Result
    {
        return $this->success(
            $this->bankService->page(
                $this->getRequestData(),
                $this->getCurrentPage(),
                $this->getPageSize()
            )
        );
    }

    #[Post(
        path: '/admin/ctoc/bank/create',
        operationId: 'ctoc:bank:create',
        summary: '银行新增',
        security: [['Bearer' => [], 'ApiKey' => []]],
        tags: ['银行'],
    )]
    #[Permission(code: 'ctoc:bank:create')]
    #[ResultResponse(instance: new Result())]
    public function create(Request $request): Result
    {
        $this->bankService->create(array_merge($request->all(), [
            'created_by' => $this->currentUser->id(),
        ]));
        return $this->success();
    }

    #[Put(
        path: '/admin/ctoc/bank/save/{id}',
        operationId: 'ctoc:bank:save',
        summary: '银行保存',
        security: [['Bearer' => [], 'ApiKey' => []]],
        tags: ['银行'],
    )]
    #[Permission(code: 'ctoc:bank:save')]
    #[ResultResponse(instance: new Result())]
    public function save(int $id, Request $request): Result
    {
        $this->bankService->updateById($id, array_merge($request->all(), [
            'updated_by' => $this->currentUser->id(),
        ]));
        return $this->success();
    }

    #[Delete(
        path: '/admin/ctoc/bank/delete',
        operationId: 'ctoc:bank:delete',
        summary: '银行删除',
        security: [['Bearer' => [], 'ApiKey' => []]],
        tags: ['银行'],
    )]
    #[ResultResponse(instance: new Result())]
    #[Permission(code: 'ctoc:bank:delete')]
    public function delete(): Result
    {
        $this->bankService->deleteById($this->getRequestData());
        return $this->success();
    }

}
