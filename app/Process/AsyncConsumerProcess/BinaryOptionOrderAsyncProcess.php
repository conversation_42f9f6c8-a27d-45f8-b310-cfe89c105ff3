<?php

/**
 * BinaryOptionOrderAsyncProcess.php
 * Author    chenmaq (<EMAIL>)
 * Version   1.0
 * Date      2025/7/18
 * @link     bbbtrade.net
 * @document bbbtrade.net
 */

namespace App\Process\AsyncConsumerProcess;

use Hyperf\AsyncQueue\Process\ConsumerProcess;
use Hyperf\Process\Annotation\Process;

#[Process(name: 'binary-option-order-async-process')]
class BinaryOptionOrderAsyncProcess extends ConsumerProcess
{
    public int $nums = 1;

    public bool $enableCoroutine = true;

    public string $queue = 'binary-order';

    public function isEnable($server): bool
    {
        return (bool)env('MARKET_DATA_SERVER',false);
    }
}