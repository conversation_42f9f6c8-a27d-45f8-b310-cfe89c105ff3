<?php

/**
 * MatchEngineBaseProcess.php
 * 撮合引擎基础进程类 - 实现进程预分配和币种管理
 * Author:chenmaq (<EMAIL>)
 * Contact:tg:@chenmaq
 * Version:2.0
 * Date:2025/6/26
 * Website:algoquant.org
 */

namespace App\Process;

use App\Enum\MatchEngine\ManagerStreamKey;
use App\MarketData\Process\MatchEngine\Logger\MatchEngineLogger;
use App\MarketData\Service\MatchEngine\RedisStreamHandler;
use Hyperf\Coordinator\Constants;
use Hyperf\Coordinator\CoordinatorManager;
use Hyperf\Coroutine\Coroutine;
use Hyperf\Engine\Channel;
use Hyperf\Engine\Constant;
use Hyperf\Process\AbstractProcess;
use Hyperf\Process\Event\AfterCoroutineHandle;
use Hyperf\Process\Event\AfterProcessHandle;
use Hyperf\Process\Event\BeforeCoroutineHandle;
use Hyperf\Process\Event\BeforeProcessHandle;
use Hyperf\Process\Event\PipeMessage;
use Hyperf\Process\Exception\ServerInvalidException;
use Hyperf\Process\Exception\SocketAcceptException;
use Hyperf\Process\ProcessCollector;
use Hyperf\Process\ProcessManager;
use Psr\Container\ContainerInterface;
use Swoole\Coroutine\Socket;
use Swoole\Process as SwooleProcess;
use Swoole\Server;
use Swoole\Table;
use Swoole\Timer;
use Throwable;

class MatchEngineBaseProcess extends AbstractProcess
{
    public bool $enableCoroutine = true;

    public array $currency = [];
    public array $currencyData = []; // 存储完整的币种数据

    /**
     * 市场类型标识（现货=1，合约=5，期货=4）
     */
    protected ?int $marketType = null;

    /**
     * 进程配置
     */
    protected int $maxProcesses;                    // 最大进程数
    protected int $currenciesPerProcess;            // 每个进程最大币种数
    protected int $preAllocProcesses;               // 预分配进程数
    protected bool $autoScale;                      // 是否自动扩容

    /**
     * 进程池管理
     */
    protected array $processPool = [];              // 所有进程信息 [processIndex => processInfo]
    protected array $activeProcesses = [];          // 活跃进程 [processIndex => true]
    protected array $idleProcesses = [];            // 空闲进程 [processIndex => true]
    protected array $currencyToProcess = [];        // 币种到进程映射 [symbol => processIndex]
    protected array $processCurrencyCount = [];     // 每个进程的币种数量 [processIndex => count]

    /**
     * 共享内存表
     */
    protected static array $commandTables = [];     // 按市场类型分组的命令队列表
    protected static ?Table $statusTable = null;    // 进程状态表
    protected static ?Table $mappingTable = null;   // 币种映射表
    protected static array $orderTables = [];       // 按市场类型分组的订单队列表

    /**
     * 当前进程相关
     */
    protected int $currentProcessIndex = 0;
    protected array $assignedCurrencies = [];
    protected bool $isManagerProcess = false;       // 是否为管理进程

    /**
     * 币种分片数组 [processIndex => [currency1, currency2, ...]]
     */
    protected array $currencyChunks = [];

    /**
     * 日志器
     */
    protected MatchEngineLogger $logger;

    /**
     * Redis Stream 处理器
     */
    protected ?RedisStreamHandler $streamHandler = null;

    public function __construct(ContainerInterface $container, array $currencyData = [])
    {
        $this->currencyData = $currencyData;
        // 提取币种符号用于进程分配
        $this->currency = array_keys($currencyData);

        $this->logger = $container->get(MatchEngineLogger::class);
        $this->streamHandler = $container->get(RedisStreamHandler::class);
        $this->loadConfiguration();
        $this->initializeSharedTables();
        $this->calculateProcessConfiguration();

        parent::__construct($container);
    }

    /**
     * 加载环境配置
     */
    protected function loadConfiguration(): void
    {
        $this->maxProcesses = intval(env('MATCH_ENGINE_MAX_PROCESSES', 10));
        $this->currenciesPerProcess = intval(env('MATCH_ENGINE_CURRENCIES_PER_PROCESS', 5));
        $this->preAllocProcesses = intval(env('MATCH_ENGINE_PREALLOC_PROCESSES', 3));
        $this->autoScale = filter_var(env('MATCH_ENGINE_AUTO_SCALE', true), FILTER_VALIDATE_BOOLEAN);
    }

    /**
     * 初始化共享内存表
     */
    protected function initializeSharedTables(): void
    {
        // 为每个市场类型创建独立的命令表
        if (empty(self::$commandTables)) {
            foreach (['spot', 'contract', 'futures'] as $marketType) {
                self::$commandTables[$marketType] = new Table(10000);
                self::$commandTables[$marketType]->column('id', Table::TYPE_STRING, 64);
                self::$commandTables[$marketType]->column('type', Table::TYPE_STRING, 32);
                self::$commandTables[$marketType]->column('data', Table::TYPE_STRING, 2048);
                self::$commandTables[$marketType]->column('target_process', Table::TYPE_INT, 4);
                self::$commandTables[$marketType]->column('timestamp', Table::TYPE_INT, 8);
                self::$commandTables[$marketType]->column('processed', Table::TYPE_INT, 1);
                self::$commandTables[$marketType]->column('market_type', Table::TYPE_STRING, 16);
                self::$commandTables[$marketType]->create();
            }
        }

        if (self::$statusTable === null) {
            // 进程状态表
            self::$statusTable = new Table(100);
            self::$statusTable->column('process_index', Table::TYPE_INT, 4);
            self::$statusTable->column('status', Table::TYPE_STRING, 16);  // active, idle, busy, error
            self::$statusTable->column('currency_count', Table::TYPE_INT, 4);
            self::$statusTable->column('last_heartbeat', Table::TYPE_INT, 8);
            self::$statusTable->column('created_at', Table::TYPE_INT, 8);
            self::$statusTable->create();
        }

        if (self::$mappingTable === null) {
            // 币种映射表
            self::$mappingTable = new Table(10000);
            self::$mappingTable->column('symbol', Table::TYPE_STRING, 32);
            self::$mappingTable->column('process_index', Table::TYPE_INT, 4);
            self::$mappingTable->column('status', Table::TYPE_STRING, 16); // active, stopped, paused
            self::$mappingTable->column('assigned_at', Table::TYPE_INT, 8);
            self::$mappingTable->create();
        }

        // 为每个市场类型创建独立的订单表
        if (empty(self::$orderTables)) {
            foreach (['spot', 'contract', 'futures'] as $marketType) {
                self::$orderTables[$marketType] = new Table(10000);
                self::$orderTables[$marketType]->column('id', Table::TYPE_STRING, 64);
                self::$orderTables[$marketType]->column('type', Table::TYPE_STRING, 32);
                self::$orderTables[$marketType]->column('data', Table::TYPE_STRING, 2048);
                self::$orderTables[$marketType]->column('target_process', Table::TYPE_INT, 4);
                self::$orderTables[$marketType]->column('timestamp', Table::TYPE_INT, 8);
                self::$orderTables[$marketType]->column('processed', Table::TYPE_INT, 1);
                self::$orderTables[$marketType]->column('market_type', Table::TYPE_STRING, 16);
                self::$orderTables[$marketType]->create();
            }
        }
    }

    /**
     * 计算进程配置
     */
    protected function calculateProcessConfiguration(): void
    {
        $currencyCount = count($this->currency);

        if ($currencyCount > 0) {
            // 根据币种数量计算需要的工作进程数
            $requiredWorkerProcesses = ceil($currencyCount / $this->currenciesPerProcess);

            // 总进程数 = 1个管理进程 + 需要的工作进程数 + 预分配进程数，但不超过最大进程数
            $this->nums = min(1 + $requiredWorkerProcesses + $this->preAllocProcesses, $this->maxProcesses);

            // 创建币种分片
            $this->currencyChunks = array_chunk($this->currency, $this->currenciesPerProcess);
        } else {
            // 没有币种时，创建 1个管理进程 + 预分配的工作进程数
            $this->nums = 1 + $this->preAllocProcesses;
            $this->currencyChunks = [];
        }

        // 初始化进程池结构
        $this->initializeProcessPool();
    }

    /**
     * 初始化进程池结构
     */
    protected function initializeProcessPool(): void
    {
        for ($i = 0; $i < $this->nums; $i++) {
            $this->processPool[$i] = [
                'process_index' => $i,
                'status' => 'idle',
                'currencies' => [],
                'currency_count' => 0,
                'created_at' => time(),
                'last_heartbeat' => time()
            ];

            $this->processCurrencyCount[$i] = 0;
            $this->idleProcesses[$i] = true;
        }

        // 分配初始币种
        $this->assignInitialCurrencies();
    }

    /**
     * 分配初始币种到进程
     */
    protected function assignInitialCurrencies(): void
    {
        $processIndex = 1; // 从进程1开始，因为进程0是管理进程

        foreach ($this->currency as $currency) {
            if ($this->processCurrencyCount[$processIndex] >= $this->currenciesPerProcess) {
                $processIndex++;
                if ($processIndex >= $this->nums) {
                    break; // 超出可用进程数
                }
            }

            $this->assignCurrencyToProcess($currency, $processIndex);
        }
    }

    /**
     * 将币种分配给指定进程
     */
    protected function assignCurrencyToProcess(string $currency, int $processIndex): void
    {
        if (!isset($this->processPool[$processIndex])) {
            return;
        }

        // 更新进程池
        $this->processPool[$processIndex]['currencies'][] = $currency;
        $this->processPool[$processIndex]['currency_count']++;
        $this->processCurrencyCount[$processIndex]++;

        // 更新币种映射
        $this->currencyToProcess[$currency] = $processIndex;

        // 更新进程状态
        if ($this->processPool[$processIndex]['status'] === 'idle') {
            $this->processPool[$processIndex]['status'] = 'active';
            unset($this->idleProcesses[$processIndex]);
            $this->activeProcesses[$processIndex] = true;
        }

        // 更新共享内存表
        self::$mappingTable->set($currency, [
            'symbol' => $currency,
            'process_index' => $processIndex,
            'status' => 'active',
            'assigned_at' => time()
        ]);
    }

    public function bind($server): void
    {
        if (Constant::isCoroutineServer($server)) {
            $this->bindCoroutineServer($server);
            return;
        }

        if ($server instanceof Server) {
            $this->bindServer($server);
            return;
        }

        throw new ServerInvalidException(sprintf('Server %s is invalid.', get_class($server)));
    }

    protected function bindServer(Server $server): void
    {
        $num = $this->nums;

        // 第一个进程作为管理进程
        for ($i = 0; $i < $num; ++$i) {
            $process = new SwooleProcess(function (SwooleProcess $process) use ($i) {
                try {
                    $this->event?->dispatch(new BeforeProcessHandle($this, $i));

                    $this->process = $process;
                    $this->setCurrentProcessData($i);

                    // 第0个进程作为管理进程
                    if ($i === 0) {
                        $this->isManagerProcess = true;
                        $this->runAsManager();
                    } else {
                        $this->isManagerProcess = false;
                        if ($this->enableCoroutine) {
                            $quit = new Channel(1);
                            $this->listen($quit);
                        }
                        $this->runAsWorker();
                    }
                } catch (Throwable $throwable) {
                    $this->logThrowable($throwable);
                } finally {
                    $this->event?->dispatch(new AfterProcessHandle($this, $i));
                    if (isset($quit)) {
                        $quit->push(true);
                    }
                    Timer::clearAll();
                    CoordinatorManager::until(Constants::WORKER_EXIT)->resume();
                    sleep($this->restartInterval);
                }
            }, $this->redirectStdinStdout, $this->pipeType, $this->enableCoroutine);

            $process->setBlocking(false);
            $server->addProcess($process);

            if ($this->enableCoroutine) {
                ProcessCollector::add($this->name, $process);
            }
        }
    }

    protected function bindCoroutineServer($server): void
    {
        $num = $this->nums;
        Coroutine::create(static function () {
            if (CoordinatorManager::until(Constants::WORKER_EXIT)->yield()) {
                ProcessManager::setRunning(false);
            }
        });

        for ($i = 0; $i < $num; ++$i) {
            $handler = function () use ($i) {
                $this->event?->dispatch(new BeforeCoroutineHandle($this, $i));

                $this->setCurrentProcessData($i);

                // 第0个进程作为管理进程
                if ($i === 0) {
                    $this->isManagerProcess = true;
                    while (true) {
                        try {
                            $this->runAsManager();
                        } catch (Throwable $throwable) {
                            $this->logThrowable($throwable);
                        }

                        if (CoordinatorManager::until(Constants::WORKER_EXIT)->yield($this->restartInterval)) {
                            break;
                        }
                    }
                } else {
                    $this->isManagerProcess = false;
                    while (true) {
                        try {
                            $this->runAsWorker();
                        } catch (Throwable $throwable) {
                            $this->logThrowable($throwable);
                        }

                        if (CoordinatorManager::until(Constants::WORKER_EXIT)->yield($this->restartInterval)) {
                            break;
                        }
                    }
                }

                $this->event?->dispatch(new AfterCoroutineHandle($this, $i));
            };

            Coroutine::create($handler);
        }
    }

    /**
     * 作为管理进程运行
     */
    protected function runAsManager(): void
    {
        $this->updateProcessStatus('active');

        // 初始化 Redis Streams
        $this->initializeRedisStreams();

        // 启动管理进程的各种定时任务
        if ($this->enableCoroutine) {
            // 处理 Redis Stream 命令
            go(function () {
                while (true) {
                    $this->processRedisStreamCommands();
                    Coroutine::sleep(0.01); // 10ms检查一次
                }
            });

            // 处理 Redis Stream 订单
            go(function () {
                while (true) {
                    $this->processRedisStreamOrders();
                    Coroutine::sleep(0.01); // 10ms检查一次
                }
            });

            // 处理 Redis Stream 系统控制
            go(function () {
                while (true) {
                    $this->processRedisStreamSystemControl();
                    Coroutine::sleep(0.01); // 10ms检查一次
                }
            });

            // 处理 Redis Stream 配置更新
            go(function () {
                while (true) {
                    $this->processRedisStreamConfigUpdate();
                    Coroutine::sleep(0.01); // 10ms检查一次
                }
            });

            // 处理命令队列
            go(function () {
                while (true) {
                    $this->processCommands();
                    Coroutine::sleep(0.01); // 10ms检查一次
                }
            });

            // 处理订单队列并分发给工作进程
            go(function () {
                while (true) {
                    $this->processOrderQueue();
                    Coroutine::sleep(0.01); // 10ms检查一次
                }
            });

            // 监控进程状态
            go(function () {
                while (true) {
                    $this->monitorProcessStatus();
                    Coroutine::sleep(5); // 5秒检查一次
                }
            });

            // 清理过期数据
            go(function () {
                while (true) {
                    $this->cleanupExpiredData();
                    Coroutine::sleep(60); // 60秒清理一次
                }
            });

            // 管理进程心跳
            go(function () {
                while (true) {
                    $this->sendHeartbeat();
                    Coroutine::sleep(30); // 30秒心跳一次
                }
            });

            // 管理进程主循环 - 保持运行
            while (true) {
                Coroutine::sleep(1);
            }
        } else {
            // 非协程模式下的处理
            Timer::tick(10, [$this, 'processCommands']);
            Timer::tick(5000, [$this, 'monitorProcessStatus']);
            Timer::tick(60000, [$this, 'cleanupExpiredData']);

            // 非协程模式主循环
            while (true) {
                sleep(1);
            }
        }
    }

    /**
     * 作为工作进程运行
     */
    protected function runAsWorker(): void
    {
        $this->updateProcessStatus('active');

        // 启动心跳
        if ($this->enableCoroutine) {
            go(function () {
                while (true) {
                    $this->sendHeartbeat();
                    Coroutine::sleep(30); // 30秒心跳一次
                }
            });
        } else {
            Timer::tick(30000, [$this, 'sendHeartbeat']);
        }

        // 调用子类的具体实现
        $this->handleWorkerProcess();
    }

    /**
     * 工作进程具体处理（子类重写）
     */
    protected function handleWorkerProcess(): void
    {
        // 默认实现：保持运行
        if ($this->enableCoroutine) {
            while (true) {
                Coroutine::sleep(1);
            }
        } else {
            while (true) {
                sleep(1);
            }
        }
    }

    /**
     * 处理命令队列
     */
    public function processCommands(): void
    {
        if (!$this->isManagerProcess || $this->marketType === null) {
            return;
        }

        // 获取当前市场类型对应的命令表
        $marketTypeString = $this->getMarketTypeString();
        $commandTable = self::$commandTables[$marketTypeString] ?? null;
        if ($commandTable === null) {
            return;
        }

        // 先收集需要处理的命令
        $commandsToProcess = [];
        foreach ($commandTable as $key => $command) {
            if ($command['processed'] == 0) {
                $commandsToProcess[$key] = $command;
            }
        }

        // 处理命令并记录需要删除的key
        $processedCommands = [];
        foreach ($commandsToProcess as $key => $command) {
            try {
                $shouldDelete = $this->handleCommand($command);
                if ($shouldDelete) {
                    $processedCommands[] = $key;
                }
            } catch (Throwable $e) {
                // 记录异常并删除命令
                $this->logThrowable($e);
                $processedCommands[] = $key;
            }
        }

        // 删除已处理的命令
        foreach ($processedCommands as $key) {
            $commandTable->del($key);
        }
    }

    /**
     * 处理订单队列并分发给工作进程
     */
    public function processOrderQueue(): void
    {
        if (!$this->isManagerProcess || $this->marketType === null) {
            return;
        }

        // 获取当前市场类型对应的订单表
        $marketTypeString = $this->getMarketTypeString();
        $orderTable = self::$orderTables[$marketTypeString] ?? null;
        if ($orderTable === null) {
            return;
        }

        // 先收集需要处理的订单
        $ordersToProcess = [];
        foreach ($orderTable as $key => $order) {
            if ($order['processed'] == 0) {
                $ordersToProcess[$key] = $order;
            }
        }

        // 处理订单并记录需要删除的key
        $processedOrders = [];
        foreach ($ordersToProcess as $key => $order) {
            try {
                $this->distributeOrderToWorker($order);
                $processedOrders[] = $key;
            } catch (Throwable $e) {
                $this->logThrowable($e);
                $processedOrders[] = $key; // 即使出错也要删除
            }
        }

        // 删除已处理的订单
        foreach ($processedOrders as $key) {
            $orderTable->del($key);
        }
    }

    /**
     * 将订单分发给对应的工作进程
     */
    protected function distributeOrderToWorker(array $orderData): void
    {
        $data = json_decode($orderData['data'], true);
        if (!$data) {
            return;
        }

        $symbol = $data['symbol'] ?? '';
        if (empty($symbol)) {
            return;
        }

        // 查找币种对应的进程
        $targetProcess = $this->currencyToProcess[$symbol] ?? null;
        if ($targetProcess === null) {
            return;
        }

        // 检查目标进程是否活跃
        $processStatus = self::$statusTable->get($targetProcess);
        if (!$processStatus || $processStatus['status'] !== 'active') {
            return;
        }

        // 获取当前市场类型对应的命令表
        if ($this->marketType === null) {
            return;
        }
        
        $marketTypeString = $this->getMarketTypeString();
        $commandTable = self::$commandTables[$marketTypeString] ?? null;
        if ($commandTable === null) {
            return;
        }

        // 通过命令系统发送订单给工作进程
        $commandType = match ($orderData['type']) {
            'cancel_order' => 'cancel_order',
            'modify_order' => 'modify_order',
            default => 'process_order'
        };
        $orderCommand = [
            'id' => 'order_cmd_' . uniqid() . '_' . mt_rand(1000, 9999),
            'type' => $commandType,
            'data' => json_encode($data),
            'target_process' => $targetProcess,
            'timestamp' => time(),
            'processed' => 0,
            'market_type' => $marketTypeString
        ];
        $result = $commandTable->set($orderCommand['id'], $orderCommand);
        if (!$result) {
            // 重试一次
            usleep(1000);
            $retryId = $orderCommand['id'] . '_retry';
            $orderCommand['id'] = $retryId;
            $commandTable->set($retryId, $orderCommand);
        }
    }

    /**
     * 处理具体命令
     * @return bool 返回true表示命令已处理完成可以删除，false表示命令应保留
     */
    protected function handleCommand(array $command): bool
    {
        $type = $command['type'];
        $data = json_decode($command['data'], true);

        switch ($type) {
            case 'add_currency':
                $this->handleAddCurrency($data);
                return true;
            case 'remove_currency':
                $this->handleRemoveCurrency($data);
                return true;
            case 'stop_currency':
                $this->handleStopCurrency($data);
                return true;
            case 'start_currency':
                $this->handleStartCurrency($data);
                return true;
            case 'get_status':
                $this->handleGetStatus();
                return true;
            case 'reload_config':
                $this->handleReloadConfig();
                return true;
            case 'process_order':
            case 'cancel_order':
            case 'modify_order':
                // 只有管理进程会调用这个方法，工作进程有自己的 processWorkerCommands
                if ($this->isManagerProcess) {
                    // 检查命令的目标进程
                    $targetProcess = $command['target_process'] ?? 0;
                    // 如果命令是发给工作进程的，不删除命令，让工作进程处理
                    if ($targetProcess > 0 && $targetProcess != $this->currentProcessIndex) {
                        return false; // 不删除命令，保留给工作进程
                    }
                    // 如果是发给管理进程的订单命令，跳过处理但删除命令
                    return true;
                } else {
                    // 工作进程不应该调用此方法，使用 processWorkerCommands 代替
                    return true;
                }
            default:
                // 交给子类处理
                $this->handleCustomCommand($type, $data);
                return true;
        }
    }

    /**
     * 处理添加币种命令
     */
    protected function handleAddCurrency(array $data): void
    {
        $symbol = $data['symbol'] ?? '';
        if (empty($symbol)) {
            return;
        }

        // 检查币种是否已存在
        if (isset($this->currencyToProcess[$symbol])) {
            return;
        }

        // 找到最适合的进程
        $targetProcess = $this->findBestProcessForCurrency($symbol);

        if ($targetProcess !== null) {
            $this->assignCurrencyToProcess($symbol, $targetProcess);
            $this->logInfo("Added currency {$symbol} to process {$targetProcess}");
        } else {
            $this->logWarning("Cannot add currency {$symbol}: no available process");
        }
    }

    /**
     * 处理移除币种命令
     */
    protected function handleRemoveCurrency(array $data): void
    {
        $symbol = $data['symbol'] ?? '';
        if (empty($symbol)) {
            return;
        }

        $processIndex = $this->currencyToProcess[$symbol] ?? null;
        if ($processIndex === null) {
            return;
        }

        $this->removeCurrencyFromProcess($symbol, $processIndex);
        $this->logInfo("Removed currency {$symbol} from process {$processIndex}");
    }

    /**
     * 处理停止币种命令
     */
    protected function handleStopCurrency(array $data): void
    {
        $symbol = $data['symbol'] ?? '';
        if (empty($symbol)) {
            return;
        }

        self::$mappingTable->set($symbol, ['status' => 'stopped']);
        $this->logInfo("Stopped currency {$symbol}");
    }

    /**
     * 处理启动币种命令
     */
    protected function handleStartCurrency(array $data): void
    {
        $symbol = $data['symbol'] ?? '';
        if (empty($symbol)) {
            return;
        }

        self::$mappingTable->set($symbol, ['status' => 'active']);
        $this->logInfo("Started currency {$symbol}");
    }

    /**
     * 处理获取状态命令
     */
    protected function handleGetStatus(): void
    {
        // 统计表中的数据量（使用安全的遍历方式）
        $commandCounts = [];
        $totalCommandCount = 0;
        $orderCounts = [];
        $totalOrderCount = 0;

        foreach (self::$commandTables as $marketType => $commandTable) {
            $count = 0;
            foreach ($commandTable as $key => $command) {
                $count++;
            }
            $commandCounts[$marketType] = $count;
            $totalCommandCount += $count;
        }

        foreach (self::$orderTables as $marketType => $orderTable) {
            $count = 0;
            foreach ($orderTable as $key => $order) {
                $count++;
            }
            $orderCounts[$marketType] = $count;
            $totalOrderCount += $count;
        }

        $status = [
            'total_processes' => $this->nums,
            'active_processes' => count($this->activeProcesses),
            'idle_processes' => count($this->idleProcesses),
            'total_currencies' => count($this->currencyToProcess),
            'pending_commands' => $totalCommandCount,
            'pending_commands_by_market' => $commandCounts,
            'pending_orders' => $totalOrderCount,
            'pending_orders_by_market' => $orderCounts,
            'market_type' => $this->getMarketTypeString(),
            'processes' => $this->processPool
        ];

        $this->logInfo("Status: " . json_encode($status));
    }

    /**
     * 处理重新加载配置命令
     */
    protected function handleReloadConfig(): void
    {
        $this->loadConfiguration();
        $this->logInfo("Configuration reloaded");
    }

    /**
     * 自定义命令处理（由子类重写）
     */
    protected function handleCustomCommand(string $type, array $data): void
    {
        // 子类可以重写此方法来处理自定义命令
    }

    /**
     * 监控进程状态
     */
    public function monitorProcessStatus(): void
    {
        if (!$this->isManagerProcess) {
            return;
        }

        $currentTime = time();

        foreach ($this->processPool as $processIndex => $processInfo) {
            // 跳过管理进程自己（进程0）
            if ($processIndex === 0) {
                continue;
            }

            $statusData = self::$statusTable->get($processIndex);
            $lastHeartbeat = $statusData['last_heartbeat'] ?? 0;

            // 检查进程是否超时
            if ($currentTime - $lastHeartbeat > 90) { // 90秒无心跳认为进程异常
                $this->handleProcessTimeout($processIndex);
            }
        }
    }

    /**
     * 处理进程超时
     */
    protected function handleProcessTimeout(int $processIndex): void
    {
        $this->logWarning("Process {$processIndex} timeout detected");

        // 标记进程为错误状态
        $this->processPool[$processIndex]['status'] = 'error';
        self::$statusTable->set($processIndex, ['status' => 'error']);

        // 从活跃进程中移除
        unset($this->activeProcesses[$processIndex]);
    }

    /**
     * 发送心跳
     */
    public function sendHeartbeat(): void
    {
        self::$statusTable->set($this->currentProcessIndex, [
            'process_index' => $this->currentProcessIndex,
            'status' => $this->processPool[$this->currentProcessIndex]['status'] ?? 'active',
            'currency_count' => count($this->assignedCurrencies),
            'last_heartbeat' => time(),
            'created_at' => $this->processPool[$this->currentProcessIndex]['created_at'] ?? time()
        ]);
    }

    /**
     * 清理过期数据
     * 由于现在立即删除已处理的数据，这里主要清理异常情况下可能遗留的过期数据
     */
    public function cleanupExpiredData(): void
    {
        if (!$this->isManagerProcess) {
            return;
        }

        $expireTime = time() - 1800; // 30分钟前的数据

        // 收集过期命令（处理所有市场类型）
        $cleanedCommands = 0;
        foreach (self::$commandTables as $marketType => $commandTable) {
            $expiredCommands = [];
            foreach ($commandTable as $key => $command) {
                if ($command['timestamp'] < $expireTime) {
                    $expiredCommands[] = $key;
                }
            }
            
            // 删除过期命令
            foreach ($expiredCommands as $key) {
                $commandTable->del($key);
                $cleanedCommands++;
            }
        }

        // 收集过期订单（处理所有市场类型）
        $cleanedOrders = 0;
        foreach (self::$orderTables as $marketType => $orderTable) {
            $expiredOrders = [];
            foreach ($orderTable as $key => $order) {
                if ($order['timestamp'] < $expireTime) {
                    $expiredOrders[] = $key;
                }
            }
            
            // 删除过期订单
            foreach ($expiredOrders as $key) {
                $orderTable->del($key);
                $cleanedOrders++;
            }
        }



        if ($cleanedCommands > 0 || $cleanedOrders > 0) {
            $this->logInfo("Cleaned up {$cleanedCommands} expired commands and {$cleanedOrders} expired orders");
        }
    }

    /**
     * 找到最适合的进程来分配币种
     */
    protected function findBestProcessForCurrency(string $symbol): ?int
    {
        // 优先使用空闲进程
        if (!empty($this->idleProcesses)) {
            return array_key_first($this->idleProcesses);
        }

        // 找到币种数量最少的活跃进程
        $minCount = PHP_INT_MAX;
        $bestProcess = null;

        foreach ($this->activeProcesses as $processIndex => $active) {
            $count = $this->processCurrencyCount[$processIndex];
            if ($count < $this->currenciesPerProcess && $count < $minCount) {
                $minCount = $count;
                $bestProcess = $processIndex;
            }
        }

        return $bestProcess;
    }

    /**
     * 从进程中移除币种
     */
    protected function removeCurrencyFromProcess(string $symbol, int $processIndex): void
    {
        // 更新进程池
        if (isset($this->processPool[$processIndex])) {
            $currencies = $this->processPool[$processIndex]['currencies'];
            $this->processPool[$processIndex]['currencies'] = array_filter($currencies, fn($s) => $s !== $symbol);
            $this->processPool[$processIndex]['currency_count']--;
            $this->processCurrencyCount[$processIndex]--;

            // 如果进程没有币种了，标记为空闲
            if ($this->processCurrencyCount[$processIndex] == 0) {
                $this->processPool[$processIndex]['status'] = 'idle';
                unset($this->activeProcesses[$processIndex]);
                $this->idleProcesses[$processIndex] = true;
            }
        }

        // 移除币种映射
        unset($this->currencyToProcess[$symbol]);
        self::$mappingTable->del($symbol);
    }

    /**
     * 更新进程状态
     */
    protected function updateProcessStatus(string $status): void
    {
        if (isset($this->processPool[$this->currentProcessIndex])) {
            $this->processPool[$this->currentProcessIndex]['status'] = $status;
        }
    }

    /**
     * 设置当前进程的数据
     */
    protected function setCurrentProcessData(int $processIndex): void
    {
        $this->currentProcessIndex = $processIndex;
        $this->isManagerProcess = ($processIndex === 0);

        // 获取分配给当前进程的币种
        if (isset($this->processPool[$processIndex])) {
            $this->assignedCurrencies = $this->processPool[$processIndex]['currencies'];
        } else {
            $this->assignedCurrencies = [];
        }

        // 设置日志器的进程信息
        $processType = $this->isManagerProcess ? 'manager' : 'worker';
        MatchEngineLogger::setProcessInfo($processIndex, $processType);

        // 更新进程状态表
        if (self::$statusTable !== null) {
            self::$statusTable->set($processIndex, [
                'process_index' => $processIndex,
                'status' => 'active',
                'currency_count' => count($this->assignedCurrencies),
                'last_heartbeat' => time(),
                'created_at' => time()
            ]);
        }
    }

    // ========== 内部工具方法 ==========

    /**
     * 设置市场类型
     */
    protected function setMarketType(int $marketType): void
    {
        $this->marketType = $marketType;
    }

    /**
     * 获取市场类型
     */
    public function getMarketType(): ?int
    {
        return $this->marketType;
    }

    /**
     * 将数字市场类型转换为字符串
     */
    protected function getMarketTypeString(): string
    {
        return match ($this->marketType) {
            1 => 'spot',      // MarketType::CRYPTO
            5 => 'contract',  // MarketType::MARGIN  
            4 => 'futures',   // MarketType::FUTURES
            default => 'spot'
        };
    }

    /**
     * 获取当前进程的索引
     */
    public function getCurrentProcessIndex(): int
    {
        return $this->currentProcessIndex;
    }

    /**
     * 获取分配给当前进程的币种符号列表
     */
    public function getAssignedCurrencies(): array
    {
        return $this->assignedCurrencies;
    }

    /**
     * 获取分配给当前进程的币种完整数据
     */
    public function getAssignedCurrencyData(): array
    {
        $assignedData = [];
        foreach ($this->assignedCurrencies as $symbol) {
            if (isset($this->currencyData[$symbol])) {
                $assignedData[$symbol] = $this->currencyData[$symbol];
            }
        }
        return $assignedData;
    }

    /**
     * 检查是否为管理进程
     */
    public function isManagerProcess(): bool
    {
        return $this->isManagerProcess;
    }

    /**
     * 获取当前市场类型的命令队列表（工作进程需要访问）
     */
    public function getCommandTable(): ?Table
    {
        if ($this->marketType === null) {
            return null;
        }

        $marketTypeString = $this->getMarketTypeString();
        return self::$commandTables[$marketTypeString] ?? null;
    }

    /**
     * 获取所有命令队列表
     */
    public function getAllCommandTables(): array
    {
        return self::$commandTables;
    }

    /**
     * 获取当前市场类型的订单队列表
     */
    public function getOrderTable(): ?Table
    {
        if ($this->marketType === null) {
            return null;
        }

        $marketTypeString = $this->getMarketTypeString();
        return self::$orderTables[$marketTypeString] ?? null;
    }

    /**
     * 获取所有订单队列表
     */
    public function getAllOrderTables(): array
    {
        return self::$orderTables;
    }

    // ========== 日志方法 ==========

    protected function logInfo(string $message): void
    {
        $this->logger->logProcessStatus($message);
    }

    protected function logWarning(string $message): void
    {
        $this->logger->logWarning($message);
    }

    protected function logError(string $message, Throwable $e = null): void
    {
        $this->logger->logError($message, $e);
    }

    protected function logDebug(string $message): void
    {
        $this->logger->logDebug($message);
    }

    /**
     * Added event for listening data from worker/task.
     */
    protected function listen(Channel $quit)
    {
        Coroutine::create(function () use ($quit) {
            while ($quit->pop(0.001) !== true) {
                try {
                    /** @var Socket $sock */
                    $sock = $this->process->exportSocket();
                    $recv = $sock->recv($this->recvLength, $this->recvTimeout);
                    if ($recv === '') {
                        throw new SocketAcceptException('Socket is closed', $sock->errCode);
                    }

                    if ($recv === false && $sock->errCode !== SOCKET_ETIMEDOUT) {
                        throw new SocketAcceptException('Socket is closed', $sock->errCode);
                    }

                    if ($this->event && $recv !== false && $data = unserialize($recv)) {
                        $this->event->dispatch(new PipeMessage($data));
                    }
                } catch (Throwable $exception) {
                    $this->logThrowable($exception);
                    if ($exception instanceof SocketAcceptException) {
                        // TODO: Reconnect the socket.
                        break;
                    }
                }
            }
            $quit->close();
        });
    }

    protected function logThrowable(Throwable $throwable): void
    {
        $this->logger->logError('Exception occurred', $throwable);

        if ($throwable instanceof SocketAcceptException) {
            $this->logger->logError('Socket of process is unavailable, please restart the server');
        }
    }

    // ========== Redis Stream 处理方法 ==========

    /**
     * 初始化 Redis Streams
     */
    protected function initializeRedisStreams(): void
    {
        if ($this->streamHandler) {
            $this->streamHandler->initializeStreams();
            $this->logInfo("Redis Streams initialized");
        }
    }

    /**
     * 处理 Redis Stream 命令消息
     */
    protected function processRedisStreamCommands(): void
    {
        if (!$this->isManagerProcess || !$this->streamHandler || $this->marketType === null) {
            return;
        }

        // 只消费当前市场类型的命令
        $marketTypeString = $this->getMarketTypeString();
        $messages = $this->streamHandler->consumeCommands($marketTypeString);
        foreach ($messages as $message) {
            $this->handleRedisStreamMessage($message);
        }
    }

    /**
     * 处理 Redis Stream 订单消息
     */
    protected function processRedisStreamOrders(): void
    {
        if (!$this->isManagerProcess || !$this->streamHandler || $this->marketType === null) {
            return;
        }

        // 只消费当前市场类型的订单
        $marketTypeString = $this->getMarketTypeString();
        $messages = $this->streamHandler->consumeOrders($marketTypeString);
        foreach ($messages as $message) {
            $this->handleRedisStreamMessage($message);
        }
    }

    /**
     * 处理 Redis Stream 系统控制消息
     */
    protected function processRedisStreamSystemControl(): void
    {
        if (!$this->isManagerProcess || !$this->streamHandler || $this->marketType === null) {
            return;
        }

        $marketTypeString = $this->getMarketTypeString();
        $messages = $this->streamHandler->consumeSystemControlByMarket($marketTypeString);
        foreach ($messages as $message) {
            $this->handleRedisStreamMessage($message);
        }
    }

    /**
     * 处理 Redis Stream 配置更新消息
     */
    protected function processRedisStreamConfigUpdate(): void
    {
        if (!$this->isManagerProcess || !$this->streamHandler || $this->marketType === null) {
            return;
        }

        $marketTypeString = $this->getMarketTypeString();
        $messages = $this->streamHandler->consumeConfigUpdateByMarket($marketTypeString);
        foreach ($messages as $message) {
            $this->handleRedisStreamMessage($message);
        }
    }

    /**
     * 处理单个 Redis Stream 消息
     */
    protected function handleRedisStreamMessage(array $message): void
    {
        try {
            $streamKey = $message['stream_key'];
            $messageId = $message['message_id'];
            $fields = $message['data'];

            // 验证消息格式
            if (!$this->streamHandler->validateMessage($fields, $message['stream'])) {
                $this->logWarning("Invalid message format from stream: {$message['stream']}");
                $this->streamHandler->ackMessage($streamKey, $messageId);
                return;
            }

            // 格式化消息
            $formattedData = $this->streamHandler->formatMessage($fields, $streamKey);
            // 根据流类型转发到 Swoole Table
            $success = $this->forwardToSwooleTable($streamKey, $formattedData);

            // 确认消息处理
            if ($success) {
                $this->streamHandler->ackMessage($streamKey, $messageId);
                //$this->logDebug("Processed message from {$streamKey->value}: {$messageId}");
            } else {
                $this->logError("Failed to forward message from {$streamKey->value}: {$messageId}");
                // 不确认消息，让其重新投递
            }

        } catch (\Throwable $e) {
            $this->logError("Error processing stream message", $e);
            // 出错时也要确认消息，避免无限重试
            if (isset($streamKey) && isset($messageId)) {
                $this->streamHandler->ackMessage($streamKey, $messageId);
            }
        }
    }

    /**
     * 将 Redis Stream 消息转发到 Swoole Table
     */
    protected function forwardToSwooleTable(ManagerStreamKey $streamKey, array $data): bool
    {
        $streamType = $streamKey->getStreamType();
        
        switch ($streamType) {
            case 'commands':
                return $this->forwardCommandToTable($data);

            case 'orders':
                return $this->forwardOrderToTable($data);

            case 'system_control':
                return $this->forwardSystemControlToTable($data);

            case 'config_update':
                return $this->forwardConfigUpdateToTable($data);

            default:
                return false;
        }
    }

    /**
     * 转发命令到 Swoole Table
     */
    protected function forwardCommandToTable(array $data): bool
    {
        if ($this->marketType === null) {
            return false;
        }

        // 获取当前市场类型对应的命令表
        $marketTypeString = $this->getMarketTypeString();
        $commandTable = self::$commandTables[$marketTypeString] ?? null;
        if ($commandTable === null) {
            return false;
        }

        $id = 'stream_cmd_' . uniqid() . '_' . mt_rand(1000, 9999);
        return $commandTable->set($id, [
            'id' => $id,
            'type' => $data['type'],
            'data' => $data['data'],
            'target_process' => $data['target_process'],
            'timestamp' => time(),
            'processed' => 0,
            'market_type' => $marketTypeString
        ]);
    }

    /**
     * 转发订单到 Swoole Table
     */
    protected function forwardOrderToTable(array $data): bool
    {
        if ($this->marketType === null) {
            return false;
        }

        // 获取当前市场类型对应的订单表
        $marketTypeString = $this->getMarketTypeString();
        $orderTable = self::$orderTables[$marketTypeString] ?? null;
        if ($orderTable === null) {
            return false;
        }

        $id = 'stream_order_' . uniqid() . '_' . mt_rand(1000, 9999);
        return $orderTable->set($id, [
            'id' => $id,
            'type' => $data['type'],
            'data' => $data['data'],
            'target_process' => 0, // 自动分配
            'timestamp' => time(),
            'processed' => 0,
            'market_type' => $marketTypeString
        ]);
    }

    /**
     * 转发系统控制到 Swoole Table
     */
    protected function forwardSystemControlToTable(array $data): bool
    {
        if ($this->marketType === null) {
            return false;
        }

        // 获取当前市场类型对应的命令表
        $marketTypeString = $this->getMarketTypeString();
        $commandTable = self::$commandTables[$marketTypeString] ?? null;
        if ($commandTable === null) {
            return false;
        }

        $id = 'stream_sys_' . uniqid() . '_' . mt_rand(1000, 9999);
        return $commandTable->set($id, [
            'id' => $id,
            'type' => $data['command'],
            'data' => $data['data'],
            'target_process' => 0, // 管理进程处理
            'timestamp' => time(),
            'processed' => 0,
            'market_type' => $marketTypeString
        ]);
    }

    /**
     * 转发配置更新到 Swoole Table
     */
    protected function forwardConfigUpdateToTable(array $data): bool
    {
        if ($this->marketType === null) {
            return false;
        }

        // 获取当前市场类型对应的命令表
        $marketTypeString = $this->getMarketTypeString();
        $commandTable = self::$commandTables[$marketTypeString] ?? null;
        if ($commandTable === null) {
            return false;
        }

        $id = 'stream_cfg_' . uniqid() . '_' . mt_rand(1000, 9999);
        return $commandTable->set($id, [
            'id' => $id,
            'type' => 'config_update',
            'data' => json_encode([
                'config_type' => $data['config_type'],
                'data' => $data['data']
            ]),
            'target_process' => 0, // 管理进程处理
            'timestamp' => time(),
            'processed' => 0,
            'market_type' => $marketTypeString
        ]);
    }



    /**
     * 子类需要重写此方法来实现具体的业务逻辑
     * 在此方法中可以通过 $this->getAssignedCurrencies() 获取分配给当前进程的币种
     * 通过 $this->isManagerProcess() 判断是否为管理进程
     */
    public function handle(): void
    {
        // 子类实现具体逻辑
    }
}