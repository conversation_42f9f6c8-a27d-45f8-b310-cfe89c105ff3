<?php

declare(strict_types=1);

namespace App\Listener\Copy;

use App\Enum\OrderType;
use App\Enum\TradeSide;
use App\Event\Trade\TraderSpotOrderCreatedEvent;
use App\Http\Api\Service\V1\Copy\CopySpotCalculationService;
use App\Http\Api\Service\V1\Copy\CopySpotTradeService;
use App\Model\Copy\CopySpotUserSetting;
use App\Model\Copy\CopySpotAdvancedSetting;
use App\Model\Copy\Enums\CopyStatus;
use App\Service\RedisFactory\CacheRedis;
use Hyperf\Context\ApplicationContext;
use Hyperf\Event\Annotation\Listener;
use Hyperf\Event\Contract\ListenerInterface;
use phpDocumentor\Reflection\PseudoTypes\IntegerValue;
use Psr\Log\LoggerInterface;
use App\Enum\Redis\CacheKey\CopySpotCacheKey;

#[Listener]
class TraderSpotOrderCreatedListener implements ListenerInterface
{
    private CopySpotCalculationService $calculationService;

    private CopySpotTradeService $copyTradeService;

    private CacheRedis $redis;

    private LoggerInterface $logger;

    private const CACHE_TTL = 1800;

    public function listen(): array
    {
        return [
            TraderSpotOrderCreatedEvent::class,
        ];
    }

    public function process(object $event): void
    {
        if (!$event instanceof TraderSpotOrderCreatedEvent) {
            return;
        }

        $this->calculationService = ApplicationContext::getContainer()->get(CopySpotCalculationService::class);
        $this->copyTradeService = ApplicationContext::getContainer()->get(CopySpotTradeService::class);
        $this->redis = ApplicationContext::getContainer()->get(CacheRedis::class);
        $this->logger = logger('跟单开单事件', 'copyTrade/spot_copy_trade.log');

        $traderOrder = $event->getOrder();
        $traderId = $traderOrder->getUserId();
        $currencyId = $traderOrder->getCurrencyId();

        try {
            $this->logger->info('处理交易员开单事件', [
                'trader_id' => $traderId,
                'order_id' => $traderOrder->getId(),
                'currency_id' => $currencyId,
                'direction' => $traderOrder->getDirection(),
                'amount' => $traderOrder->getAmount(),
                'price' => $traderOrder->getPrice()
            ]);

            $followers = $this->getActiveFollowers($traderId);
            
            if (empty($followers)) {
                $this->logger->info('交易员无活跃跟单用户', ['trader_id' => $traderId]);
                return;
            }

            $this->logger->info('找到跟单用户', [
                'trader_id' => $traderId,
                'followers_count' => count($followers)
            ]);

            $copyOrders = [];
            $failedCopies = [];

            foreach ($followers as $follower) {
                try {
                    $copyResult = $this->processSingleFollower($traderOrder, $follower, $currencyId);

                    if ($copyResult['success']) {
                        // 直接创建跟单订单，而不是收集数据
                        $createResult = $this->copyTradeService->createSingleCopyOrder($copyResult['order_data'], $traderOrder);

                        if ($createResult['success']) {
                            $copyOrders[] = $createResult;
                        } else {
                            $failedCopies[] = [
                                'follower_id' => $follower['user_id'],
                                'reason' => $createResult['error'] ?? 'create_order_failed'
                            ];
                        }
                    } else {
                        $failedCopies[] = [
                            'follower_id' => $follower['user_id'],
                            'reason' => $copyResult['reason']
                        ];
                    }
                } catch (\Exception $e) {
                    $this->logger->error('处理单个跟单用户失败', [
                        'trader_id' => $traderId,
                        'follower_id' => $follower['user_id'],
                        'error' => $e->getMessage()
                    ]);

                    $failedCopies[] = [
                        'follower_id' => $follower['user_id'],
                        'reason' => 'processing_error'
                    ];
                }
            }

            if (!empty($copyOrders)) {
                $this->logger->info('跟单订单创建完成', [
                    'trader_order_id' => $traderOrder->getId(),
                    'success_count' => count($copyOrders),
                    'failed_count' => count($failedCopies)
                ]);
            }

            if (!empty($failedCopies)) {
                $this->logger->warning('部分跟单失败', [
                    'trader_order_id' => $traderOrder->getId(),
                    'failed_copies' => $failedCopies
                ]);
            }

        } catch (\Exception $e) {
            $this->logger->error('处理交易员开单事件失败', [
                'trader_id' => $traderId,
                'order_id' => $traderOrder->getId(),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
        }
    }

    private function getActiveFollowers(int $traderId): array
    {
        $cacheKey = CopySpotCacheKey::getFollowersKey($traderId);
        
        $cached = $this->redis->get($cacheKey);
        if ($cached) {
            return json_decode($cached, true) ?: [];
        }

        $followers = CopySpotUserSetting::where(CopySpotUserSetting::FIELD_EXPERT_USER_ID, $traderId)
            ->where(CopySpotUserSetting::FIELD_STATUS, CopyStatus::FOLLOWING)
            ->select([
                CopySpotUserSetting::FIELD_FOLLOWER_USER_ID . ' as user_id',
                CopySpotUserSetting::FIELD_EXPERT_ID . ' as expert_id',
                CopySpotUserSetting::FIELD_COPY_TYPE . ' as copy_type',
                CopySpotUserSetting::FIELD_FIXED_AMOUNT . ' as fixed_amount',
                CopySpotUserSetting::FIELD_RATE . ' as rate',
                CopySpotUserSetting::FIELD_MAX_FOLLOW_AMOUNT . ' as max_follow_amount',
                CopySpotUserSetting::FIELD_AUTO_NEW_PAIRS . ' as auto_new_pairs',
                CopySpotUserSetting::FIELD_COPY_CURRENCIES . ' as copy_currencies'
            ])
            ->get()
            ->toArray();

        $this->redis->setex($cacheKey, self::CACHE_TTL, json_encode($followers));
        return $followers;
    }

    private function processSingleFollower(
        $traderOrder,
        array $follower,
        int $currencyId
    ): array {
        if (!$this->shouldCopyCurrency($follower, $currencyId)) {
            return [
                'success' => false,
                'reason' => 'currency_not_allowed'
            ];
        }

        $userSetting = $this->buildUserSetting($follower);
        $advancedSetting = $this->getAdvancedSetting($follower['user_id'], $follower['expert_id'], $currencyId);

        $calculation = $this->calculationService->calculateCopyAmount(
            $traderOrder,
            $userSetting,
            $advancedSetting
        );

        if (!$calculation['can_copy']) {
            return [
                'success' => false,
                'reason' => $calculation['reason']
            ];
        }

        // 准备现货交易服务需要的订单数据格式
        return [
            'success' => true,
            'order_data' => [
                'follower_user_id' => $follower['user_id'],
                'expert_id' => $follower['expert_id'],
                'currency_id' => $currencyId,
                'side' => $traderOrder->getDirection() === intval(TradeSide::BUY_INT->value) ? 'buy' : 'sell',
                'type' => $traderOrder->getOrderType() === intval(OrderType::LIMIT_INT->value) ? 'limit' : 'market',
                'price' => (string)$traderOrder->getPrice(),
                'quantity' => $calculation['copy_quantity'],
                'copy_order_id' => $traderOrder->getId(),
                'copy_amount' => $calculation['copy_amount'],
                'copy_type' => $calculation['copy_type'] ?? 'unknown'
            ]
        ];
    }

    private function shouldCopyCurrency(array $follower, int $currencyId): bool
    {
        $copyCurrencies = $follower['copy_currencies'];
        
        if (empty($copyCurrencies)) {
            return (bool)$follower['auto_new_pairs'];
        }

        return in_array($currencyId, $copyCurrencies, true);
    }

    private function buildUserSetting(array $follower): CopySpotUserSetting
    {
        $setting = new CopySpotUserSetting();
        $setting->follower_user_id = $follower['user_id'];
        $setting->expert_id = $follower['expert_id'];
        $setting->copy_type = $follower['copy_type'];
        $setting->fixed_amount = $follower['fixed_amount'];
        $setting->rate = $follower['rate'];
        $setting->max_follow_amount = $follower['max_follow_amount'];
        $setting->auto_new_pairs = $follower['auto_new_pairs'];
        $setting->copy_currencies = $follower['copy_currencies'];
        $setting->status = CopyStatus::FOLLOWING;

        return $setting;
    }

    private function getAdvancedSetting(int $followerUserId, int $expertId, int $currencyId): ?CopySpotAdvancedSetting
    {
        $cacheKey = CopySpotCacheKey::getAdvancedSettingKey($followerUserId, $expertId, $currencyId);
        
        $cached = $this->redis->get($cacheKey);
        if ($cached) {
            $data = json_decode($cached, true);
            if (empty($data)) {
                return null;
            }
            
            $setting = new CopySpotAdvancedSetting();
            $setting->fill($data);
            return $setting;
        }

        $setting = CopySpotAdvancedSetting::where(CopySpotAdvancedSetting::FIELD_FOLLOWER_USER_ID, $followerUserId)
            ->where(CopySpotAdvancedSetting::FIELD_EXPERT_ID, $expertId)
            ->where(CopySpotAdvancedSetting::FIELD_CURRENCY_ID, $currencyId)
            ->first();

        $cacheData = $setting ? $setting->toArray() : [];
        $this->redis->setex($cacheKey, self::CACHE_TTL, json_encode($cacheData));
        
        return $setting;
    }
}
