<?php

declare(strict_types=1);

namespace App\Listener\Copy;

use App\Event\Trade\TraderSpotOrderFilledEvent;
use App\Http\Api\Service\V1\Copy\CopySpotTradeService;
use App\Http\Api\Service\V1\Copy\CopySpotPositionService;
use App\Model\Copy\CopySpotExpert;
use App\Model\Copy\Enums\ExpertStatus;
use App\Service\UserAccounts\UserAccountsAssetService;
use App\Model\Enums\User\AccountType;
use Hyperf\Context\ApplicationContext;
use Hyperf\Event\Annotation\Listener;
use Hyperf\Event\Contract\ListenerInterface;
use Psr\Log\LoggerInterface;

#[Listener]
class TraderSpotOrderFilledListener implements ListenerInterface
{
    private CopySpotTradeService $copyTradeService;

    private CopySpotPositionService $positionService;

    private UserAccountsAssetService $assetService;

    private LoggerInterface $logger;

    private const DEFAULT_PRECISION = 8;

    public function listen(): array
    {
        return [
            TraderSpotOrderFilledEvent::class,
        ];
    }

    public function process(object $event): void
    {
        if (!$event instanceof TraderSpotOrderFilledEvent) {
            return;
        }

        $this->copyTradeService = ApplicationContext::getContainer()->get(CopySpotTradeService::class);
        $this->positionService = ApplicationContext::getContainer()->get(CopySpotPositionService::class);
        $this->assetService = ApplicationContext::getContainer()->get(UserAccountsAssetService::class);
        $this->logger = logger('现货跟单交易', 'copyTrade/spot_copy_trade.log');

        $traderOrder = $event->getOrder();
        $filledAmount = $event->getFilledAmount();
        $filledPrice = $event->getFilledPrice();

        try {
            $this->logger->info('处理交易员订单成交事件', [
                'trader_id' => $traderOrder->getUserId(),
                'order_id' => $traderOrder->getId(),
                'filled_amount' => $filledAmount,
                'filled_price' => $filledPrice,
                'direction' => $traderOrder->getDirection()
            ]);

            if (!$this->isClosingPosition($traderOrder)) {
                $this->logger->info('非平仓操作，跳过处理', [
                    'order_id' => $traderOrder->getId(),
                    'direction' => $traderOrder->getDirection()
                ]);
                return;
            }

            // 1. 获取交易员的expert_id
            $expert = CopySpotExpert::where('user_id', $traderOrder->getUserId())
                ->where('status', ExpertStatus::APPROVED)
                ->where('is_active', true)
                ->first();
            if (!$expert) {
                $this->logger->warning('交易员不存在有效的expert记录', [
                    'trader_user_id' => $traderOrder->getUserId()
                ]);
                return;
            }

            $expertId = $expert->id;

            // 2. 获取交易员自己的持仓记录
            $traderPosition = $this->positionService->getPosition(
                $traderOrder->getUserId(), // follower_user_id = 交易员自己
                $expertId,                 // expert_id = 交易员的expert_id
                $traderOrder->getCurrencyId()
            );

            // 3. 计算平仓比例
            $closeRatio = '1'; // 默认100%平仓

            if ($traderPosition) {
                $traderAvailableQuantity = (string)$traderPosition->getAvailableQuantity();
                // 计算卖出前的持仓数量 = 当前剩余数量 + 本次卖出数量
                $traderQuantityBeforeSell = bcadd($traderAvailableQuantity, '0', self::DEFAULT_PRECISION);

                if (bccomp($traderQuantityBeforeSell, '0', self::DEFAULT_PRECISION) > 0) {
                    // 平仓比例 = 本次卖出数量 / 卖出前的持仓数量
                    $closeRatio = bcdiv($filledAmount, $traderQuantityBeforeSell, self::DEFAULT_PRECISION);

                }
            }

            // 4. 查询该交易员的跟单持仓
            $positions = $this->positionService->getPositionsByExpertAndCurrency(
                $expert->user_id,
                $traderOrder->getCurrencyId()
            );

            if (empty($positions)) {
                $this->logger->warning('未找到对应的跟单持仓', [
                    'expert_id' => $expertId,
                    'currency_id' => $traderOrder->getCurrencyId(),
                    'trader_user_id' => $traderOrder->getUserId()
                ]);
                return;
            }


            $successCount = 0;
            $failedCount = 0;

            foreach ($positions as $position) {
                try {
                    // 计算跟单用户应该平仓的数量
                    $followerPositionQuantity = (string)$position['available_quantity'];

                    if (bccomp($closeRatio, '1', self::DEFAULT_PRECISION) >= 0) {
                        // 100%平仓：卖掉所有可用数量
                        $followerCloseQuantity = $followerPositionQuantity;
                    } else {
                        // 按比例平仓
                        $followerCloseQuantity = bcmul($followerPositionQuantity, $closeRatio, self::DEFAULT_PRECISION);
                    }

                    $result = $this->processFollowerCloseFromPosition(
                        $position,
                        $traderOrder,
                        $followerCloseQuantity,
                        $filledPrice
                    );

                    if ($result['success']) {
                        $successCount++;
                    } else {
                        $failedCount++;
                    }

                } catch (\Exception $e) {
                    $this->logger->error('处理跟单用户平仓失败', [
                        'position_id' => $position['id'],
                        'follower_id' => $position['follower_user_id'],
                        'error' => $e->getMessage()
                    ]);
                    $failedCount++;
                }
            }

            $this->logger->info('平仓跟随处理完成', [
                'trader_order_id' => $traderOrder->getId(),
                'total' => count($positions),
                'success' => $successCount,
                'failed' => $failedCount
            ]);

        } catch (\Exception $e) {
            $this->logger->error('处理交易员成交事件失败', [
                'trader_id' => $traderOrder->getUserId(),
                'order_id' => $traderOrder->getId(),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
        }
    }

    private function isClosingPosition($traderOrder): bool
    {
        return $traderOrder->getDirection() === -1; // 卖出为平仓
    }

    private function processFollowerCloseFromPosition(
        array $position,
        $traderOrder,
        string $followerCloseQuantity,
        string $filledPrice
    ): array {
        $followerUserId = $position['follower_user_id'];
        $expertId = $position['expert_id'];

        if (bccomp($followerCloseQuantity, '0', self::DEFAULT_PRECISION) <= 0) {
            return [
                'success' => false,
                'reason' => 'invalid_close_quantity'
            ];
        }

        // 验证用户实际余额
        $followerBalance = $this->getFollowerBalance($followerUserId, $traderOrder->getCurrencyId());

        // 如果实际余额不足，使用实际余额
        if (bccomp($followerBalance, $followerCloseQuantity, self::DEFAULT_PRECISION) < 0) {
            $followerCloseQuantity = $followerBalance;

            if (bccomp($followerCloseQuantity, '0', self::DEFAULT_PRECISION) <= 0) {
                return [
                    'success' => false,
                    'reason' => 'insufficient_balance'
                ];
            }
        }

        try {
            $result = $this->copyTradeService->createCopyCloseOrder(
                $followerUserId,
                $expertId,
                $traderOrder,
                $followerCloseQuantity,
                $filledPrice
            );

            $this->logger->info('创建平仓跟单订单成功', [
                'follower_id' => $followerUserId,
                'trader_order_id' => $traderOrder->getId(),
                'close_quantity' => $followerCloseQuantity,
                'close_price' => $filledPrice
            ]);

            return [
                'success' => true,
                'close_order_id' => $result['close_order_id'],
                'close_quantity' => $followerCloseQuantity
            ];

        } catch (\Exception $e) {
            $this->logger->error('创建平仓跟单订单失败', [
                'follower_id' => $followerUserId,
                'trader_order_id' => $traderOrder->getId(),
                'error' => $e->getMessage()
            ]);

            return [
                'success' => false,
                'reason' => 'create_order_failed',
                'error' => $e->getMessage()
            ];
        }
    }

    private function getFollowerBalance(int $userId, int $currencyId): string
    {
        try {
            $userAsset = $this->assetService->getUserAsset($userId, AccountType::SPOT->value, $currencyId);
            return $userAsset ? (string)$userAsset->available : '0';
        } catch (\Exception $e) {
            $this->logger->error('获取用户余额失败', [
                'user_id' => $userId,
                'currency_id' => $currencyId,
                'error' => $e->getMessage()
            ]);
            return '0';
        }
    }
}
