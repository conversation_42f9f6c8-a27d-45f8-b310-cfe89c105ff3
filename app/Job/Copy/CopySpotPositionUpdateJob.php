<?php

declare(strict_types=1);

namespace App\Job\Copy;

use App\Http\Api\Service\V1\Copy\CopySpotPositionService;
use App\Model\Copy\CopySpotOrder;
use App\Model\Copy\CopySpotExpert;
use App\Model\Copy\Enums\ExpertStatus;
use App\Model\Trade\TradeSpotOrder;
use Hyperf\AsyncQueue\Job;
use Hyperf\Context\ApplicationContext;
use Psr\Log\LoggerInterface;

class CopySpotPositionUpdateJob extends Job
{
    public function __construct(
        private array $data
    ) {
    }

    public function handle(): void
    {
        $logger = ApplicationContext::getContainer()->get(LoggerInterface::class);
        $positionService = ApplicationContext::getContainer()->get(CopySpotPositionService::class);

        try {
            $spotOrderId = $this->data['spot_order_id'];
            $tradeType = $this->data['trade_type']; // 'buy' or 'sell'
            $currencyId = $this->data['currency_id'];
            $price = $this->data['price'];
            $quantity = $this->data['quantity'];

            $logger->info('开始处理持仓更新任务', [
                'spot_order_id' => $spotOrderId,
                'trade_type' => $tradeType,
                'currency_id' => $currencyId,
                'price' => $price,
                'quantity' => $quantity
            ]);

            // 获取现货订单信息
            $spotOrder = TradeSpotOrder::find($spotOrderId);
            if (!$spotOrder) {
                $logger->error('现货订单不存在', ['spot_order_id' => $spotOrderId]);
                return;
            }

            $isTrader = (int)$spotOrder->is_trader === 1;
            $isCopyOrder = !empty($spotOrder->copy_order_id);

            // 1. 如果是交易员订单，维护交易员自己的持仓
            if ($isTrader) {
                $this->handleTraderPosition($spotOrder, $currencyId, $price, $quantity, $tradeType, $logger, $positionService);
            }

            // 2. 如果是跟单订单，维护跟单用户持仓
            if ($isCopyOrder) {
                $this->handleFollowerPosition($spotOrder, $currencyId, $price, $quantity, $tradeType, $logger, $positionService);
            }

            if (!$isTrader && !$isCopyOrder) {
                $logger->info('既非交易员订单也非跟单订单，跳过持仓更新', ['spot_order_id' => $spotOrderId]);
                return;
            }

            $logger->info('持仓更新任务完成', [
                'spot_order_id' => $spotOrderId,
                'trade_type' => $tradeType,
                'currency_id' => $currencyId,
                'is_trader' => (int)$spotOrder->is_trader,
                'has_copy_order' => !empty($spotOrder->copy_order_id)
            ]);

        } catch (\Exception $e) {
            $logger->error('持仓更新任务失败', [
                'data' => $this->data,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            throw $e; // 重新抛出异常，让队列系统处理重试
        }
    }

    private function handleTraderPosition($spotOrder, $currencyId, $price, $quantity, $tradeType, $logger, $positionService): void
    {
        $traderId = $spotOrder->user_id;

        // 获取交易员的expert_id
        $expert = CopySpotExpert::where('user_id', $traderId)
            ->where('status', ExpertStatus::APPROVED)
            ->where('is_active', true)
            ->first();
        if (!$expert) {
            $logger->warning('交易员不存在有效的expert记录', ['trader_user_id' => $traderId]);
            return;
        }

        $expertId = $expert->id;

        if ($tradeType === 'buy') {
            $positionService->updatePositionForBuy(
                $traderId,    // follower_user_id = 交易员自己
                $expertId,    // expert_id = 交易员的expert_id
                $traderId,    // expert_user_id = 交易员自己
                $currencyId,
                $price,
                $quantity
            );
        } elseif ($tradeType === 'sell') {
            $positionService->updatePositionForSell(
                $traderId,    // follower_user_id = 交易员自己
                $expertId,    // expert_id = 交易员的expert_id
                $currencyId,
                $price,
                $quantity
            );
        }

        $logger->info('交易员持仓更新完成', [
            'trader_user_id' => $traderId,
            'expert_id' => $expertId,
            'currency_id' => $currencyId,
            'trade_type' => $tradeType
        ]);
    }

    private function handleFollowerPosition($spotOrder, $currencyId, $price, $quantity, $tradeType, $logger, $positionService): void
    {
        // 获取跟单记录 - 通过follower_order_id查询
        $copyOrder = CopySpotOrder::where(CopySpotOrder::FIELD_FOLLOWER_ORDER_ID, $spotOrder->id)->first();
        if (!$copyOrder) {
            $logger->error('跟单记录不存在', [
                'spot_order_id' => $spotOrder->id,
                'copy_order_id' => $spotOrder->copy_order_id
            ]);
            return;
        }

        $followerUserId = $copyOrder->follower_user_id;
        $expertId = $copyOrder->expert_id;
        $expertUserId = $copyOrder->expert_user_id;

        if ($tradeType === 'buy') {
            $positionService->updatePositionForBuy(
                $followerUserId,
                $expertId,
                $expertUserId,
                $currencyId,
                $price,
                $quantity
            );
        } elseif ($tradeType === 'sell') {
            $positionService->updatePositionForSell(
                $followerUserId,
                $expertId,
                $currencyId,
                $price,
                $quantity
            );
        }

        $logger->info('跟单用户持仓更新完成', [
            'follower_user_id' => $followerUserId,
            'expert_id' => $expertId,
            'currency_id' => $currencyId,
            'trade_type' => $tradeType
        ]);
    }
}
