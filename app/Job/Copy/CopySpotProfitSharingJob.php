<?php

declare(strict_types=1);

namespace App\Job\Copy;

use App\Http\Api\Service\V1\Copy\CopySpotPositionService;
use App\Model\Copy\CopySpotExpert;
use App\Model\Enums\User\AccountType;
use App\Model\Enums\User\FlowsType;
use App\Service\UserAccounts\UserAccountsAssetService;
use Hyperf\AsyncQueue\Job;
use Hyperf\Context\ApplicationContext;
use Hyperf\DbConnection\Db;
use Psr\Log\LoggerInterface;

class CopySpotProfitSharingJob extends Job
{
    public function __construct(
        private array $data
    ) {
    }

    public function handle(): void
    {
        $logger = ApplicationContext::getContainer()->get(LoggerInterface::class);
        $assetService = ApplicationContext::getContainer()->get(UserAccountsAssetService::class);

        try {
            $followerUserId = $this->data['follower_user_id'];
            $expertUserId = $this->data['expert_user_id'];
            $currencyId = $this->data['currency_id'];
            $realizedPnl = $this->data['realized_pnl'];
            $positionId = $this->data['position_id'];

            $logger->info('开始处理现货分润任务', [
                'follower_user_id' => $followerUserId,
                'expert_user_id' => $expertUserId,
                'currency_id' => $currencyId,
                'realized_pnl' => $realizedPnl,
                'position_id' => $positionId
            ]);

            // 检查是否有盈利
            if (bccomp($realizedPnl, '0', 8) <= 0) {
                $logger->info('无盈利，跳过分润', [
                    'realized_pnl' => $realizedPnl,
                    'position_id' => $positionId
                ]);
                return;
            }

            // 查询交易员的分润比例
            $expert = CopySpotExpert::where('user_id', $expertUserId)->first();
            if (!$expert) {
                $logger->error('交易员expert记录不存在', [
                    'expert_user_id' => $expertUserId,
                    'position_id' => $positionId
                ]);
                return;
            }

            $profitSharingRate = (string)$expert->profit_sharing_rate;
            if (bccomp($profitSharingRate, '0', 8) <= 0) {
                $logger->info('分润比例为0，跳过分润', [
                    'expert_user_id' => $expertUserId,
                    'profit_sharing_rate' => $profitSharingRate,
                    'position_id' => $positionId
                ]);
                return;
            }

            // 计算分润金额
            $profitSharingAmount = bcmul($realizedPnl, $profitSharingRate, 8);

            $logger->info('计算分润金额', [
                'realized_pnl' => $realizedPnl,
                'profit_sharing_rate' => $profitSharingRate,
                'profit_sharing_amount' => $profitSharingAmount,
                'position_id' => $positionId
            ]);

            // 执行分润转账
            $this->executeProfitSharing(
                $followerUserId,
                $expertUserId,
                $currencyId,
                $profitSharingAmount,
                $positionId,
                $assetService,
                $logger
            );

            $logger->info('现货分润任务完成', [
                'follower_user_id' => $followerUserId,
                'expert_user_id' => $expertUserId,
                'currency_id' => $currencyId,
                'profit_sharing_amount' => $profitSharingAmount,
                'position_id' => $positionId
            ]);

        } catch (\Exception $e) {
            $logger->error('现货分润任务失败', [
                'data' => $this->data,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            throw $e;
        }
    }

    private function executeProfitSharing(
        int $followerUserId,
        int $expertUserId,
        int $currencyId,
        string $profitSharingAmount,
        int $positionId,
        UserAccountsAssetService $assetService,
        LoggerInterface $logger
    ): void {
        Db::transaction(function () use (
            $followerUserId,
            $expertUserId,
            $currencyId,
            $profitSharingAmount,
            $positionId,
            $assetService,
            $logger
        ) {
            // 1. 从跟单用户现货账户扣除分润金额
            $deductResult = $assetService->deductAvailableAsset(
                $followerUserId,
                AccountType::SPOT->value,
                $currencyId,
                floatval($profitSharingAmount),
                FlowsType::COPY_SPOT_PROFIT_SHARING_OUT->value,
                intval($positionId)
            );

            if (!$deductResult) {
                throw new \RuntimeException('扣除跟单用户分润金额失败');
            }

            $logger->info('扣除跟单用户分润金额成功', [
                'follower_user_id' => $followerUserId,
                'currency_id' => $currencyId,
                'amount' => $profitSharingAmount,
                'position_id' => $positionId
            ]);

            // 2. 向交易员现货账户增加分润金额
            $addResult = $assetService->addAvailableAsset(
                $expertUserId,
                AccountType::SPOT->value,
                $currencyId,
                floatval($profitSharingAmount),
                FlowsType::COPY_SPOT_PROFIT_SHARING_IN->value,
                $positionId
            );

            if (!$addResult) {
                throw new \RuntimeException('增加交易员分润金额失败');
            }

            $logger->info('增加交易员分润金额成功', [
                'expert_user_id' => $expertUserId,
                'currency_id' => $currencyId,
                'amount' => $profitSharingAmount,
                'position_id' => $positionId
            ]);
        });
    }
}
