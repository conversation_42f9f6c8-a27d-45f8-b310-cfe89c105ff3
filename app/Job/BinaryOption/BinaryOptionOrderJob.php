<?php

/**
 * BinaryOptionOrderJob.php
 * Author    chenmaq (<EMAIL>)
 * Version   1.0
 * Date      2025/7/18
 * @link     bbbtrade.net
 * @document bbbtrade.net
 */

namespace App\Job\BinaryOption;

use App\Http\Api\Service\V1\Binary\BinaryOptionService;
use Hyperf\AsyncQueue\Job;
use Hyperf\Context\ApplicationContext;
use Psr\Log\LoggerInterface;

class BinaryOptionOrderJob extends Job
{
    public string|int $uniqid;

    /**
     * 订单ID
     */
    public int $optionOrder;

    public function __construct(int $optionOrder)
    {
        $this->optionOrder = $optionOrder;
        $this->uniqid = uniqid();
    }

    public function handle()
    {
        $container = ApplicationContext::getContainer();
        $binaryOptionService = $container->get(BinaryOptionService::class);
        $logger = $container->get(LoggerInterface::class);

        try {
            $logger->info('开始处理二元期权订单结算', [
                'order_id' => $this->optionOrder,
                'uniqid' => $this->uniqid
            ]);

            // 执行订单结算
            $result = $binaryOptionService->settleOrder($this->optionOrder);

            if ($result) {
                $logger->info('二元期权订单结算成功', [
                    'order_id' => $this->optionOrder,
                    'uniqid' => $this->uniqid
                ]);
            } else {
                $logger->warning('二元期权订单结算失败', [
                    'order_id' => $this->optionOrder,
                    'uniqid' => $this->uniqid,
                    'reason' => '订单可能已被处理或不存在'
                ]);
            }

        } catch (\Throwable $e) {
            $logger->error('二元期权订单结算异常', [
                'order_id' => $this->optionOrder,
                'uniqid' => $this->uniqid,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            // 重新抛出异常，让异步队列处理重试逻辑
            throw $e;
        }
    }

    /**
     * 获取任务标识
     */
    public function getJobId(): string
    {
        return "binary_option_settlement_{$this->optionOrder}_{$this->uniqid}";
    }

    /**
     * 设置最大重试次数
     */
    public function getMaxAttempts(): int
    {
        return 3;
    }

    /**
     * 设置重试延迟时间(秒)
     */
    public function getRetrySeconds(): array
    {
        return [10, 30, 60]; // 第1次重试延迟10秒，第2次30秒，第3次60秒
    }
}