<?php

/**
 * TraderPerpetualProfitSharingJob.php
 * Author    chenmaq (<EMAIL>)
 * Version   1.0
 * Date      2025/7/17
 * @link     bbbtrade.net
 * @document bbbtrade.net
 */

namespace App\Job\Contract\Trader;

use App\Enum\CurrencyConfigKey;
use App\Model\Copy\CopyContractPosition;
use App\Model\Copy\CopyContractProfitSharing;
use App\Model\Enums\User\AccountType;
use App\Model\Enums\User\FlowsType;
use App\Service\UserAccounts\UserAccountsAssetService;
use Hyperf\AsyncQueue\Job;
use Hyperf\Context\ApplicationContext;

/**
 * 交易员分润异步任务
 */
class TraderPerpetualProfitSharingJob extends Job
{
    public function __construct(
        public int $position_id
    ){}

    public function handle()
    {
        $this->ProfitSharing();
    }

    protected function ProfitSharing()
    {
        try {
            $position = CopyContractPosition::query()
                ->leftJoin("trade_perpetual_position","trade_perpetual_position.id", "=", "copy_contract_position.follower_position_id")
                ->select([
                    "copy_contract_position.*",
                    "trade_perpetual_position.status as pstatus",
                    "trade_perpetual_position.is_copy",
                    "trade_perpetual_position.realized_pnl",
                    "trade_perpetual_position.currency_id",
                    "trade_perpetual_position.total_charge"
                ])
                ->where('copy_contract_position.follower_position_id', $this->position_id)
                ->first();
            if($position){
                $position = $position->toArray();
            }else{
                return;
            }
            if($position['realized_pnl'] <= 0 || $position['profit_sharing_rate'] <= 0){
                return;
            }

            $availableAmount = bcsub(strval($position['realized_pnl']),strval($position['total_charge']),8);

            $share_amount = bcmul($availableAmount , strval($position['profit_sharing_rate']),8);

            if((float)$share_amount <= 0){
                return;
            }

            $logger = logger('跟单分润','copyTrade/copy_profit_sharing.log');

            $assetsService = ApplicationContext::getContainer()->get(UserAccountsAssetService::class);

            $quote_currency_id = redis('default')->hGet(CurrencyConfigKey::getCurrencyKey($position['currency_id']),'quote_assets_id') ?? 1273;

            $result = $assetsService->deductAvailableAsset(
                (int)$position['follower_user_id'],
                AccountType::COPY->value,
                (int)$quote_currency_id,
                (float)$share_amount,
                FlowsType::PERPETUAL_COPY_FEE->value,
                $this->position_id
            );

            if($result){
                $result = $assetsService->addAvailableAsset(
                    (int)$position['expert_id'],
                    AccountType::WALLET->value,
                    (int)$quote_currency_id,
                    (float)$share_amount,
                    FlowsType::PERPETUAL_PROFIT_SHARING->value,
                    $this->position_id,
                );
            }
            if($result){
                CopyContractProfitSharing::create([
                    'expert_id' => $position['expert_id'],
                    'expert_user_id' => $position['expert_id'],
                    'follower_user_id' => $position['follower_user_id'],
                    'copy_position_id' => $this->position_id,
                    'expert_position_id' => $position['expert_position_id'],
                    'follower_position_id' => $position['follower_position_id'],
                    'profit' => $position['realized_pnl'],
                    'profit_sharing' => $share_amount,
                    'profit_sharing_rate' => $position['profit_sharing_rate'],
                    'currency_id' => $quote_currency_id,
                ])->save();

                $logger->info("仓位{$this->position_id}分润执行成功");
                return;
            }
            $logger->error("仓位{$this->position_id}分润执行失败");

        }catch (\Throwable $t){
            $logger->error(
                "仓位{$this->position_id}分润执行失败：".$t->getMessage()
            );
        }
    }
}