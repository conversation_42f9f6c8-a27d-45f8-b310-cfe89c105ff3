<?php

declare(strict_types=1);

namespace App\Model\Binary;

use Hyperf\DbConnection\Model\Model;

/**
 * @property int $id 
 * @property string $order_no 订单号
 * @property int $user_id 用户ID
 * @property int $currency_id 币种ID
 * @property int $time_period 时间周期(秒)
 * @property int $direction 方向:1看涨,2看跌
 * @property float $invest_amount 投资金额
 * @property int $price_source 价格来源:1现货,2合约
 * @property float $open_price 开仓价格
 * @property float $close_price 结算价格
 * @property float $win_rate 盈利比例
 * @property float $lose_rate 亏损比例
 * @property float $payout_amount 结算金额(正数盈利,负数亏损)
 * @property float $fee_amount 手续费
 * @property int $status 状态:1等待结算,2盈利,3亏损,4平局,5已取消
 * @property string $open_time 开仓时间
 * @property \Carbon\Carbon $expire_time 到期时间
 * @property \Carbon\Carbon $settle_time 结算时间
 * @property string $remark 备注
 * @property \Carbon\Carbon $created_at 
 * @property \Carbon\Carbon $updated_at 
 */
class BinaryOptionOrder extends Model
{
    /**
     * 
     */
    public const FIELD_ID = 'id';
    /**
     * 订单号
     */
    public const FIELD_ORDER_NO = 'order_no';
    /**
     * 用户ID
     */
    public const FIELD_USER_ID = 'user_id';
    /**
     * 币种ID
     */
    public const FIELD_CURRENCY_ID = 'currency_id';
    /**
     * 时间周期(秒)
     */
    public const FIELD_TIME_PERIOD = 'time_period';
    /**
     * 方向:1看涨,2看跌
     */
    public const FIELD_DIRECTION = 'direction';
    /**
     * 投资金额
     */
    public const FIELD_INVEST_AMOUNT = 'invest_amount';
    /**
     * 价格来源:1现货,2合约
     */
    public const FIELD_PRICE_SOURCE = 'price_source';
    /**
     * 开仓价格
     */
    public const FIELD_OPEN_PRICE = 'open_price';
    /**
     * 结算价格
     */
    public const FIELD_CLOSE_PRICE = 'close_price';
    /**
     * 盈利比例
     */
    public const FIELD_WIN_RATE = 'win_rate';
    /**
     * 亏损比例
     */
    public const FIELD_LOSE_RATE = 'lose_rate';
    /**
     * 结算金额(正数盈利,负数亏损)
     */
    public const FIELD_PAYOUT_AMOUNT = 'payout_amount';
    /**
     * 手续费
     */
    public const FIELD_FEE_AMOUNT = 'fee_amount';
    /**
     * 状态:1等待结算,2盈利,3亏损,4平局,5已取消
     */
    public const FIELD_STATUS = 'status';
    /**
     * 开仓时间
     */
    public const FIELD_OPEN_TIME = 'open_time';
    /**
     * 到期时间
     */
    public const FIELD_EXPIRE_TIME = 'expire_time';
    /**
     * 结算时间
     */
    public const FIELD_SETTLE_TIME = 'settle_time';
    /**
     * 备注
     */
    public const FIELD_REMARK = 'remark';
    /**
     * 创建时间
     */
    public const FIELD_CREATED_AT = 'created_at';
    /**
     * 更新时间
     */
    public const FIELD_UPDATED_AT = 'updated_at';
    /**
     * The table associated with the model.
     */
    protected ?string $table = 'binary_option_orders';

    /**
     * The attributes that are mass assignable.
     */
    protected array $fillable = ['id', 'order_no', 'user_id', 'currency_id', 'time_period', 'direction', 'invest_amount', 'price_source', 'open_price', 'close_price', 'win_rate', 'lose_rate', 'payout_amount', 'fee_amount', 'status', 'open_time', 'expire_time', 'settle_time', 'remark', 'created_at', 'updated_at'];

    /**
     * The attributes that should be cast to native types.
     */
    protected array $casts = [
        'id' => 'integer',
        // 用户ID
        'user_id' => 'integer',
        // 币种ID
        'currency_id' => 'integer',
        // 时间周期(秒)
        'time_period' => 'integer',
        // 方向:1看涨,2看跌
        'direction' => 'integer',
        // 投资金额
        'invest_amount' => 'float',
        // 价格来源:1现货,2合约
        'price_source' => 'integer',
        // 开仓价格
        'open_price' => 'float',
        // 结算价格
        'close_price' => 'float',
        // 盈利比例
        'win_rate' => 'float',
        // 亏损比例
        'lose_rate' => 'float',
        // 结算金额(正数盈利,负数亏损)
        'payout_amount' => 'float',
        // 手续费
        'fee_amount' => 'float',
        // 状态:1等待结算,2盈利,3亏损,4平局,5已取消
        'status' => 'integer',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
        'expire_time' => 'datetime',
        'settle_time' => 'datetime'
    ];
    public function getId() : int
    {
        return $this->id;
    }
    public function setId($value) : object
    {
        $this->id = $value;
        return $this;
    }
    public function getOrderNo() : string
    {
        return $this->order_no;
    }
    public function setOrderNo($value) : object
    {
        $this->order_no = $value;
        return $this;
    }
    public function getUserId() : int
    {
        return $this->user_id;
    }
    public function setUserId($value) : object
    {
        $this->user_id = $value;
        return $this;
    }
    public function getCurrencyId() : int
    {
        return $this->currency_id;
    }
    public function setCurrencyId($value) : object
    {
        $this->currency_id = $value;
        return $this;
    }
    public function getTimePeriod() : int
    {
        return $this->time_period;
    }
    public function setTimePeriod($value) : object
    {
        $this->time_period = $value;
        return $this;
    }
    public function getDirection() : int
    {
        return $this->direction;
    }
    public function setDirection($value) : object
    {
        $this->direction = $value;
        return $this;
    }
    public function getInvestAmount() : float
    {
        return $this->invest_amount;
    }
    public function setInvestAmount($value) : object
    {
        $this->invest_amount = $value;
        return $this;
    }
    public function getPriceSource() : int
    {
        return $this->price_source;
    }
    public function setPriceSource($value) : object
    {
        $this->price_source = $value;
        return $this;
    }
    public function getOpenPrice() : float
    {
        return $this->open_price;
    }
    public function setOpenPrice($value) : object
    {
        $this->open_price = $value;
        return $this;
    }
    public function getClosePrice() : float
    {
        return $this->close_price;
    }
    public function setClosePrice($value) : object
    {
        $this->close_price = $value;
        return $this;
    }
    public function getWinRate() : float
    {
        return $this->win_rate;
    }
    public function setWinRate($value) : object
    {
        $this->win_rate = $value;
        return $this;
    }
    public function getLoseRate() : float
    {
        return $this->lose_rate;
    }
    public function setLoseRate($value) : object
    {
        $this->lose_rate = $value;
        return $this;
    }
    public function getPayoutAmount() : float
    {
        return $this->payout_amount;
    }
    public function setPayoutAmount($value) : object
    {
        $this->payout_amount = $value;
        return $this;
    }
    public function getFeeAmount() : float
    {
        return $this->fee_amount;
    }
    public function setFeeAmount($value) : object
    {
        $this->fee_amount = $value;
        return $this;
    }
    public function getStatus() : int
    {
        return $this->status;
    }
    public function setStatus($value) : object
    {
        $this->status = $value;
        return $this;
    }
    public function getOpenTime() : mixed
    {
        return $this->open_time;
    }
    public function setOpenTime($value) : object
    {
        $this->open_time = $value;
        return $this;
    }
    public function getExpireTime() : mixed
    {
        return $this->expire_time;
    }
    public function setExpireTime($value) : object
    {
        $this->expire_time = $value;
        return $this;
    }
    public function getSettleTime() : mixed
    {
        return $this->settle_time;
    }
    public function setSettleTime($value) : object
    {
        $this->settle_time = $value;
        return $this;
    }
    public function getRemark() : string
    {
        return $this->remark;
    }
    public function setRemark($value) : object
    {
        $this->remark = $value;
        return $this;
    }
    public function getCreatedAt() : mixed
    {
        return $this->created_at;
    }
    public function setCreatedAt($value) : static
    {
        $this->created_at = $value;
        return $this;
    }
    public function getUpdatedAt() : mixed
    {
        return $this->updated_at;
    }
    public function setUpdatedAt($value) : static
    {
        $this->updated_at = $value;
        return $this;
    }
}
