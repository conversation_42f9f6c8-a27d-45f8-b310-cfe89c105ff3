<?php

declare(strict_types=1);

namespace App\Model\Binary;

use Hyperf\DbConnection\Model\Model;

/**
 * @property int $id 
 * @property int $currency_id 币种ID
 * @property int $time_period 时间周期(秒)
 * @property string $time_period_name 时间周期名称(1m,5m,1h等)
 * @property int $price_source 价格来源:1现货,2合约
 * @property int $win_rate_type 盈利比例类型:1固定,2动态
 * @property int $lose_rate_type 亏损比例类型:1固定,2动态
 * @property float $fixed_win_rate 固定盈利比例(0.8000表示80%)
 * @property float $fixed_lose_rate 固定亏损比例(1.0000表示100%)
 * @property string $dynamic_win_formula 动态盈利计算公式:0.8 + ({price_diff_rate} * 0.5)
 * @property string $dynamic_lose_formula 动态亏损计算公式:1.0 - ({price_diff_rate} * 0.2)
 * @property float $min_amount 最小下单金额
 * @property float $max_amount 最大下单金额
 * @property int $status 状态:0禁用,1启用
 * @property \Carbon\Carbon $created_at 
 * @property \Carbon\Carbon $updated_at 
 */
class BinaryOptionConfig extends Model
{
    /**
     * 
     */
    public const FIELD_ID = 'id';
    /**
     * 币种ID
     */
    public const FIELD_CURRENCY_ID = 'currency_id';
    /**
     * 时间周期(秒)
     */
    public const FIELD_TIME_PERIOD = 'time_period';
    /**
     * 时间周期名称(1m,5m,1h等)
     */
    public const FIELD_TIME_PERIOD_NAME = 'time_period_name';
    /**
     * 价格来源:1现货,2合约
     */
    public const FIELD_PRICE_SOURCE = 'price_source';
    /**
     * 盈利比例类型:1固定,2动态
     */
    public const FIELD_WIN_RATE_TYPE = 'win_rate_type';
    /**
     * 亏损比例类型:1固定,2动态
     */
    public const FIELD_LOSE_RATE_TYPE = 'lose_rate_type';
    /**
     * 固定盈利比例(0.8000表示80%)
     */
    public const FIELD_FIXED_WIN_RATE = 'fixed_win_rate';
    /**
     * 固定亏损比例(1.0000表示100%)
     */
    public const FIELD_FIXED_LOSE_RATE = 'fixed_lose_rate';
    /**
     * 动态盈利计算公式:0.8 + ({price_diff_rate} * 0.5)
     */
    public const FIELD_DYNAMIC_WIN_FORMULA = 'dynamic_win_formula';
    /**
     * 动态亏损计算公式:1.0 - ({price_diff_rate} * 0.2)
     */
    public const FIELD_DYNAMIC_LOSE_FORMULA = 'dynamic_lose_formula';
    /**
     * 最小下单金额
     */
    public const FIELD_MIN_AMOUNT = 'min_amount';
    /**
     * 最大下单金额
     */
    public const FIELD_MAX_AMOUNT = 'max_amount';
    /**
     * 状态:0禁用,1启用
     */
    public const FIELD_STATUS = 'status';
    /**
     * 创建时间
     */
    public const FIELD_CREATED_AT = 'created_at';
    /**
     * 更新时间
     */
    public const FIELD_UPDATED_AT = 'updated_at';
    /**
     * The table associated with the model.
     */
    protected ?string $table = 'binary_option_configs';

    /**
     * The attributes that are mass assignable.
     */
    protected array $fillable = ['id', 'currency_id', 'time_period', 'time_period_name', 'price_source', 'win_rate_type', 'lose_rate_type', 'fixed_win_rate', 'fixed_lose_rate', 'dynamic_win_formula', 'dynamic_lose_formula', 'min_amount', 'max_amount', 'status', 'created_at', 'updated_at'];

    /**
     * The attributes that should be cast to native types.
     */
    protected array $casts = [
        'id' => 'integer',
        // 币种ID
        'currency_id' => 'integer',
        // 时间周期(秒)
        'time_period' => 'integer',
        // 价格来源:1现货,2合约
        'price_source' => 'integer',
        // 盈利比例类型:1固定,2动态
        'win_rate_type' => 'integer',
        // 亏损比例类型:1固定,2动态
        'lose_rate_type' => 'integer',
        // 固定盈利比例(0.8000表示80%)
        'fixed_win_rate' => 'float',
        // 固定亏损比例(1.0000表示100%)
        'fixed_lose_rate' => 'float',
        // 最小下单金额
        'min_amount' => 'float',
        // 最大下单金额
        'max_amount' => 'float',
        // 状态:0禁用,1启用
        'status' => 'integer',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];
    public function getId() : int
    {
        return $this->id;
    }
    public function setId($value) : object
    {
        $this->id = $value;
        return $this;
    }
    public function getCurrencyId() : int
    {
        return $this->currency_id;
    }
    public function setCurrencyId($value) : object
    {
        $this->currency_id = $value;
        return $this;
    }
    public function getTimePeriod() : int
    {
        return $this->time_period;
    }
    public function setTimePeriod($value) : object
    {
        $this->time_period = $value;
        return $this;
    }
    public function getTimePeriodName() : string
    {
        return $this->time_period_name;
    }
    public function setTimePeriodName($value) : object
    {
        $this->time_period_name = $value;
        return $this;
    }
    public function getPriceSource() : int
    {
        return $this->price_source;
    }
    public function setPriceSource($value) : object
    {
        $this->price_source = $value;
        return $this;
    }
    public function getWinRateType() : int
    {
        return $this->win_rate_type;
    }
    public function setWinRateType($value) : object
    {
        $this->win_rate_type = $value;
        return $this;
    }
    public function getLoseRateType() : int
    {
        return $this->lose_rate_type;
    }
    public function setLoseRateType($value) : object
    {
        $this->lose_rate_type = $value;
        return $this;
    }
    public function getFixedWinRate() : float
    {
        return $this->fixed_win_rate;
    }
    public function setFixedWinRate($value) : object
    {
        $this->fixed_win_rate = $value;
        return $this;
    }
    public function getFixedLoseRate() : float
    {
        return $this->fixed_lose_rate;
    }
    public function setFixedLoseRate($value) : object
    {
        $this->fixed_lose_rate = $value;
        return $this;
    }
    public function getDynamicWinFormula() : string
    {
        return $this->dynamic_win_formula;
    }
    public function setDynamicWinFormula($value) : object
    {
        $this->dynamic_win_formula = $value;
        return $this;
    }
    public function getDynamicLoseFormula() : string
    {
        return $this->dynamic_lose_formula;
    }
    public function setDynamicLoseFormula($value) : object
    {
        $this->dynamic_lose_formula = $value;
        return $this;
    }
    public function getMinAmount() : float
    {
        return $this->min_amount;
    }
    public function setMinAmount($value) : object
    {
        $this->min_amount = $value;
        return $this;
    }
    public function getMaxAmount() : float
    {
        return $this->max_amount;
    }
    public function setMaxAmount($value) : object
    {
        $this->max_amount = $value;
        return $this;
    }
    public function getStatus() : int
    {
        return $this->status;
    }
    public function setStatus($value) : object
    {
        $this->status = $value;
        return $this;
    }
    public function getCreatedAt() : mixed
    {
        return $this->created_at;
    }
    public function setCreatedAt($value) : static
    {
        $this->created_at = $value;
        return $this;
    }
    public function getUpdatedAt() : mixed
    {
        return $this->updated_at;
    }
    public function setUpdatedAt($value) : static
    {
        $this->updated_at = $value;
        return $this;
    }
}
