<?php

declare(strict_types=1);
/**
 * 策略平台API
 * 现货用户跟单配置模型
 */

namespace App\Model\Copy;

use App\Model\Copy\Enums\CopyStatus;
use App\Model\User\User;
use App\QueryBuilder\Model;
use Carbon\Carbon;
use Hyperf\Database\Model\Relations\BelongsTo;

/**
 * @property int $id 主键ID
 * @property int $follower_user_id 跟单者用户ID
 * @property int $expert_id 专家ID
 * @property int $expert_user_id 专家用户ID（冗余字段）
 * @property int $copy_type 跟单类型：0-额度，1-比例百分比
 * @property string|null $fixed_amount 固定额度（USDT）
 * @property string|null $rate 倍率 %
 * @property string|null $stop_loss_rate 止损比例 %
 * @property string|null $take_profit_rate 止盈比例 %
 * @property string|null $max_follow_amount 最大跟随金额
 * @property bool $auto_new_pairs 自动跟随新币对
 * @property bool $is_exclusive 是否尊享模式
 * @property array|null $copy_currencies 跟单币种配置（支持多币种）
 * @property CopyStatus $status 状态：1-跟单中，2-暂停
 * @property Carbon|null $created_at 创建时间
 * @property Carbon|null $updated_at 更新时间
 * @property User $followerUser 跟单者用户关联
 * @property CopySpotExpert $expert 专家关联
 */
final class CopySpotUserSetting extends Model
{
    /**
     * 主键ID
     */
    public const FIELD_ID = 'id';
    /**
     * 跟单者用户ID
     */
    public const FIELD_FOLLOWER_USER_ID = 'follower_user_id';
    /**
     * 专家ID
     */
    public const FIELD_EXPERT_ID = 'expert_id';
    /**
     * 专家用户ID（冗余字段）
     */
    public const FIELD_EXPERT_USER_ID = 'expert_user_id';
    /**
     * 跟单类型：0-额度，1-比例百分比
     */
    public const FIELD_COPY_TYPE = 'copy_type';
    /**
     * 固定额度（USDT）
     */
    public const FIELD_FIXED_AMOUNT = 'fixed_amount';
    /**
     * 倍率 %
     */
    public const FIELD_RATE = 'rate';
    /**
     * 止损比例 %
     */
    public const FIELD_STOP_LOSS_RATE = 'stop_loss_rate';
    /**
     * 止盈比例 %
     */
    public const FIELD_TAKE_PROFIT_RATE = 'take_profit_rate';
    /**
     * 最大跟随金额
     */
    public const FIELD_MAX_FOLLOW_AMOUNT = 'max_follow_amount';
    /**
     * 自动跟随新币对
     */
    public const FIELD_AUTO_NEW_PAIRS = 'auto_new_pairs';
    /**
     * 是否尊享模式
     */
    public const FIELD_IS_EXCLUSIVE = 'is_exclusive';
    /**
     * 跟单币种配置（支持多币种）
     */
    public const FIELD_COPY_CURRENCIES = 'copy_currencies';
    /**
     * 状态：1-跟单中，2-暂停
     */
    public const FIELD_STATUS = 'status';
    /**
     * 创建时间
     */
    public const FIELD_CREATED_AT = 'created_at';
    /**
     * 更新时间
     */
    public const FIELD_UPDATED_AT = 'updated_at';

    /**
     * The table associated with the model.
     */
    protected ?string $table = 'copy_spot_user_setting';

    /**
     * The attributes that are mass assignable.
     */
    protected array $fillable = [
        'follower_user_id', // 跟单者用户ID
        'expert_id', // 专家ID
        'expert_user_id', // 专家用户ID（冗余字段）
        'copy_type', // 跟单类型：0-额度，1-比例百分比
        'fixed_amount', // 固定额度（USDT）
        'rate', // 倍率 %
        'stop_loss_rate', // 止损比例 %
        'take_profit_rate', // 止盈比例 %
        'max_follow_amount', // 最大跟随金额
        'auto_new_pairs', // 自动跟随新币对
        'is_exclusive', // 是否尊享模式
        'copy_currencies', // 跟单币种配置（支持多币种）
        'status', // 状态：1-跟单中，2-暂停
        'created_at', // 创建时间
        'updated_at', // 更新时间
    ];

    /**
     * The attributes that should be cast to native types.
     */
    protected array $casts = [
        'id' => 'integer', // 主键ID
        'follower_user_id' => 'integer', // 跟单者用户ID
        'expert_id' => 'integer', // 专家ID
        'expert_user_id' => 'integer', // 专家用户ID（冗余字段）
        'copy_type' => 'integer', // 跟单类型：0-额度，1-比例百分比
        'fixed_amount' => 'decimal:8', // 固定额度（USDT）
        'rate' => 'decimal:2', // 倍率 %
        'stop_loss_rate' => 'decimal:2', // 止损比例 %
        'take_profit_rate' => 'decimal:2', // 止盈比例 %
        'max_follow_amount' => 'decimal:8', // 最大跟随金额
        'auto_new_pairs' => 'boolean', // 自动跟随新币对
        'is_exclusive' => 'boolean', // 是否尊享模式
        'copy_currencies' => 'array', // 跟单币种配置（支持多币种）
        'status' => CopyStatus::class, // 状态：1-跟单中，2-暂停
        'created_at' => 'datetime', // 创建时间
        'updated_at' => 'datetime', // 更新时间
    ];

    /**
     * 跟单者用户关联
     */
    public function followerUser(): BelongsTo
    {
        return $this->belongsTo(User::class, 'follower_user_id', 'id');
    }

    /**
     * 专家关联
     */
    public function expert(): BelongsTo
    {
        return $this->belongsTo(CopySpotExpert::class, 'expert_id', 'id');
    }

    /**
     * 专家用户关联
     */
    public function expertUser(): BelongsTo
    {
        return $this->belongsTo(User::class, 'expert_user_id', 'id');
    }

    /**
     * 获取主键ID
     */
    public function getId(): int
    {
        return $this->id;
    }

    /**
     * 设置主键ID
     */
    public function setId(int $value): static
    {
        $this->id = $value;
        return $this;
    }

    /**
     * 获取跟单者用户ID
     */
    public function getFollowerUserId(): int
    {
        return $this->follower_user_id;
    }

    /**
     * 设置跟单者用户ID
     */
    public function setFollowerUserId(int $value): static
    {
        $this->follower_user_id = $value;
        return $this;
    }

    /**
     * 获取专家ID
     */
    public function getExpertId(): int
    {
        return $this->expert_id;
    }

    /**
     * 设置专家ID
     */
    public function setExpertId(int $value): static
    {
        $this->expert_id = $value;
        return $this;
    }

    /**
     * 获取专家用户ID（冗余字段）
     */
    public function getExpertUserId(): int
    {
        return $this->expert_user_id;
    }

    /**
     * 设置专家用户ID（冗余字段）
     */
    public function setExpertUserId(int $value): static
    {
        $this->expert_user_id = $value;
        return $this;
    }

    /**
     * 获取跟单类型
     */
    public function getCopyType(): int
    {
        return $this->copy_type;
    }

    /**
     * 设置跟单类型
     */
    public function setCopyType(int $value): static
    {
        $this->copy_type = $value;
        return $this;
    }

    /**
     * 获取固定额度（USDT）
     */
    public function getFixedAmount(): ?string
    {
        return $this->fixed_amount;
    }

    /**
     * 设置固定额度（USDT）
     */
    public function setFixedAmount(?string $value): static
    {
        $this->fixed_amount = $value;
        return $this;
    }

    /**
     * 获取倍率 %
     */
    public function getRate(): ?string
    {
        return $this->rate;
    }

    /**
     * 设置倍率 %
     */
    public function setRate(?string $value): static
    {
        $this->rate = $value;
        return $this;
    }

    /**
     * 获取止损比例 %
     */
    public function getStopLossRate(): ?string
    {
        return $this->stop_loss_rate;
    }

    /**
     * 设置止损比例 %
     */
    public function setStopLossRate(?string $value): static
    {
        $this->stop_loss_rate = $value;
        return $this;
    }

    /**
     * 获取止盈比例 %
     */
    public function getTakeProfitRate(): ?string
    {
        return $this->take_profit_rate;
    }

    /**
     * 设置止盈比例 %
     */
    public function setTakeProfitRate(?string $value): static
    {
        $this->take_profit_rate = $value;
        return $this;
    }

    /**
     * 获取最大跟随金额
     */
    public function getMaxFollowAmount(): ?string
    {
        return $this->max_follow_amount;
    }

    /**
     * 设置最大跟随金额
     */
    public function setMaxFollowAmount(?string $value): static
    {
        $this->max_follow_amount = $value;
        return $this;
    }

    /**
     * 获取自动跟随新币对
     */
    public function getAutoNewPairs(): bool
    {
        return $this->auto_new_pairs;
    }

    /**
     * 设置自动跟随新币对
     */
    public function setAutoNewPairs(bool $value): static
    {
        $this->auto_new_pairs = $value;
        return $this;
    }

    /**
     * 获取是否尊享模式
     */
    public function getIsExclusive(): bool
    {
        return $this->is_exclusive;
    }

    /**
     * 设置是否尊享模式
     */
    public function setIsExclusive(bool $value): static
    {
        $this->is_exclusive = $value;
        return $this;
    }

    /**
     * 获取跟单币种配置（支持多币种）
     */
    public function getCopyCurrencies(): ?array
    {
        return $this->copy_currencies;
    }

    /**
     * 设置跟单币种配置（支持多币种）
     */
    public function setCopyCurrencies(?array $value): static
    {
        $this->copy_currencies = $value;
        return $this;
    }

    /**
     * 获取状态
     */
    public function getStatus(): CopyStatus
    {
        return $this->status;
    }

    /**
     * 设置状态
     */
    public function setStatus(CopyStatus $value): static
    {
        $this->status = $value;
        return $this;
    }

    /**
     * 获取创建时间
     */
    public function getCreatedAt(): ?Carbon
    {
        return $this->created_at;
    }

    /**
     * 获取更新时间
     */
    public function getUpdatedAt(): ?Carbon
    {
        return $this->updated_at;
    }
}
