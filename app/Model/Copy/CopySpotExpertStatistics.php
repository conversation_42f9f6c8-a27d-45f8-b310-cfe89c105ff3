<?php

declare(strict_types=1);
/**
 * 策略平台API
 * 现货交易专家统计表模型
 */

namespace App\Model\Copy;

use App\QueryBuilder\Model;

/**
 * @property int $id 主键ID
 * @property int $expert_id 专家ID
 * @property int $expert_user_id 专家用户ID（冗余字段）
 * @property float $profit 收益金额
 * @property float $profit_7d 7日收益金额
 * @property float $profit_30d 30日收益金额
 * @property float $profit_90d 90日收益金额
 * @property float $profit_180d 180日收益金额
 * @property int $profit_order_count 盈利订单数
 * @property int $profit_order_count_7d 7日盈利订单数
 * @property int $profit_order_count_30d 30日盈利订单数
 * @property int $profit_order_count_90d 90日盈利订单数
 * @property int $profit_order_count_180d 180日盈利订单数
 * @property int $loss_order_count 亏损订单数
 * @property int $loss_order_count_7d 7日亏损订单数
 * @property int $loss_order_count_30d 30日亏损订单数
 * @property int $loss_order_count_90d 90日亏损订单数
 * @property int $loss_order_count_180d 180日亏损订单数
 * @property float $win_rate 胜率 %
 * @property float $win_rate_7d 7日胜率 %
 * @property float $win_rate_30d 30日胜率 %
 * @property float $win_rate_90d 90日胜率 %
 * @property float $win_rate_180d 180日胜率 %
 * @property float $profit_rate 收益率 %
 * @property float $profit_rate_7d 7日收益率 %
 * @property float $profit_rate_30d 30日收益率 %
 * @property float $profit_rate_90d 90日收益率 %
 * @property float $profit_rate_180d 180日收益率 %
 * @property float $follower_profit 跟单者收益
 * @property float $follower_profit_7d 7日跟单者收益
 * @property float $follower_profit_30d 30日跟单者收益
 * @property float $follower_profit_90d 90日跟单者收益
 * @property float $follower_profit_180d 180日跟单者收益
 * @property float $aum 资产管理规模
 * @property int $trade_frequency 交易频率
 * @property int $trade_frequency_7d 7日交易频率
 * @property int $trade_frequency_30d 30日交易频率
 * @property int $trade_frequency_90d 90日交易频率
 * @property int $trade_frequency_180d 180日交易频率
 * @property int $total_follower_count 累计跟单人数
 * @property \Carbon\Carbon|null $created_at 创建时间
 * @property \Carbon\Carbon|null $updated_at 更新时间
 */
final class CopySpotExpertStatistics extends Model
{
    /**
     * 主键ID
     */
    public const FIELD_ID = 'id';
    /**
     * 专家ID
     */
    public const FIELD_EXPERT_ID = 'expert_id';
    /**
     * 专家用户ID（冗余字段）
     */
    public const FIELD_EXPERT_USER_ID = 'expert_user_id';
    /**
     * 收益金额
     */
    public const FIELD_PROFIT = 'profit';
    /**
     * 7日收益金额
     */
    public const FIELD_PROFIT_7D = 'profit_7d';
    /**
     * 30日收益金额
     */
    public const FIELD_PROFIT_30D = 'profit_30d';
    /**
     * 90日收益金额
     */
    public const FIELD_PROFIT_90D = 'profit_90d';
    /**
     * 180日收益金额
     */
    public const FIELD_PROFIT_180D = 'profit_180d';
    /**
     * 盈利订单数
     */
    public const FIELD_PROFIT_ORDER_COUNT = 'profit_order_count';
    /**
     * 7日盈利订单数
     */
    public const FIELD_PROFIT_ORDER_COUNT_7D = 'profit_order_count_7d';
    /**
     * 30日盈利订单数
     */
    public const FIELD_PROFIT_ORDER_COUNT_30D = 'profit_order_count_30d';
    /**
     * 90日盈利订单数
     */
    public const FIELD_PROFIT_ORDER_COUNT_90D = 'profit_order_count_90d';
    /**
     * 180日盈利订单数
     */
    public const FIELD_PROFIT_ORDER_COUNT_180D = 'profit_order_count_180d';
    /**
     * 亏损订单数
     */
    public const FIELD_LOSS_ORDER_COUNT = 'loss_order_count';
    /**
     * 7日亏损订单数
     */
    public const FIELD_LOSS_ORDER_COUNT_7D = 'loss_order_count_7d';
    /**
     * 30日亏损订单数
     */
    public const FIELD_LOSS_ORDER_COUNT_30D = 'loss_order_count_30d';
    /**
     * 90日亏损订单数
     */
    public const FIELD_LOSS_ORDER_COUNT_90D = 'loss_order_count_90d';
    /**
     * 180日亏损订单数
     */
    public const FIELD_LOSS_ORDER_COUNT_180D = 'loss_order_count_180d';
    /**
     * 胜率 %
     */
    public const FIELD_WIN_RATE = 'win_rate';
    /**
     * 7日胜率 %
     */
    public const FIELD_WIN_RATE_7D = 'win_rate_7d';
    /**
     * 30日胜率 %
     */
    public const FIELD_WIN_RATE_30D = 'win_rate_30d';
    /**
     * 90日胜率 %
     */
    public const FIELD_WIN_RATE_90D = 'win_rate_90d';
    /**
     * 180日胜率 %
     */
    public const FIELD_WIN_RATE_180D = 'win_rate_180d';
    /**
     * 收益率 %
     */
    public const FIELD_PROFIT_RATE = 'profit_rate';
    /**
     * 7日收益率 %
     */
    public const FIELD_PROFIT_RATE_7D = 'profit_rate_7d';
    /**
     * 30日收益率 %
     */
    public const FIELD_PROFIT_RATE_30D = 'profit_rate_30d';
    /**
     * 90日收益率 %
     */
    public const FIELD_PROFIT_RATE_90D = 'profit_rate_90d';
    /**
     * 180日收益率 %
     */
    public const FIELD_PROFIT_RATE_180D = 'profit_rate_180d';
    /**
     * 跟单者收益
     */
    public const FIELD_FOLLOWER_PROFIT = 'follower_profit';
    /**
     * 7日跟单者收益
     */
    public const FIELD_FOLLOWER_PROFIT_7D = 'follower_profit_7d';
    /**
     * 30日跟单者收益
     */
    public const FIELD_FOLLOWER_PROFIT_30D = 'follower_profit_30d';
    /**
     * 90日跟单者收益
     */
    public const FIELD_FOLLOWER_PROFIT_90D = 'follower_profit_90d';
    /**
     * 180日跟单者收益
     */
    public const FIELD_FOLLOWER_PROFIT_180D = 'follower_profit_180d';
    /**
     * 资产管理规模
     */
    public const FIELD_AUM = 'aum';
    /**
     * 交易频率
     */
    public const FIELD_TRADE_FREQUENCY = 'trade_frequency';
    /**
     * 7日交易频率
     */
    public const FIELD_TRADE_FREQUENCY_7D = 'trade_frequency_7d';
    /**
     * 30日交易频率
     */
    public const FIELD_TRADE_FREQUENCY_30D = 'trade_frequency_30d';
    /**
     * 90日交易频率
     */
    public const FIELD_TRADE_FREQUENCY_90D = 'trade_frequency_90d';
    /**
     * 180日交易频率
     */
    public const FIELD_TRADE_FREQUENCY_180D = 'trade_frequency_180d';
    /**
     * 累计跟单人数
     */
    public const FIELD_TOTAL_FOLLOWER_COUNT = 'total_follower_count';
    /**
     * 创建时间
     */
    public const FIELD_CREATED_AT = 'created_at';
    /**
     * 更新时间
     */
    public const FIELD_UPDATED_AT = 'updated_at';

    /**
     * The table associated with the model.
     */
    protected ?string $table = 'copy_spot_expert_statistics';

    /**
     * The attributes that are mass assignable.
     */
    protected array $fillable = [
        'id', // 主键ID
        'expert_id', // 专家ID
        'expert_user_id', // 专家用户ID（冗余字段）
        'profit', // 收益金额
        'profit_7d', // 7日收益金额
        'profit_30d', // 30日收益金额
        'profit_90d', // 90日收益金额
        'profit_180d', // 180日收益金额
        'profit_order_count', // 盈利订单数
        'profit_order_count_7d', // 7日盈利订单数
        'profit_order_count_30d', // 30日盈利订单数
        'profit_order_count_90d', // 90日盈利订单数
        'profit_order_count_180d', // 180日盈利订单数
        'loss_order_count', // 亏损订单数
        'loss_order_count_7d', // 7日亏损订单数
        'loss_order_count_30d', // 30日亏损订单数
        'loss_order_count_90d', // 90日亏损订单数
        'loss_order_count_180d', // 180日亏损订单数
        'win_rate', // 胜率 %
        'win_rate_7d', // 7日胜率 %
        'win_rate_30d', // 30日胜率 %
        'win_rate_90d', // 90日胜率 %
        'win_rate_180d', // 180日胜率 %
        'profit_rate', // 收益率 %
        'profit_rate_7d', // 7日收益率 %
        'profit_rate_30d', // 30日收益率 %
        'profit_rate_90d', // 90日收益率 %
        'profit_rate_180d', // 180日收益率 %
        'follower_profit', // 跟单者收益
        'follower_profit_7d', // 7日跟单者收益
        'follower_profit_30d', // 30日跟单者收益
        'follower_profit_90d', // 90日跟单者收益
        'follower_profit_180d', // 180日跟单者收益
        'aum', // 资产管理规模
        'trade_frequency', // 交易频率
        'trade_frequency_7d', // 7日交易频率
        'trade_frequency_30d', // 30日交易频率
        'trade_frequency_90d', // 90日交易频率
        'trade_frequency_180d', // 180日交易频率
        'total_follower_count', // 累计跟单人数
        'created_at', // 创建时间
        'updated_at' // 更新时间
    ];

    /**
     * The attributes that should be cast to native types.
     */
    protected array $casts = [
        'id' => 'integer', // 主键ID
        'expert_id' => 'integer', // 专家ID
        'expert_user_id' => 'integer', // 专家用户ID（冗余字段）
        'profit' => 'float', // 收益金额
        'profit_7d' => 'float', // 7日收益金额
        'profit_30d' => 'float', // 30日收益金额
        'profit_90d' => 'float', // 90日收益金额
        'profit_180d' => 'float', // 180日收益金额
        'profit_order_count' => 'integer', // 盈利订单数
        'profit_order_count_7d' => 'integer', // 7日盈利订单数
        'profit_order_count_30d' => 'integer', // 30日盈利订单数
        'profit_order_count_90d' => 'integer', // 90日盈利订单数
        'profit_order_count_180d' => 'integer', // 180日盈利订单数
        'loss_order_count' => 'integer', // 亏损订单数
        'loss_order_count_7d' => 'integer', // 7日亏损订单数
        'loss_order_count_30d' => 'integer', // 30日亏损订单数
        'loss_order_count_90d' => 'integer', // 90日亏损订单数
        'loss_order_count_180d' => 'integer', // 180日亏损订单数
        'win_rate' => 'float', // 胜率 %
        'win_rate_7d' => 'float', // 7日胜率 %
        'win_rate_30d' => 'float', // 30日胜率 %
        'win_rate_90d' => 'float', // 90日胜率 %
        'win_rate_180d' => 'float', // 180日胜率 %
        'profit_rate' => 'float', // 收益率 %
        'profit_rate_7d' => 'float', // 7日收益率 %
        'profit_rate_30d' => 'float', // 30日收益率 %
        'profit_rate_90d' => 'float', // 90日收益率 %
        'profit_rate_180d' => 'float', // 180日收益率 %
        'follower_profit' => 'float', // 跟单者收益
        'follower_profit_7d' => 'float', // 7日跟单者收益
        'follower_profit_30d' => 'float', // 30日跟单者收益
        'follower_profit_90d' => 'float', // 90日跟单者收益
        'follower_profit_180d' => 'float', // 180日跟单者收益
        'aum' => 'float', // 资产管理规模
        'trade_frequency' => 'integer', // 交易频率
        'trade_frequency_7d' => 'integer', // 7日交易频率
        'trade_frequency_30d' => 'integer', // 30日交易频率
        'trade_frequency_90d' => 'integer', // 90日交易频率
        'trade_frequency_180d' => 'integer', // 180日交易频率
        'total_follower_count' => 'integer', // 累计跟单人数
        'created_at' => 'datetime', // 创建时间
        'updated_at' => 'datetime' // 更新时间
    ];
}
