<?php

declare(strict_types=1);

namespace App\Model\Copy;

use Hyperf\DbConnection\Model\Model;

/**
 * @property int $id 主键ID
 * @property int $follower_user_id 跟单者用户ID
 * @property int $expert_id 专家ID
 * @property int $expert_user_id 专家用户ID（冗余字段）
 * @property int $currency_id 币种ID
 * @property float $total_buy_quantity 总买入数量
 * @property float $total_sell_quantity 总卖出数量
 * @property float $available_quantity 可用持仓数量
 * @property float $frozen_quantity 冻结数量（平仓中）
 * @property float $avg_buy_price 平均买入价格
 * @property float $total_buy_amount 总买入金额
 * @property float $avg_sell_price 平均卖出价格
 * @property float $total_sell_amount 总卖出金额
 * @property float $realized_pnl 已实现盈亏
 * @property int $status 状态：1-持仓中，2-已平仓
 * @property \Carbon\Carbon $created_at 创建时间
 * @property \Carbon\Carbon $updated_at 更新时间
 */
class CopySpotPosition extends Model
{
    /**
     * 主键ID
     */
    public const FIELD_ID = 'id';
    /**
     * 跟单者用户ID
     */
    public const FIELD_FOLLOWER_USER_ID = 'follower_user_id';
    /**
     * 专家ID
     */
    public const FIELD_EXPERT_ID = 'expert_id';
    /**
     * 专家用户ID（冗余字段）
     */
    public const FIELD_EXPERT_USER_ID = 'expert_user_id';
    /**
     * 币种ID
     */
    public const FIELD_CURRENCY_ID = 'currency_id';
    /**
     * 总买入数量
     */
    public const FIELD_TOTAL_BUY_QUANTITY = 'total_buy_quantity';
    /**
     * 总卖出数量
     */
    public const FIELD_TOTAL_SELL_QUANTITY = 'total_sell_quantity';
    /**
     * 可用持仓数量
     */
    public const FIELD_AVAILABLE_QUANTITY = 'available_quantity';
    /**
     * 冻结数量（平仓中）
     */
    public const FIELD_FROZEN_QUANTITY = 'frozen_quantity';
    /**
     * 平均买入价格
     */
    public const FIELD_AVG_BUY_PRICE = 'avg_buy_price';
    /**
     * 总买入金额
     */
    public const FIELD_TOTAL_BUY_AMOUNT = 'total_buy_amount';
    /**
     * 平均卖出价格
     */
    public const FIELD_AVG_SELL_PRICE = 'avg_sell_price';
    /**
     * 总卖出金额
     */
    public const FIELD_TOTAL_SELL_AMOUNT = 'total_sell_amount';
    /**
     * 已实现盈亏
     */
    public const FIELD_REALIZED_PNL = 'realized_pnl';
    /**
     * 状态：1-持仓中，2-已平仓
     */
    public const FIELD_STATUS = 'status';
    /**
     * 创建时间
     */
    public const FIELD_CREATED_AT = 'created_at';
    /**
     * 更新时间
     */
    public const FIELD_UPDATED_AT = 'updated_at';
    /**
     * The table associated with the model.
     */
    protected ?string $table = 'copy_spot_position';

    /**
     * The attributes that are mass assignable.
     */
    protected array $fillable = ['id', 'follower_user_id', 'expert_id', 'expert_user_id', 'currency_id', 'total_buy_quantity', 'total_sell_quantity', 'available_quantity', 'frozen_quantity', 'avg_buy_price', 'total_buy_amount', 'avg_sell_price', 'total_sell_amount', 'realized_pnl', 'status', 'created_at', 'updated_at'];

    /**
     * The attributes that should be cast to native types.
     */
    protected array $casts = [
        // 主键ID
        'id' => 'integer',
        // 跟单者用户ID
        'follower_user_id' => 'integer',
        // 专家ID
        'expert_id' => 'integer',
        // 专家用户ID（冗余字段）
        'expert_user_id' => 'integer',
        // 币种ID
        'currency_id' => 'integer',
        // 总买入数量
        'total_buy_quantity' => 'float',
        // 总卖出数量
        'total_sell_quantity' => 'float',
        // 可用持仓数量
        'available_quantity' => 'float',
        // 冻结数量（平仓中）
        'frozen_quantity' => 'float',
        // 平均买入价格
        'avg_buy_price' => 'float',
        // 总买入金额
        'total_buy_amount' => 'float',
        // 平均卖出价格
        'avg_sell_price' => 'float',
        // 总卖出金额
        'total_sell_amount' => 'float',
        // 已实现盈亏
        'realized_pnl' => 'float',
        // 状态：1-持仓中，2-已平仓
        'status' => 'integer',
        // 创建时间
        'created_at' => 'datetime',
        // 更新时间
        'updated_at' => 'datetime',
    ];
    public function getId() : int
    {
        return $this->id;
    }
    public function setId($value) : object
    {
        $this->id = $value;
        return $this;
    }
    public function getFollowerUserId() : int
    {
        return $this->follower_user_id;
    }
    public function setFollowerUserId($value) : object
    {
        $this->follower_user_id = $value;
        return $this;
    }
    public function getExpertId() : int
    {
        return $this->expert_id;
    }
    public function setExpertId($value) : object
    {
        $this->expert_id = $value;
        return $this;
    }
    public function getExpertUserId() : int
    {
        return $this->expert_user_id;
    }
    public function setExpertUserId($value) : object
    {
        $this->expert_user_id = $value;
        return $this;
    }
    public function getCurrencyId() : int
    {
        return $this->currency_id;
    }
    public function setCurrencyId($value) : object
    {
        $this->currency_id = $value;
        return $this;
    }
    public function getTotalBuyQuantity() : float
    {
        return $this->total_buy_quantity;
    }
    public function setTotalBuyQuantity($value) : object
    {
        $this->total_buy_quantity = $value;
        return $this;
    }
    public function getTotalSellQuantity() : float
    {
        return $this->total_sell_quantity;
    }
    public function setTotalSellQuantity($value) : object
    {
        $this->total_sell_quantity = $value;
        return $this;
    }
    public function getAvailableQuantity() : float
    {
        return $this->available_quantity;
    }
    public function setAvailableQuantity($value) : object
    {
        $this->available_quantity = $value;
        return $this;
    }
    public function getFrozenQuantity() : float
    {
        return $this->frozen_quantity;
    }
    public function setFrozenQuantity($value) : object
    {
        $this->frozen_quantity = $value;
        return $this;
    }
    public function getAvgBuyPrice() : float
    {
        return $this->avg_buy_price;
    }
    public function setAvgBuyPrice($value) : object
    {
        $this->avg_buy_price = $value;
        return $this;
    }
    public function getTotalBuyAmount() : float
    {
        return $this->total_buy_amount;
    }
    public function setTotalBuyAmount($value) : object
    {
        $this->total_buy_amount = $value;
        return $this;
    }
    public function getAvgSellPrice() : float
    {
        return $this->avg_sell_price;
    }
    public function setAvgSellPrice($value) : object
    {
        $this->avg_sell_price = $value;
        return $this;
    }
    public function getTotalSellAmount() : float
    {
        return $this->total_sell_amount;
    }
    public function setTotalSellAmount($value) : object
    {
        $this->total_sell_amount = $value;
        return $this;
    }
    public function getRealizedPnl() : float
    {
        return $this->realized_pnl;
    }
    public function setRealizedPnl($value) : object
    {
        $this->realized_pnl = $value;
        return $this;
    }
    public function getStatus() : int
    {
        return $this->status;
    }
    public function setStatus($value) : object
    {
        $this->status = $value;
        return $this;
    }
    public function getCreatedAt() : mixed
    {
        return $this->created_at;
    }
    public function setCreatedAt($value) : static
    {
        $this->created_at = $value;
        return $this;
    }
    public function getUpdatedAt() : mixed
    {
        return $this->updated_at;
    }
    public function setUpdatedAt($value) : static
    {
        $this->updated_at = $value;
        return $this;
    }
}
