<?php

declare(strict_types=1);

namespace App\Model\Copy\Enums;

enum CopySpotPositionStatus: int
{
    case HOLDING = 1;  // 持仓中
    case CLOSED = 2;   // 已平仓

    public function getDescription(): string
    {
        return match ($this) {
            self::HOLDING => '持仓中',
            self::CLOSED => '已平仓',
        };
    }

    public static function getAll(): array
    {
        return [
            self::HOLDING->value => self::HOLDING->getDescription(),
            self::CLOSED->value => self::CLOSED->getDescription(),
        ];
    }
}
