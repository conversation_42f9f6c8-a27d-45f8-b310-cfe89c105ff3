<?php

declare(strict_types=1);
/**
 * This file is part of MineAdmin.
 *
 * @link     https://www.mineadmin.com
 * @document https://doc.mineadmin.com
 * @contact  <EMAIL>
 * @license  https://github.com/mineadmin/MineAdmin/blob/master/LICENSE
 */

namespace App\Model\Ctoc;

use App\Model\User\User;
use Carbon\Carbon;
use Hyperf\Database\Model\Relations\BelongsTo;
use Hyperf\DbConnection\Model\Model as MineModel;

/**
 * c2c保证金表模型.
 *
 * @property int $user_id 用户ID（关联cpx_user用户表）
 * @property string $order_sn 订单号
 * @property float $money 保证金
 * @property int $status 审核状态: 0-待支付 1-已支付 2-支付失败
 * @property int $created_by 创建者
 * @property int $updated_by 更新者
 * @property Carbon $created_at 创建时间
 * @property Carbon $updated_at 更新时间
 */
final class UserMerchantMoney extends MineModel
{
    /**
     * 数据表名称.
     */
    protected ?string $table = 'ctc_user_merchant_money';

    /**
     * 允许批量赋值的属性.
     */
    protected array $fillable = [
        'user_id',
        'order_sn',
        'money',
        'status',
        'created_by',
        'updated_by',
        'created_at',
        'updated_at',
    ];

    /**
     * 数据转换设置.
     */
    protected array $casts = [
        'user_id' => 'integer',
        'order_sn' => 'string',
        'money' => 'decimal:2',
        'status' => 'integer',
        'created_by' => 'integer',
        'updated_by' => 'integer',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /**
     * 隐藏的属性.
     */
    protected array $hidden = [
    ];


    /**
     * ID.
     * Summary of FIELD_ID
     * @var string
     */
    public const FIELD_ID = 'id';

    /**
     * 用户ID
     * Summary of FIELD_USER_ID
     * @var string
     */
    public const FIELD_USER_ID = 'user_id';

    /**
     * 订单号
     * Summary of FIELD_ORDER_SN
     * @var string
     */
    public const FIELD_ORDER_SN = 'order_sn';

    /**
     * 保证金金额
     * Summary of FIELD_MONEY
     * @var string
     */
    public const FIELD_MONEY = 'money';

    /**
     * 支付状态: 0-待支付 1-已支付 2-支付失败
     * Summary of FIELD_STATUS
     * @var string
     */
    public const FIELD_STATUS = 'status';


    /**
     * 创建者
     * Summary of FIELD_CREATED_BY
     * @var string
     */
    public const FIELD_CREATED_BY = 'created_by';

    /**
     * 更新者
     * Summary of FIELD_UPDATED_BY
     * @var string
     */
    public const FIELD_UPDATED_BY = 'updated_by';

    /**
     * 创建时间
     * Summary of FIELD_CREATED_AT
     * @var string
     */
    public const FIELD_CREATED_AT = 'created_at';

    /**
     * 更新时间
     * Summary of FIELD_UPDATED_AT
     * @var string
     */
    public const FIELD_UPDATED_AT = 'updated_at';

  
    /**
     * 关联 User 模型.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class, 'user_id', 'id');
    }

}