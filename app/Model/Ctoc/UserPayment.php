<?php

declare(strict_types=1);
/**
 * This file is part of MineAdmin.
 *
 * @link     https://www.mineadmin.com
 * @document https://doc.mineadmin.com
 * @contact  <EMAIL>
 * @license  https://github.com/mineadmin/MineAdmin/blob/master/LICENSE
 */

namespace App\Model\Ctoc;

use App\Model\User\User;
use Carbon\Carbon;
use Hyperf\Database\Model\Relations\BelongsTo;
use Hyperf\Database\Model\SoftDeletes;
use Hyperf\DbConnection\Model\Model as MineModel;

/**
 * 用户支付账号表模型.
 *
 * @property int $user_id 用户ID
 * @property int $flat_currency_id 法币ID
 * @property string $payment_type 支付方式 如 wechat
 * @property array $account_info 账号信息json
 * @property int $created_by 创建者
 * @property int $updated_by 更新者
 * @property Carbon $created_at 创建时间
 * @property Carbon $updated_at 更新时间
 */
final class UserPayment extends MineModel
{
    use SoftDeletes;

    /**
     * 数据表名称.
     */
    protected ?string $table = 'cpx_user_payment';

    /**
     * 允许批量赋值的属性.
     */
    protected array $fillable = [
        'user_id',
        'flat_currency_id',
        'payment_type',
        'account_info',
        'created_by',
        'updated_by',
        'created_at',
        'updated_at',
    ];

    /**
     * 数据转换设置.
     */
    protected array $casts = [
        'user_id' => 'integer',
        'flat_currency_id' => 'integer',
        'payment_type' => 'string',
        'account_info' => 'array',
        'created_by' => 'integer',
        'updated_by' => 'integer',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /**
     * 隐藏的属性.
     */
    protected array $hidden = [
    ];

    /**
     * id 
     */
    public const FIELD_ID = 'id';

    /**
     * 用户id 
     * Summary of FIELD_USER_ID
     * @var string
     */
    public const FIELD_USER_ID = 'user_id';

    /**
     * 支付方式 
     * Summary of FIELD_PAYMENT_TYPE
     * @var string
     */
    public const FIELD_PAYMENT_TYPE = 'payment_type';

    /**
     * 账号信息 
     * Summary of FIELD_ACCOUNT_INFO
     * @var string
     */
    public const FIELD_ACCOUNT_INFO = 'account_info';

    /**
     * 币种id 
     * Summary of FIELD_FLAT_CURRENCY_ID
     * @var string
     */
    public const FIELD_FLAT_CURRENCY_ID = 'flat_currency_id';


    /**
     * 删除时间
     */
    public const FIELD_DELETED_AT = 'deleted_at';

    /**
     * 关联 User 模型.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class, 'user_id', 'id');
    }

}