<?php

declare(strict_types=1);
/**
 * 策略平台API
 * c2c商家枚举
 */

namespace App\Model\Ctoc\Enums;

use Hyperf\Constants\Annotation\Constants;
use Hyperf\Constants\Annotation\Message;
use Hyperf\Constants\EnumConstantsTrait;

#[Constants]
enum UserMerchantEnums: int
{
    use EnumConstantsTrait;

    /**
     * 待审核
     */
    #[Message('usermerchant.enums.status.0')]
    case STAY = 0;

    /**
     * 审核通过
     */
    #[Message('usermerchant.enums.status.1')]
    case APPROVED = 1;

    /**
     * 拒绝
     */
    #[Message('usermerchant.enums.status.2')]
    case REJECTED = 2;


}
