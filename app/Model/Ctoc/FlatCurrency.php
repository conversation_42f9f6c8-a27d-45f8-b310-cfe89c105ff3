<?php

declare(strict_types=1);
/**
 * This file is part of MineAdmin.
 *
 * @link     https://www.mineadmin.com
 * @document https://doc.mineadmin.com
 * @contact  <EMAIL>
 * @license  https://github.com/mineadmin/MineAdmin/blob/master/LICENSE
 */

namespace App\Model\Ctoc;

use App\Service\SystemConfigService;
use Carbon\Carbon;
use Hyperf\Database\Model\Relations\BelongsTo;
use Hyperf\Database\Model\Relations\BelongsToMany;
use Hyperf\Database\Model\Relations\HasMany;
use Hyperf\DbConnection\Model\Model as MineModel;
use Plugin\West\SysSettings\Model\Config;

/**
 * 法币表模型.
 *
 * @property array $payment_type 支付方式 比如，支付宝，微信，多选
 * @property string $title 名称
 * @property string $icon 图标
 * @property int $created_by 创建者
 * @property int $updated_by 更新者
 * @property Carbon $created_at 创建时间
 * @property Carbon $updated_at 更新时间
 */
final class FlatCurrency extends MineModel
{
    /**
     * 数据表名称.
     */
    protected ?string $table = 'cpx_flat_currency';

    /**
     * 允许批量赋值的属性.
     */
    protected array $fillable = [
        'payment_type',
        'title',
        'icon',
        'created_by',
        'updated_by',
        'created_at',
        'updated_at',
    ];

    /**
     * 数据转换设置.
     */
    protected array $casts = [
        'payment_type' => 'array',
        'title' => 'string',
        'icon' => 'string',
        'created_by' => 'integer',
        'updated_by' => 'integer',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /**
     * 隐藏的属性.
     */
    protected array $hidden = [
    ];

    /**
     * ID.
     * Summary of FIELD_ID
     * @var string
     */
    public const FIELD_ID = 'id';

    /**
     * 名称
     * Summary of FIELD_TITLE
     * @var string
     */
    public const FIELD_TITLE = 'title';

    /**
     * 图标
     * Summary of FIELD_ICON
     * @var string
     */
    public const FIELD_ICON = 'icon';

    /**
     * 支付方式
     * Summary of FIELD_PAYMENT_TYPE
     * @var string
     */
    public const FIELD_PAYMENT_TYPE = 'payment_type';

    /**
     * 创建时间
     * Summary of FIELD_CREATED_AT
     * @var string
     */
    public const FIELD_CREATED_AT = 'created_at';

    /**
     * 更新时间
     * Summary of FIELD_UPDATED_AT
     * @var string
     */
    public const FIELD_UPDATED_AT = 'updated_at';


}