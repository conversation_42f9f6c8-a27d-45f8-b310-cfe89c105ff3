<?php

declare(strict_types=1);
/**
 * This file is part of MineAdmin.
 *
 * @link     https://www.mineadmin.com
 * @document https://doc.mineadmin.com
 * @contact  <EMAIL>
 * @license  https://github.com/mineadmin/MineAdmin/blob/master/LICENSE
 */

namespace App\Model\Ctoc;

use Carbon\Carbon;
use Hyperf\DbConnection\Model\Model as MineModel;

/**
 * 支付方式表模型.
 *
 * @property string $name 选项名称
 * @property string $field 选项字段
 * @property array $type 表单类型，比如图片，文本
 * @property int $created_by 创建者
 * @property int $updated_by 更新者
 * @property Carbon $created_at 创建时间
 * @property Carbon $updated_at 更新时间
 */
final class Payment extends MineModel
{
    /**
     * 数据表名称.
     */
    protected ?string $table = 'cpx_payment';

    /**
     * 允许批量赋值的属性.
     */
    protected array $fillable = [
        'payment_type',
        'name',
        'field',
        'type',
        'created_by',
        'updated_by',
        'created_at',
        'updated_at',
    ];

    /**
     * 数据转换设置.
     */
    protected array $casts = [
        'payment_type' => 'string',
        'name' => 'string',
        'field' => 'string',
        'type' => 'integer',
        'created_by' => 'integer',
        'updated_by' => 'integer',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /**
     * 隐藏的属性.
     */
    protected array $hidden = [
    ];

    /**
     * ID.
     * Summary of FIELD_ID
     * @var string
     */
    public const FIELD_ID = 'id';

    /**
     * 选项名称
     * Summary of name
     * @var string
     */
    public const FIELD_NAME = 'name';

    /**
     * 选项字段
     * Summary of field
     * @var string
     */
    public const FIELD_FIELD = 'field';

    /**
     * 表单类型，比如图片，文本.    
     * Summary of TYPE
     * @var string
     */
    public const FIELD_TYPE = 'type';

    /**
     * 支付方式：如 支付宝 微信.    
     * Summary of TYPE
     * @var string
     */
    public const FIELD_PAYMENT_TYPE = 'payment_type';


}