<?php

declare(strict_types=1);
/**
 * This file is part of MineAdmin.
 *
 * @link     https://www.mineadmin.com
 * @document https://doc.mineadmin.com
 * @contact  <EMAIL>
 * @license  https://github.com/mineadmin/MineAdmin/blob/master/LICENSE
 */

namespace App\Model\Ctoc;

use Carbon\Carbon;
use Hyperf\DbConnection\Model\Model as MineModel;

/**
 * 银行表模型.
 *
 * @property string $name 银行名称
 * @property int $created_by 创建者
 * @property int $updated_by 更新者
 * @property Carbon $created_at 创建时间
 * @property Carbon $updated_at 更新时间
 */
final class Bank extends MineModel
{
    /**
     * 数据表名称.
     */
    protected ?string $table = 'cpx_bank';

    /**
     * 允许批量赋值的属性.
     */
    protected array $fillable = [
        'name',
        'created_by',
        'updated_by',
        'created_at',
        'updated_at',
    ];

    /**
     * 数据转换设置.
     */
    protected array $casts = [
        'name' => 'string',
        'created_by' => 'integer',
        'updated_by' => 'integer',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /**
     * 隐藏的属性.
     */
    protected array $hidden = [
    ];



}