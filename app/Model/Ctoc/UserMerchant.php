<?php

declare(strict_types=1);
/**
 * This file is part of MineAdmin.
 *
 * @link     https://www.mineadmin.com
 * @document https://doc.mineadmin.com
 * @contact  <EMAIL>
 * @license  https://github.com/mineadmin/MineAdmin/blob/master/LICENSE
 */

namespace App\Model\Ctoc;

use App\Model\User\User;
use Carbon\Carbon;
use Hyperf\Database\Model\Relations\BelongsTo;
use Hyperf\DbConnection\Model\Model as MineModel;

/**
 * c2c商家申请表模型.
 *
 * @property int $user_id 用户ID（关联cpx_user用户表）
 * @property string $user_name 姓名
 * @property string $id_card 身份证号
 * @property string $id_card_front 身份证正面图片
 * @property string $id_card_back 身份证反面图片
 * @property string $store_name 店铺名称
 * @property string $store_logo 店铺Logo图片
 * @property string $contact_name 联系人姓名
 * @property string $contact_phone 联系电话
 * @property string $contact_email 联系邮箱
 * @property int $status 审核状态: 0-待审核 1-审核通过 2-拒绝
 * @property string $remark 审核备注
 * @property int $created_by 创建者
 * @property int $updated_by 更新者
 * @property Carbon $created_at 创建时间
 * @property Carbon $updated_at 更新时间
 */
final class UserMerchant extends MineModel
{
    /**
     * 数据表名称.
     */
    protected ?string $table = 'ctc_user_merchant';

    /**
     * 允许批量赋值的属性.
     */
    protected array $fillable = [
        'user_id',
        'user_name',
        'id_card',
        'id_card_front',
        'id_card_back',
        'store_name',
        'store_logo',
        'contact_name',
        'contact_phone',
        'contact_email',
        'status',
        'remark',
        'created_by',
        'updated_by',
        'created_at',
        'updated_at',
    ];

    /**
     * 数据转换设置.
     */
    protected array $casts = [
        'user_id' => 'integer',
        'user_name' => 'string',
        'id_card' => 'string',
        'id_card_front' => 'string',
        'id_card_back' => 'string',
        'store_name' => 'string',
        'store_logo' => 'string',
        'contact_name' => 'string',
        'contact_phone' => 'string',
        'contact_email' => 'string',
        'status' => 'integer',
        'remark' => 'string',
        'created_by' => 'integer',
        'updated_by' => 'integer',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /**
     * 隐藏的属性.
     */
    protected array $hidden = [
    ];



    /**
     * ID.
     * Summary of FIELD_ID
     * @var string
     */
    public const FIELD_ID = 'id';

    /**
     * ID.
     * Summary of FIELD_USER_ID
     * @var string
     */
    public const FIELD_USER_ID = 'user_id';

    /**
     * 姓名
     * Summary of FIELD_USER_NAME
     * @var string
     */
    public const FIELD_USER_NAME = 'user_name';

    /**
     * 身份证号
     * Summary of FIELD_ID_CARD
     * @var string
     */
    public const FIELD_ID_CARD = 'id_card';

    /**
     * 身份证正面图片
     * Summary of FIELD_ID_CARD_FRONT
     * @var string
     */
    public const FIELD_ID_CARD_FRONT = 'id_card_front';

    /**
     * 身份证反面图片
     * Summary of FIELD_ID_CARD_BACK
     * @var string
     */
    public const FIELD_ID_CARD_BACK = 'id_card_back';

    /**
     * 店铺名称
     * Summary of FIELD_STORE_NAME
     * @var string
     */
    public const FIELD_STORE_NAME = 'store_name';

    /**
     * 店铺Logo图片
     * Summary of FIELD_STORE_LOGO
     * @var string
     */
    public const FIELD_STORE_LOGO = 'store_logo';

    /**
     * 联系人姓名
     * Summary of FIELD_CONTACT_NAME
     * @var string
     */
    public const FIELD_CONTACT_NAME = 'contact_name';

    /**
     * 联系电话
     * Summary of FIELD_CONTACT_PHONE
     * @var string
     */
    public const FIELD_CONTACT_PHONE = 'contact_phone';

    /**
     * 联系邮箱
     * Summary of FIELD_CONTACT_EMAIL
     * @var string
     */
    public const FIELD_CONTACT_EMAIL = 'contact_email';

    /**
     * 审核状态
     * Summary of FIELD_STATUS
     * @var string
     */
    public const FIELD_STATUS = 'status';

    /**
     * 审核备注
     * Summary of FIELD_REMARK
     * @var string
     */
    public const FIELD_REMARK = 'remark';

    /**
     * 创建者
     * Summary of FIELD_CREATED_BY
     * @var string
     */
    public const FIELD_CREATED_BY = 'created_by';

    /**
     * 更新者
     * Summary of FIELD_UPDATED_BY
     * @var string
     */
    public const FIELD_UPDATED_BY = 'updated_by';

    /**
     * 创建时间
     * Summary of FIELD_CREATED_AT
     * @var string
     */
    public const FIELD_CREATED_AT = 'created_at';

    /**
     * 更新时间
     * Summary of FIELD_UPDATED_AT
     * @var string
     */
    public const FIELD_UPDATED_AT = 'updated_at';



    /**
     * 关联 User 模型.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class, 'user_id', 'id');
    }

}