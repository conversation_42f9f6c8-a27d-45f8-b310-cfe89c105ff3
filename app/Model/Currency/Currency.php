<?php

declare(strict_types=1);

namespace App\Model\Currency;

use App\QueryBuilder\Model;
use Carbon\Carbon;
use Hyperf\Database\Model\Relations\HasOne;

/**
 * @property int $id 
 * @property string $symbol 交易标的
 * @property string $base_asset 基础标的
 * @property int $base_assets_precision 基础资产显示精度
 * @property string $quote_asset 计价币种symbol
 * @property int $quote_assets_id 计价币种id
 * @property int $s_price_precision 现货交易资产价格精度
 * @property int $s_quantity_precision 现货交易数量精度
 * @property int $m_price_precision 合约价格显示精度
 * @property int $m_quantity_precision 合约交易数量精度
 * @property int $is_spotTrade 是否支持现货交易
 * @property int $is_marginTrade 是否支持合约交易
 * @property int $market_type 标的交易市场类型 : 1加密数字货币 2美股 3外汇 4期货
 * @property string $trading_start 交易时段开始时间：h:i | 0为不限制
 * @property string $trading_end 交易时段结束时间：h:i | 0为不限制
 * @property string $trading_timezone 交易时间时区:UTC
 * @property int $status 是否启用标的
 * @property Carbon $created_at 
 * @property Carbon $updated_at 
 * @property-read null|CurrencyMate $currencyMate 
 */
final class Currency extends Model
{
    /**
     * 计价币种id
     */
    public const FIELD_QUOTE_ASSETS_ID = 'quote_assets_id';
    /**
     * 现货交易资产价格精度
     */
    public const FIELD_S_PRICE_PRECISION = 's_price_precision';
    /**
     * 现货交易数量精度
     */
    public const FIELD_S_QUANTITY_PRECISION = 's_quantity_precision';
    /**
     * 合约价格显示精度
     */
    public const FIELD_M_PRICE_PRECISION = 'm_price_precision';
    /**
     * 合约交易数量精度
     */
    public const FIELD_M_QUANTITY_PRECISION = 'm_quantity_precision';
    /**
     * 标的交易精度
     */
    public const FIELD_BASE_QUOTE_PRECISION = 'base_quote_precision';
    /**
     * 
     */
    public const FIELD_ID = 'id';
    /**
     * 交易标的
     */
    public const FIELD_SYMBOL = 'symbol';
    /**
     * 基础标的
     */
    public const FIELD_BASE_ASSET = 'base_asset';
    /**
     * 基础标的资产精度
     */
    public const FIELD_BASE_ASSETS_PRECISION = 'base_assets_precision';
    /**
     * 交易资产
     */
    public const FIELD_QUOTE_ASSET = 'quote_asset';
    /**
     * 交易资产计算精度
     */
    public const FIELD_QUOTE_PRECISION = 'quote_precision';
    /**
     * 是否支持现货交易
     */
    public const FIELD_IS_SPOTTRADE = 'is_spotTrade';
    /**
     * 是否支持合约交易
     */
    public const FIELD_IS_MARGINTRADE = 'is_marginTrade';
    /**
     * 标的交易市场类型 : 1加密数字货币 2美股 3外汇 4期货
     */
    public const FIELD_MARKET_TYPE = 'market_type';
    /**
     * 交易时段开始时间：h:i | 0为不限制
     */
    public const FIELD_TRADING_START = 'trading_start';
    /**
     * 交易时段结束时间：h:i | 0为不限制
     */
    public const FIELD_TRADING_END = 'trading_end';
    /**
     * 交易时间时区:UTC
     */
    public const FIELD_TRADING_TIMEZONE = 'trading_timezone';
    /**
     * 是否启用标的
     */
    public const FIELD_STATUS = 'status';
    /**
     * 图标
     */
    public const FIELD_ICON = 'icon';
    /**
     * 创建时间
     */
    public const FIELD_CREATED_AT = 'created_at';
    /**
     * 更新时间
     */
    public const FIELD_UPDATED_AT = 'updated_at';
    /**
     * The table associated with the model.
     */
    protected ?string $table = 'currency';

    /**
     * The connection name for the model.
     */
    // protected ?string $connection = 'default';

    /**
     * The attributes that are mass assignable.
     */
    // protected array $fillable = [
    //     'name',
    //     'symbol',
    //     'icon',
    //     'sort',
    //     'decimals',
    // ];

    protected array $guarded = ['id', 'created_at', 'updated_at'];

    /**
     * The attributes that should be cast to native types.
     */
    protected array $casts = ['id' => 'integer', 'base_assets_precision' => 'integer', 'quote_assets_id' => 'integer', 's_price_precision' => 'integer', 's_quantity_precision' => 'integer', 'm_price_precision' => 'integer', 'm_quantity_precision' => 'integer', 'is_spotTrade' => 'integer', 'is_marginTrade' => 'integer', 'market_type' => 'integer', 'status' => 'integer', 'icon'=>'string', 'created_at' => 'datetime', 'updated_at' => 'datetime'];
    public function getId() : int
    {
        return $this->id;
    }
    public function setId($value) : object
    {
        $this->id = $value;
        return $this;
    }
    public function getSymbol() : string
    {
        return $this->symbol;
    }
    public function setSymbol($value) : object
    {
        $this->symbol = $value;
        return $this;
    }
    public function getBaseAsset() : string
    {
        return $this->base_asset;
    }
    public function setBaseAsset($value) : object
    {
        $this->base_asset = $value;
        return $this;
    }
    public function getBaseAssetsPrecision() : int
    {
        return $this->base_assets_precision;
    }
    public function setBaseAssetsPrecision($value) : object
    {
        $this->base_assets_precision = $value;
        return $this;
    }
    public function getQuoteAsset() : string
    {
        return $this->quote_asset;
    }
    public function setQuoteAsset($value) : object
    {
        $this->quote_asset = $value;
        return $this;
    }
    public function getQuotePrecision() : int
    {
        return $this->quote_precision;
    }
    public function setQuotePrecision($value) : object
    {
        $this->quote_precision = $value;
        return $this;
    }
    public function getIsSpotTrade() : int
    {
        return $this->is_spotTrade;
    }
    public function setIsSpotTrade($value) : object
    {
        $this->is_spotTrade = $value;
        return $this;
    }
    public function getIsMarginTrade() : int
    {
        return $this->is_marginTrade;
    }
    public function setIsMarginTrade($value) : object
    {
        $this->is_marginTrade = $value;
        return $this;
    }
    public function getMarketType() : int
    {
        return $this->market_type;
    }
    public function setMarketType($value) : object
    {
        $this->market_type = $value;
        return $this;
    }
    public function getTradingStart() : string
    {
        return $this->trading_start;
    }
    public function setTradingStart($value) : object
    {
        $this->trading_start = $value;
        return $this;
    }
    public function getTradingEnd() : string
    {
        return $this->trading_end;
    }
    public function setTradingEnd($value) : object
    {
        $this->trading_end = $value;
        return $this;
    }
    public function getTradingTimezone() : string
    {
        return $this->trading_timezone;
    }
    public function setTradingTimezone($value) : object
    {
        $this->trading_timezone = $value;
        return $this;
    }
    public function getStatus() : int
    {
        return $this->status;
    }
    public function setStatus($value) : object
    {
        $this->status = $value;
        return $this;
    }
    public function getCreatedAt() : mixed
    {
        return $this->created_at;
    }
    public function setCreatedAt($value) : static
    {
        $this->created_at = $value;
        return $this;
    }
    public function getUpdatedAt() : mixed
    {
        return $this->updated_at;
    }
    public function setUpdatedAt($value) : static
    {
        $this->updated_at = $value;
        return $this;
    }
    public function getBaseQuotePrecision() : int
    {
        return $this->base_quote_precision;
    }
    public function setBaseQuotePrecision($value) : object
    {
        $this->base_quote_precision = $value;
        return $this;
    }
    public function getSPricePrecision() : int
    {
        return $this->s_price_precision;
    }
    public function setSPricePrecision($value) : object
    {
        $this->s_price_precision = $value;
        return $this;
    }
    public function getSQuantityPrecision() : int
    {
        return $this->s_quantity_precision;
    }
    public function setSQuantityPrecision($value) : object
    {
        $this->s_quantity_precision = $value;
        return $this;
    }
    public function getMPricePrecision() : int
    {
        return $this->m_price_precision;
    }
    public function setMPricePrecision($value) : object
    {
        $this->m_price_precision = $value;
        return $this;
    }
    public function getMQuantityPrecision() : int
    {
        return $this->m_quantity_precision;
    }
    public function setMQuantityPrecision($value) : object
    {
        $this->m_quantity_precision = $value;
        return $this;
    }

    /**
     * 币种详情关联（包含logo等信息）
     * @return HasOne
     */
    public function currencyMate(): HasOne
    {
        return $this->hasOne(CurrencyMate::class, 'currency_id', 'id');
    }

    public function tradeConfig()
    {

    }

    /**
     * 获取币种logo
     * @return string|null
     */
    public function getLogo(): ?string
    {
        return $this->currencyMate?->getLogo();
    }

    /**
     * 获取币种描述信息
     * @return array|null
     */
    public function getDescription(): ?array
    {
        return $this->currencyMate?->getDescription();
    }

    /**
     * 获取币种详细信息
     * @return array|null
     */
    public function getMateInfo(): ?array
    {
        return $this->currencyMate?->getMateInfo();
    }

    public function getCateIds()
    {
        return $this->currencyMate?->getCateIds();
    }
    public function getQuoteAssetsId() : int
    {
        return $this->quote_assets_id;
    }
    public function setQuoteAssetsId($value) : object
    {
        $this->quote_assets_id = $value;
        return $this;
    }
}
