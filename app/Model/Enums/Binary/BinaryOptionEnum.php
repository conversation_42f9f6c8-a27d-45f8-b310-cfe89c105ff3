<?php

declare(strict_types=1);

namespace App\Model\Enums\Binary;

enum BinaryOptionEnum: int
{
    case UP = 1;    // 看涨
    case DOWN = 2;  // 看跌

    public function getLabel(): string
    {
        return match ($this) {
            self::UP => '看涨',
            self::DOWN => '看跌',
        };
    }
}

enum BinaryOptionStatus: int
{
    case PENDING = 1;    // 等待结算
    case WIN = 2;        // 盈利
    case LOSE = 3;       // 亏损
    case DRAW = 4;       // 平局
    case CANCELLED = 5;  // 已取消

    public function getLabel(): string
    {
        return match ($this) {
            self::PENDING => '等待结算',
            self::WIN => '盈利',
            self::LOSE => '亏损',
            self::DRAW => '平局',
            self::CANCELLED => '已取消',
        };
    }
}
