<?php

declare(strict_types=1);

namespace App\Model\Trade;

use App\Model\Currency\Currency;
use App\Model\Match\MatchOrder;
use Hyperf\DbConnection\Model\Model;

/**
 * @property int $id 
 * @property int $user_id 
 * @property int $currency_id 
 * @property int $direction 1买-1卖
 * @property int $order_type 订单类型 参考 OrderType
 * @property float $price 价格
 * @property float $amount 数量
 * @property int $bbo_level 限价订单的深度档位
 * @property float $charge 手续费
 * @property int $match_order 关联的撮合引擎订单表id
 * @property float $used_amount 已使用的冻结金额
 * @property float $frozen_amount 冻结资金记录
 * @property int $is_trader 是否交易员订单：0-不是，1-是
 * @property int|null $copy_order_id 跟单订单id
 * @property \Carbon\Carbon $created_at
 * @property \Carbon\Carbon $updated_at
 */
class TradeSpotOrder extends Model
{
    /**
     * 已使用的冻结金额
     */
    public const FIELD_USED_AMOUNT = 'used_amount';
    /**
     * 冻结资金记录
     */
    public const FIELD_FROZEN_AMOUNT = 'frozen_amount';
    /**
     * 是否交易员订单：0-不是，1-是
     */
    public const FIELD_IS_TRADER = 'is_trader';
    /**
     * 跟单订单id
     */
    public const FIELD_COPY_ORDER_ID = 'copy_order_id';
    /**
     *
     */
    public const FIELD_ID = 'id';
    /**
     * 
     */
    public const FIELD_USER_ID = 'user_id';
    /**
     * 
     */
    public const FIELD_CURRENCY_ID = 'currency_id';
    /**
     * 1买-1卖
     */
    public const FIELD_DIRECTION = 'direction';
    /**
     * 订单类型 参考 OrderType
     */
    public const FIELD_ORDER_TYPE = 'order_type';
    /**
     * 价格
     */
    public const FIELD_PRICE = 'price';
    /**
     * 数量
     */
    public const FIELD_AMOUNT = 'amount';
    /**
     * 限价订单的深度档位
     */
    public const FIELD_BBO_LEVEL = 'bbo_level';
    /**
     * 手续费
     */
    public const FIELD_CHARGE = 'charge';
    /**
     * 关联的撮合引擎订单表id
     */
    public const FIELD_MATCH_ORDER = 'match_order';
    /**
     * 创建时间
     */
    public const FIELD_CREATED_AT = 'created_at';
    /**
     * 更新时间
     */
    public const FIELD_UPDATED_AT = 'updated_at';
    /**
     * The table associated with the model.
     */
    protected ?string $table = 'trade_spot_order';

    /**
     * The attributes that are mass assignable.
     */
    protected array $fillable = ['id', 'user_id', 'currency_id', 'direction', 'order_type', 'price', 'amount', 'bbo_level', 'charge', 'match_order', 'used_amount', 'frozen_amount', 'is_trader', 'copy_order_id', 'created_at', 'updated_at'];

    /**
     * The attributes that should be cast to native types.
     */
    protected array $casts = [
        'id' => 'integer',
        'user_id' => 'integer',
        'currency_id' => 'integer',
        'direction' => 'integer',
        'order_type' => 'integer',
        'bbo_level' => 'integer',
        'match_order' => 'integer',
        'is_trader' => 'integer',
        'copy_order_id' => 'integer',
        'created_at' => 'datetime',
        'updated_at' => 'datetime'
    ];

    /**
     * 关联撮合引擎订单
     */
    public function matchOrder(): \Hyperf\Database\Model\Relations\BelongsTo
    {
        return $this->belongsTo(MatchOrder::class, 'match_order', 'id');
    }

    /**
     * 关联币种信息
     */
    public function currency(): \Hyperf\Database\Model\Relations\BelongsTo
    {
        return $this->belongsTo(Currency::class, 'currency_id', 'id');
    }
    public function getId() : int
    {
        return $this->id;
    }
    public function setId($value) : object
    {
        $this->id = $value;
        return $this;
    }
    public function getUserId() : int
    {
        return $this->user_id;
    }
    public function setUserId($value) : object
    {
        $this->user_id = $value;
        return $this;
    }
    public function getCurrencyId() : int
    {
        return $this->currency_id;
    }
    public function setCurrencyId($value) : object
    {
        $this->currency_id = $value;
        return $this;
    }
    public function getDirection() : int
    {
        return $this->direction;
    }
    public function setDirection($value) : object
    {
        $this->direction = $value;
        return $this;
    }
    public function getOrderType() : int
    {
        return $this->order_type;
    }
    public function setOrderType($value) : object
    {
        $this->order_type = $value;
        return $this;
    }
    public function getPrice() : float
    {
        return (float)$this->price;
    }
    public function setPrice($value) : object
    {
        $this->price = $value;
        return $this;
    }
    public function getAmount() : float
    {
        return (float)$this->amount;
    }
    public function setAmount($value) : object
    {
        $this->amount = $value;
        return $this;
    }
    public function getBboLevel() : int
    {
        return $this->bbo_level;
    }
    public function setBboLevel($value) : object
    {
        $this->bbo_level = $value;
        return $this;
    }
    public function getCharge() : float
    {
        return (float)$this->charge;
    }
    public function setCharge($value) : object
    {
        $this->charge = $value;
        return $this;
    }
    public function getMatchOrder() : int
    {
        return $this->match_order;
    }
    public function setMatchOrder($value) : object
    {
        $this->match_order = $value;
        return $this;
    }
    public function getCreatedAt() : mixed
    {
        return $this->created_at;
    }
    public function setCreatedAt($value) : static
    {
        $this->created_at = $value;
        return $this;
    }
    public function getUpdatedAt() : mixed
    {
        return $this->updated_at;
    }
    public function setUpdatedAt($value) : static
    {
        $this->updated_at = $value;
        return $this;
    }
    public function getFrozenAmount() : float
    {
        return (float)$this->frozen_amount;
    }
    public function setFrozenAmount($value) : object
    {
        $this->frozen_amount = $value;
        return $this;
    }
    public function getUsedAmount() : float
    {
        return (float)$this->used_amount;
    }
    public function setUsedAmount($value) : object
    {
        $this->used_amount = $value;
        return $this;
    }
    public function getIsTrader() : int
    {
        return $this->is_trader;
    }
    public function setIsTrader($value) : object
    {
        $this->is_trader = $value;
        return $this;
    }
    public function getCopyOrderId() : ?int
    {
        return $this->copy_order_id;
    }
    public function setCopyOrderId($value) : object
    {
        $this->copy_order_id = $value;
        return $this;
    }
}
