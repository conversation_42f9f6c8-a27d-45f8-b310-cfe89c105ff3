<?php

declare(strict_types=1);

/**
 * ContractMatchEngineProcess.php
 * 合约撮合引擎工作进程
 * Author:chenmaq (<EMAIL>)
 * Contact:tg:@chenmaq
 * Version:3.0
 * Date:2025/06/29
 * Website:algoquant.org
 */

namespace App\MarketData\Process\MatchEngine;

use App\Enum\MarketType;
use App\MarketData\Process\MatchEngine\Logger\MatchEngineLogger;
use App\MarketData\Service\MatchEngine\Contract\ContractCommandHandler;
use App\MarketData\Service\MatchEngine\Contract\ContractEventHandler;
use App\MarketData\Service\MatchEngine\Contract\ContractMarketMakerService;
use App\MarketData\Service\MatchEngine\Contract\ContractMatchEngineService;
use App\Process\MatchEngineBaseProcess;
use Hyperf\Di\Annotation\Inject;
use Hyperf\Process\Annotation\Process;
use Psr\Container\ContainerInterface;
use Swoole\Coroutine;

#[Process(name: "contract-match-engine")]
class ContractMatchEngineProcess extends MatchEngineBaseProcess
{
    public bool $enableCoroutine = true;

    /**
     * 是否正在运行
     */
    private bool $isRunning = true;

    /**
     * 合约撮合引擎服务
     */
    private ContractMatchEngineService $contractMatchEngineService;

    /**
     * 合约事件处理器
     */
    private ContractEventHandler $contractEventHandler;

    /**
     * 合约命令处理器
     */
    private ContractCommandHandler $contractCommandHandler;

    /**
     * 合约做市机器人服务
     */
    private array $contractMarketMakerService; //['symbol' => instance] 币种到实例的映射

    #[Inject]
    protected ContainerInterface $container;

    public function __construct(ContainerInterface $container)
    {
        // 初始化服务层组件
        $this->initializeServices();

        // 先初始化服务以加载币种数据
        $this->contractMatchEngineService->loadContractCurrenciesData();

        // 设置为合约市场类型
        $this->setMarketType(\App\Enum\MarketType::MARGIN->value); // 5

        // 传递币种数据给基类
        parent::__construct($container, $this->contractMatchEngineService->getAllCurrencies());
    }

    public function isEnable($server):bool
    {
        return (bool)env('MARKET_DATA_SERVER',false);
    }

    /**
     * 初始化服务层组件
     */
    private function initializeServices(): void
    {
        try {
            // 获取撮合引擎专用日志记录器
            $logger = $this->container->get(MatchEngineLogger::class);
            
            // 设置进程信息到日志器（这里先设置默认值，后面会更新）
            MatchEngineLogger::setProcessInfo(0, 'worker');
            
            // 初始化合约撮合引擎服务
            $this->contractMatchEngineService = new ContractMatchEngineService($logger);
            
            // 初始化合约事件处理器
            $this->contractEventHandler = new ContractEventHandler($logger);
            
            // 初始化合约命令处理器
            $this->contractCommandHandler = new ContractCommandHandler($logger, $this->contractMatchEngineService);
            
        } catch (\Throwable $e) {
            $this->logError("Failed to initialize contract services", $e);
            throw $e;
        }
    }

    public function handle(): void
    {
        if ($this->isManagerProcess()) {
            // 管理进程：基类已经处理了主循环，这里不需要额外操作
            return;
        } else {
            $this->runAsMatchEngineWorker();
        }
    }
    
    /**
     * 重写基类的工作进程处理方法
     */
    protected function handleWorkerProcess(): void
    {
        $this->runAsMatchEngineWorker();
    }

    /**
     * 作为撮合引擎工作进程运行
     */
    private function runAsMatchEngineWorker(): void
    {
        try {
            $processIndex = $this->getCurrentProcessIndex();
            $assignedCurrencies = $this->getAssignedCurrencies();

            // 设置进程索引到服务
            $this->contractMatchEngineService->setProcessIndex($processIndex);
            
            // 更新日志器的进程信息
            MatchEngineLogger::setProcessInfo($processIndex, 'worker');
            
            // 初始化撮合引擎服务
            if (!$this->contractMatchEngineService->initialize()) {
                throw new \RuntimeException("Failed to initialize contract match engine service");
            }

            // 创建市场
            $assignedCurrencyData = $this->getAssignedCurrencyData();
            $this->contractMatchEngineService->createMarkets($assignedCurrencyData);
            
            // 注册事件监听器
            $exchange = $this->contractMatchEngineService->getExchange();
            if ($exchange) {
                // 设置内部数据聚合服务到事件处理器
                $this->contractEventHandler->setCurrencyIdMap($assignedCurrencyData);
                $this->contractEventHandler->registerEventListeners($exchange);
            }
            
            // 启动命令处理协程
            if ($this->enableCoroutine) {
                $this->startCommandProcessing();
            }
            //var_dump($this->getCurrentProcessIndex(),array_keys($assignedCurrencyData));

            // 初始化合约做市机器人服务，每个币种至少一个做市服务实例
            foreach ($assignedCurrencyData as $key => $val){
                if(!isset($this->contractMarketMakerService[$key])){
                    $this->contractMarketMakerService[$key] = make(ContractMarketMakerService::class);
                    $this->contractMarketMakerService[$key]->initialize([
                        'ExternalIndexFollowStrategy' => [
                            'symbol' => $key,
                            'spread_percent' => 1,
                            'order_levels' => 20,
                            'base_precision' => $val['m_price_precision'],
                            'quote_precision' => $val['m_quantity_precision'],
                            'market_type' => MarketType::MARGIN->value
                        ],
                    ]);
                    $this->contractMarketMakerService[$key]->startEventLoop();

                    $this->logInfo("{$key}做市机器人启动成功");
                }
            }


            // 初始化做市机器人服务（可以传入策略配置）
//            if(is_array($assignedCurrencyData) && isset($assignedCurrencyData['BTCUSDT'])){
//                $strategiesConfig = [
//                    'ExternalIndexFollowStrategy' => [
//                        'symbol' => 'BTCUSDT',
//                        'spread_percent' => 1,
//                        'order_levels' => 20,
//                        'base_precision' => $assignedCurrencyData['BTCUSDT']['m_price_precision'],
//                        'quote_precision' => $assignedCurrencyData['BTCUSDT']['m_quantity_precision'],
//                        'market_type' => MarketType::MARGIN->value
//                    ],
//                ];
//                $this->contractMarketMakerService->initialize($strategiesConfig);
//                $this->contractMarketMakerService->startEventLoop();
//            }
            
            // 主循环
            while ($this->isRunning) {
                if ($this->enableCoroutine) {
                    Coroutine::sleep(1);
                }
            }

        } catch (\Throwable $e) {
            $this->logError("Failed to initialize contract match engine worker process", $e);
        }
    }

    /**
     * 启动命令处理协程（用于接收基类分发的命令）
     */
    private function startCommandProcessing(): void
    {
        go(function() {
            while ($this->isRunning) {
                try {
                    $this->processWorkerCommands();
                    Coroutine::sleep(0.01); // 10ms检查一次
                } catch (\Throwable $e) {
                    $this->logError("Contract command processing error", $e);
                    Coroutine::sleep(0.1);
                }
            }
        });
    }

    /**
     * 处理工作进程专用命令（基类会分发命令到这里）
     */
    private function processWorkerCommands(): void
    {
        // 获取基类的命令表
        $commandTable = $this->getCommandTable();
        if ($commandTable === null) {
            return;
        }
        
        $currentProcessIndex = $this->getCurrentProcessIndex();
        $processedCommands = [];
        
        // 处理命令
        foreach ($commandTable as $key => $command) {
            // 只处理目标是当前进程且未被处理的命令
            if ($command['processed'] == 0 && $command['target_process'] == $currentProcessIndex) {
                try {
                    // 直接处理命令
                    $this->handleWorkerCommand($command);
                    $processedCommands[] = $key;
                    
                } catch (\Throwable $e) {
                    $this->logError("Contract worker command processing error for command {$key}: " . $e->getMessage(), $e);
                    $processedCommands[] = $key; // 即使出错也要删除
                }
            }
        }
        
        // 删除已处理的命令
        foreach ($processedCommands as $key) {
            $commandTable->del($key);
        }
    }

    /**
     * 处理工作进程命令
     */
    private function handleWorkerCommand(array $command): void
    {
        $type = $command['type'];
        $data = json_decode($command['data'], true);
        
        // 委托给命令处理器
        $this->contractCommandHandler->handleCommand($type, $data);
    }

    /**
     * 重写基类的自定义命令处理
     */
    protected function handleCustomCommand(string $type, array $data): void
    {
        // 委托给命令处理器
        $this->contractCommandHandler->handleCommand($type, $data);
    }

    // ========== 公共API方法 ==========

    /**
     * 停止运行
     */
    public function stop(): void
    {
        $this->isRunning = false;
        
        // 清理做市机器人服务
        if ($this->contractMarketMakerService) {
            $this->contractMarketMakerService->cleanup();
        }
        
        // 清理撮合引擎资源
        if ($this->contractMatchEngineService) {
            $this->contractMatchEngineService->cleanup();
        }
        
        $this->logInfo("Contract match engine worker process {$this->getCurrentProcessIndex()} stopped");
    }

    /**
     * 获取市场信息
     */
    public function getMarketsInfo(): array
    {
        if ($this->contractMatchEngineService) {
            return $this->contractMatchEngineService->getMarketsInfo();
        }
        return [];
    }

    /**
     * 获取做市机器人服务
     */
    public function getMarketMakerService(): ContractMarketMakerService
    {
        return $this->contractMarketMakerService;
    }
} 