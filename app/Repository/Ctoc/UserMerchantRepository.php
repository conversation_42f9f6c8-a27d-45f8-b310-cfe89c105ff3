<?php

declare(strict_types=1);
/**
 * This file is part of MineAdmin.
 *
 * @link     https://www.mineadmin.com
 * @document https://doc.mineadmin.com
 * @contact  <EMAIL>
 * @license  https://github.com/mineadmin/MineAdmin/blob/master/LICENSE
 */

namespace App\Repository\Ctoc;

use App\Model\Ctoc\UserMerchant as Model;
use App\Repository\IRepository;
use Hyperf\Collection\Arr;
use Hyperf\Database\Model\Builder;

class UserMerchantRepository extends IRepository
{
    public function __construct(protected readonly Model $model) {}

    public function handleSearch(Builder $query, array $params): Builder
    {
        if (Arr::has($params, 'user_id')) {
            if (\is_array($params['user_id'])) {
                $query->whereIn('user_id', $params['user_id']);
            } else {
                $query->where('user_id', $params['user_id']);
            }
        }
        if (Arr::has($params, 'user_name')) {
            $query->where('user_name', 'like', '%' . $params['user_name'] . '%');
        }
        if (Arr::has($params, 'id_card')) {
            $query->where('id_card', 'like', '%' . $params['id_card'] . '%');
        }
        if (Arr::has($params, 'id_card_front')) {
            $query->where('id_card_front', 'like', '%' . $params['id_card_front'] . '%');
        }
        if (Arr::has($params, 'id_card_back')) {
            $query->where('id_card_back', 'like', '%' . $params['id_card_back'] . '%');
        }
        if (Arr::has($params, 'store_name')) {
            $query->where('store_name', 'like', '%' . $params['store_name'] . '%');
        }
        if (Arr::has($params, 'store_logo')) {
            $query->where('store_logo', 'like', '%' . $params['store_logo'] . '%');
        }
        if (Arr::has($params, 'contact_name')) {
            $query->where('contact_name', 'like', '%' . $params['contact_name'] . '%');
        }
        if (Arr::has($params, 'contact_phone')) {
            $query->where('contact_phone', 'like', '%' . $params['contact_phone'] . '%');
        }
        if (Arr::has($params, 'contact_email')) {
            $query->where('contact_email', 'like', '%' . $params['contact_email'] . '%');
        }
        if (Arr::has($params, 'status')) {
            if (\is_array($params['status'])) {
                $query->whereIn('status', $params['status']);
            } else {
                $query->where('status', $params['status']);
            }
        }
        if (Arr::has($params, 'remark')) {
            $query->where('remark', 'like', '%' . $params['remark'] . '%');
        }
        if (Arr::has($params, 'created_by')) {
            if (\is_array($params['created_by'])) {
                $query->whereIn('created_by', $params['created_by']);
            } else {
                $query->where('created_by', $params['created_by']);
            }
        }
        if (Arr::has($params, 'updated_by')) {
            if (\is_array($params['updated_by'])) {
                $query->whereIn('updated_by', $params['updated_by']);
            } else {
                $query->where('updated_by', $params['updated_by']);
            }
        }
        if (Arr::has($params, 'created_at')) {
            if (\is_array($params['created_at'])) {
                $query->whereBetween('created_at', $params['created_at']);
            } else {
                $query->where('created_at', $params['created_at']);
            }
        }
        return $query;
    }
}